package com.twl.meeboss.chat.module.boss.viewmodel

import androidx.annotation.StringRes
import androidx.compose.foundation.lazy.LazyListState
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.hasNotificationPermission
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.export.BossServiceRouter
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.api.resp.toTopHighlyMatchedModel
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.export.model.SimpleJob
import com.twl.meeboss.chat.module.notification.manager.SystemNotificationManager
import com.twl.meeboss.chat.repos.ChatBossRepository
import com.twl.meeboss.chat.repos.ChatConversationRepository
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.preference.SpKey
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import javax.inject.Inject

private typealias JobWithFilter = Pair<String?, Filter>

/**
 * Tab 数据模型
 */
data class ConversationTab(
    val title: String,
    val salary: String? = null,
    val jobId: String? = null,
    @JobStatus val jobStatus: Int = 0,
    val statusReason: String? = null,
    val deleted: Boolean = false,
)

/**
 * 会话过滤器类型
 * 定义了不同的会话筛选维度，每种过滤器对应不同的业务场景
 */
enum class Filter(
    @StringRes val titleRes: Int,
    @StringRes val emptyHintRes: Int,
) {
    /** 全部会话：显示所有可见的会话记录 */
    All(
        titleRes = R.string.messages_employer_second_nav_value1,
        emptyHintRes = R.string.chat_no_message_hint_in_all
    ),
    /** 新打招呼：显示刚开始沟通的会话 */
    New(
        titleRes = R.string.messages_employer_second_nav_value2,
        emptyHintRes = R.string.chat_no_message_hint_in_new_greeting_for_boss
    ),
    /** 沟通中：显示双方互聊过的会话 */
    InProgress(
        titleRes = R.string.messages_employer_second_nav_value3,
        emptyHintRes = R.string.chat_no_message_hint_in_communication
    ),
    /** 已收藏：显示用户手动标记为重要的会话 */
    Star(
        titleRes = R.string.messages_employer_second_nav_value4,
        emptyHintRes = R.string.messages_employer_starred_empty_list
    ),
    /** 高匹配度：显示系统算法识别的高匹配候选人会话，只有存在高匹配会话时才显示此过滤器 */
    TopMatches(
        titleRes = R.string.messages_list_filter_top_matches,
        emptyHintRes = R.string.messages_list_filter_top_matches_empty
    )
}

private const val SP_KEY_CACHED_JOBS = "SP_KEY_CACHED_JOBS"

/**
 * Boss聊天页面的ViewModel
 *
 * 核心功能：
 * 1. 管理职位Tab切换和会话数据流
 * 2. 处理动态过滤器（TopMatches过滤器根据实际数据显示/隐藏）
 * 3. 缓存分页数据和滚动状态，提升用户体验
 * 4. 响应式监听数据变化，实时更新UI状态
 */
@HiltViewModel
class ConversationPageViewModel @Inject constructor(
    private val repos: ChatConversationRepository,
    private val chatBossRepo: ChatBossRepository,
) : BaseMviViewModel<ConversationPageUiState, ConversationPageUiIntent>() {

    /**
     * 分页数据流缓存
     * Key: (jobId, filter) 组合，Value: 对应的分页数据流
     * 避免重复创建相同的数据流，提升性能并保持数据一致性
     */
    private val chatFlowCache = mutableMapOf<JobWithFilter, Flow<PagingData<Conversation>>>()

    /**
     * 列表滚动状态缓存
     * Key: (jobId, filter) 组合，Value: 对应的列表滚动状态
     * 用户切换Tab或过滤器后再回来时，保持之前的滚动位置
     */
    private val listStates = mutableMapOf<JobWithFilter, LazyListState>()

    /**
     * FilterBar滚动状态缓存
     * Key: jobId，Value: 该职位对应的FilterBar滚动状态
     * 每个职位的过滤器选择状态独立保存，切换职位时保持FilterBar位置
     */
    private val filterBarStates = mutableMapOf<String?, LazyListState>()

    // Moshi 实例
    private val moshi: Moshi = GsonUtils.getMoshi()
    private val jobsListType = Types.newParameterizedType(List::class.java, SimpleJob::class.java)
    private val jobsAdapter = moshi.adapter<List<SimpleJob>>(jobsListType)

    private var refreshJobs: Boolean = true

    /**
     * 职位信息缓存
     * 从SharedPreferences加载职位数据，保证数据尽快上屏
     * 数据更新时自动保存到本地，保证离线时也能展示基本信息
     */
    private var cachedJobs: List<SimpleJob> = run {
        val cachedJobsJson = SpManager.getUserString(SP_KEY_CACHED_JOBS, "")
        if (cachedJobsJson.isNullOrBlank()) {
            emptyList()
        } else {
            try {
                jobsAdapter.fromJson(cachedJobsJson) ?: emptyList()
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
        set(value) {
            field = value
            saveCachedJobsToSp(value)
        }

    val notificationCount = SystemNotificationManager.getNotificationCount()

    init {
        XLog.info(TAG, "Initializing ConversationPageViewModel")
        // 初始化通知权限状态检查
        checkNotificationPermission()
        // 启动职位Tab动态管理：监听有会话的职位变化，自动更新Tab列表
        observeJobsWithConversations()
        // 启动TopMatches过滤器动态管理：根据实际数据决定是否显示该过滤器
        observeTopMatchedConversations()
    }

    override fun onResume(owner: LifecycleOwner) {
        uiStateFlow.value.run {
            if (currentJobId == null && jobSelectedFilter.getOrDefault(null, Filter.All) == Filter.All) {
                getTopMatchesShowInfo()
            }
        }
    }

    /**
     * 将职位数据保存到 SharedPreferences
     */
    private fun saveCachedJobsToSp(jobs: List<SimpleJob>) {
        viewModelScope.launch {
            try {
                XLog.debug(TAG, "Saving ${jobs.size} jobs to SharedPreferences")
                val jobsJson = jobsAdapter.toJson(jobs)
                SpManager.putUserString(SP_KEY_CACHED_JOBS, jobsJson)
                XLog.debug(TAG, "Successfully saved jobs to SharedPreferences")
            } catch (e: Exception) {
                XLog.error(TAG, "Failed to save jobs to SharedPreferences", e)
            }
        }
    }

    /**
     * 检查通知权限状态并更新UI
     * 如果没有通知权限且今天未关闭过提示，则显示通知权限引导
     */
    private fun checkNotificationPermission() {
        val hasPermission = hasNotificationPermission()
        val todayDateStr = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE)
        val lastCloseDateStr = SpManager.getUserString(SpKey.KEY_CHAT_TAB_NOTIFICATION_CLOSE_DATE, null)

        val shouldShowGuide = !hasPermission && lastCloseDateStr != todayDateStr
        XLog.debug(TAG, "Notification permission check - hasPermission: $hasPermission, shouldShowGuide: $shouldShowGuide")

        sendUiState {
            copy(showNotificationGuide = shouldShowGuide)
        }
    }

    /**
     * 获取或创建会话的数据流
     * @param jobId 职位ID，null表示所有职位
     * @return 会话分页数据流
     */
    private fun getOrCreateFlow(jobId: String?, filter: Filter): Flow<PagingData<Conversation>> {
        XLog.info(TAG, "Getting conversations flow for jobId: $jobId with filter: $filter")
        return chatFlowCache.getOrPut(jobId to filter) {
            XLog.debug(TAG, "Creating new conversations flow for jobId: $jobId with filter: $filter")
            when (filter) {
                Filter.All -> repos.getAllConversations(jobId).cachedIn(viewModelScope)
                Filter.New -> repos.getNewGreetingConversations(jobId).cachedIn(viewModelScope)
                Filter.InProgress -> repos.getInCommunicationConversations(jobId).cachedIn(viewModelScope)
                Filter.Star -> repos.getStarredConversations(jobId).cachedIn(viewModelScope)
                Filter.TopMatches -> repos.getTopMatchedConversations(jobId).cachedIn(viewModelScope)
            }
        }
    }

    /**
     * 初始化UI状态
     * 预加载默认Tab的数据并返回初始状态
     */
    override fun initUiState(): ConversationPageUiState {
        XLog.info(TAG, "Initializing UI state")
        // 预先加载默认 Tab 的数据
        Filter.entries.forEach {
            getOrCreateFlow(DefaultTab.jobId, it)
        }

        return ConversationPageUiState(
            tabs = listOf(DefaultTab),
            currentJobId = DefaultTab.jobId
        )
    }

    /**
     * 监听有会话的职位ID变化，加载职位信息并更新 Tab
     * 当职位ID列表变化时自动更新UI
     */
    private fun observeJobsWithConversations() {
        XLog.info(TAG, "Starting to observe job IDs with conversations")
        viewModelScope.launch {
            repos.observeJobIdsWithConversations()
                .collect { jobIdsWithConversations ->
                    XLog.debug(TAG, "Received jobIdsWithConversations: $jobIdsWithConversations")

                    // 使用缓存的职位信息更新 Tab
                    updateTabsWithExistingJobs(jobIdsWithConversations)

                    // 如果缓存为空或存在新的job ID，则从服务器加载
                    val cachedJobIds = cachedJobs.map { it.jobId }.toHashSet()
                    // 如果存在会话数据中的 jobId 不在缓存的职位列表中，或者初次加载
                    if (refreshJobs || jobIdsWithConversations.any { it.isNotBlank() && it !in cachedJobIds }) {
                        XLog.debug(TAG, "Need to load jobs from server")
                        loadJobsAndUpdateTabs(jobIdsWithConversations)
                    }
                }
        }
    }

    /**
     * 加载职位信息并更新 Tab
     * 当发现新的jobId时调用，从服务器获取最新职位信息
     *
     * @param jobIdsWithConversations 有会话数据的职位ID集合
     */
    private fun loadJobsAndUpdateTabs(jobIdsWithConversations: Set<String>, checkDiff: Boolean = false) {
        XLog.info(TAG, "Loading jobs and updating tabs for jobIds: $jobIdsWithConversations")

        viewModelScope.launch {
            requestData(
                request = {
                    XLog.debug(TAG, "Requesting simple jobs from server")
                    chatBossRepo.getSimpleJobs()
                },
                success = { response ->
                    if (response == null) {
                        XLog.info(TAG, "Received null jobs response from server")
                        return@requestData
                    }

                    XLog.debug(TAG, "Received ${response.size} jobs from server")
                    // 服务端返回的既是有序数据
                    // 更新缓存
                    cachedJobs = response
                    refreshJobs = false
                    // 更新UI
                    updateTabsWithExistingJobs(jobIdsWithConversations, checkDiff)
                },
                fail = {
                    XLog.error(TAG, "Failed to load job tabs: ${it.message}", it)
                }
            )
        }
    }

    /**
     * 使用现有的职位信息更新 Tab
     * 根据职位信息和会话数据创建Tab列表
     *
     * @param jobIdsWithConversations 有会话数据的职位ID集合
     */
    private fun updateTabsWithExistingJobs(jobIdsWithConversations: Set<String>, checkDiff: Boolean = false) {
        XLog.debug(TAG, "Updating tabs with existing jobs for jobIds: $jobIdsWithConversations")
        // 创建 Tab 列表
        val tabs = mutableListOf<ConversationTab>()

        // 添加"全部"选项
        tabs.add(DefaultTab)

        // 添加有会话的职位选项
        cachedJobs.forEach { job ->
            // 只有在有会话的 jobId 列表中的职位才添加到 Tab
            if (job.jobId in jobIdsWithConversations) {
                XLog.info(TAG, "Adding tab for job: ${job.jobId} - ${job.jobTitle}")
                tabs.add(
                    ConversationTab(
                        title = job.jobTitle,
                        salary = job.salaryDesc,
                        jobId = job.jobId,
                        jobStatus = job.jobStatus,
                        statusReason = job.rejectReason,
                        deleted = job.delStatus == 1
                    )
                )
            }
        }

        // 记住当前选中的 jobId
        val currentJobId = uiStateFlow.value.currentJobId
        XLog.debug(TAG, "Current selected jobId: $currentJobId")

        // 如果当前选中的 jobId 不在新的 tabs 中，需要切换到默认 Tab
        val needSwitchToDefault = tabs.none { it.jobId == currentJobId }

        if (needSwitchToDefault) {
            XLog.debug(TAG, "Current jobId not in new tabs, switching to default tab")
        }

        // 更新 UI 状态
        sendUiState {
            val newState = copy(tabs = tabs, jobs = cachedJobs.associate { it.jobId to it.jobTitle })

            // 如果需要切换到默认 Tab
            if (needSwitchToDefault) {
                newState.copy(currentJobId = DefaultTab.jobId)
            } else if (checkDiff && this.tabs.associate { it.jobId to it.jobStatus } != tabs.associate { it.jobId to it.jobStatus }) {
                newState.copy(currentJobId = DefaultTab.jobId)
            } else {
                newState
            }
        }
        XLog.debug(TAG, "Updated UI state with ${tabs.size} tabs")
    }

    private fun updateJobStatus(jobId: String, jobStatus: Int) {
        requestData(
            request = {
                BossServiceRouter.updateBossJobStatus(jobId, jobStatus)
            },
            success = {
                T.ss(R.string.job_opened_successfully)
                loadJobsAndUpdateTabs(
                    jobIdsWithConversations = cachedJobs.map { it.jobId }.toSet(),
                    checkDiff = true
                )
            },
            fail = {
                T.ss(it.message)
            }
        )
    }

    /**
     * 处理UI事件
     * 根据不同的事件类型执行相应的操作
     */
    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is ConversationPageUiIntent.SetCurrentJob -> {
                val jobId = intent.jobId
                XLog.info(TAG, "Handling SetCurrentJob intent with jobId: $jobId")

                // 预先创建缓存，但不在 UiState 中保存映射
                Filter.entries.forEach {
                    getOrCreateFlow(jobId, it)
                }

                sendUiState {
                    if (jobId == null && jobSelectedFilter.getOrDefault(null, Filter.All) == Filter.All) {
                        getTopMatchesShowInfo()
                    }

                    copy(currentJobId = jobId)
                }
            }

            is ConversationPageUiIntent.UpdateFilterState -> {
                val jobId = intent.jobId
                val filter = intent.filter
                XLog.info(TAG, "Handling UpdateFilterState intent for jobId: $jobId, filter: $filter")

                if (jobId == null && filter == Filter.All) {
                    getTopMatchesShowInfo()
                }

                // 更新过滤状态
                sendUiState {
                    val updatedFilterStates = jobSelectedFilter.toMutableMap()
                    updatedFilterStates[jobId] = filter

                    copy(jobSelectedFilter = updatedFilterStates)
                }
            }

            is ConversationPageUiIntent.ToggleStar -> {
                startCandidate(intent.securityId, intent.friendId, intent.starred)
            }

            is ConversationPageUiIntent.DeleteConversation -> {
                deleteConversation(intent.friendId, intent.friendIdentity)
            }

            is ConversationPageUiIntent.OpenJob -> {
                updateJobStatus(intent.jobId, JobStatus.OPENING)
            }

            is ConversationPageUiIntent.RefreshJobTabs -> {
                XLog.info(TAG, "Handling RefreshJobTabs intent")
                loadJobsAndUpdateTabs(
                    jobIdsWithConversations = cachedJobs.map { it.jobId }.toSet(),
                    checkDiff = true
                )
            }

            is ConversationPageUiIntent.DismissNotificationGuide -> {
                XLog.info(TAG, "Handling DismissNotificationGuide intent")
                // 保存关闭日期
                val todayDateStr = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE)
                SpManager.putUserString(
                    SpKey.KEY_CHAT_TAB_NOTIFICATION_CLOSE_DATE,
                    todayDateStr
                )
                XLog.debug(TAG, "Saved notification guide close date: $todayDateStr")

                // 更新 UI 状态
                sendUiState {
                    copy(showNotificationGuide = false)
                }
            }

            is ConversationPageUiIntent.NotificationPermissionGranted -> {
                XLog.info(TAG, "Handling NotificationPermissionGranted intent")
                sendUiState {
                    copy(showNotificationGuide = false)
                }
            }

            is ConversationPageUiIntent.RequestTopMatches -> getTopMatchesShowInfo()

            else -> {
                XLog.error(TAG, "Received unknown intent: $intent")
            }
        }
    }

    /**
     * 给用户加星标
     * 请求更新星标状态，成功后主动更新联系人的会话属性
     */
    private fun startCandidate(securityId: String, friendId: String, starred: Boolean) {
        requestData(
            request = {
                showLoadingDialog()
                BossServiceRouter.starCandidate(securityId, starred).also {
                    dismissLoadingDialog()
                }
            },
            success = {
                FacadeManager.getInstance().conversationFacade.starContact(friendId, starred)
                T.ss(if (starred) R.string.star_action_confirmation_message else R.string.unstar_action_confirmation_message)
            },
            fail = {
                T.ss(it.message)
            }
        )
    }

    /**
     * 删除会话
     * 删除服务器和本地的会话记录及消息
     *
     * @param friendId 需要删除的会话对应的好友ID
     */
    private fun deleteConversation(friendId: String, friendIdentity: Int) {
        XLog.info(TAG, "Deleting conversation for friendId: $friendId")
        requestData(
            request = {
                XLog.debug(TAG, "Requesting conversation deletion from server for friendId: $friendId")
                showLoadingDialog()
                chatBossRepo.deleteConversation(friendId, friendIdentity).also {
                    dismissLoadingDialog()
                }
            },
            success = {
                XLog.debug(TAG, "Successfully deleted conversation on server, cleaning local data")
                // 删除联系人会话
                FacadeManager.getInstance().conversationFacade.deleteConversation(friendId)
                // 删除所有消息
                FacadeManager.getInstance().messageFacade.deleteAllMessages(friendId)
                XLog.info(TAG, "Conversation deleted successfully for friendId: $friendId")
            },
            fail = {
                T.ss(it.message)
                XLog.error(TAG, "Failed to delete conversation for friendId: $friendId", it)
            }
        )
    }

    /**
     * ViewModel销毁时清理资源
     */
    override fun onCleared() {
        XLog.info(TAG, "ViewModel is being cleared, releasing resources")
        super.onCleared()
        chatFlowCache.clear()
        listStates.clear()
        filterBarStates.clear()
    }

    /**
     * 获取会话的数据流
     * 对外暴露的方法，供UI组件调用
     *
     * @param jobId 职位ID，null表示所有职位
     * @return 会话分页数据流
     */
    fun getListFlow(jobId: String?, filter: Filter): Flow<PagingData<Conversation>> {
        return getOrCreateFlow(jobId, filter)
    }

    fun getListState(jobId: String?, filter: Filter): LazyListState {
        return listStates.getOrPut(jobId to filter) {
            LazyListState()
        }
    }

    /**
     * 获取指定 jobId 的 FilterBar 滚动状态
     * 为每个 Job 缓存独立的 FilterBar 滚动位置
     */
    fun getFilterBarState(jobId: String?): LazyListState {
        return filterBarStates.getOrPut(jobId) {
            LazyListState()
        }
    }

    /**
     * 根据业务数据动态生成可用的过滤器列表
     *
     * 业务规则：
     * - All、New、InProgress、Star 为固定过滤器，始终显示
     * - TopMatches 为动态过滤器，只有在存在高匹配会话时才显示
     * - 过滤器顺序：All -> TopMatches(条件显示) -> New -> InProgress -> Star
     *
     * @param hasTopMatchedChats 是否存在高匹配会话数据
     * @return 当前可用的过滤器列表
     */
    private fun generateAvailableFilters(hasTopMatchedChats: Boolean): List<Filter> {
        return buildList {
            add(Filter.All)
            // 动态过滤器：只有真正存在高匹配数据时才添加
            if (hasTopMatchedChats) {
                add(Filter.TopMatches)
            }
            add(Filter.New)
            add(Filter.InProgress)
            add(Filter.Star)
        }
    }

    /**
     * 监听高匹配会话状态变化
     * 用于控制过滤器是否显示
     *
     * 业务场景：
     * - TopMatches过滤器只有在真正存在高匹配会话时才显示
     * - 当数据发生变化时，过滤器会自动显示或隐藏
     *
     * 技术实现：
     * - 使用响应式流监听TopMatches状态，通过Room Flow实时响应数据库变化
     */
    private fun observeTopMatchedConversations() {
        XLog.info(TAG, "Starting to observe top matched conversations status")
        viewModelScope.launch {
            repos.hasTopMatchedConversations()
                .collect { hasTopMatched ->
                    XLog.debug(TAG, "Top matched conversations status changed: $hasTopMatched")
                    updateFiltersAndStates(hasTopMatched)
                }
        }
    }

    /**
     * 更新过滤器列表和相关状态
     * 统一处理 filters、filterStates 和 filterBarStates 的更新逻辑
     * 处理TopMatches状态变化的核心业务逻辑
     *
     * 当TopMatches数据发生变化时：
     * 1. 处理过滤器选择冲突（如果当前选中TopMatches已清空，则回退到All）
     * 2. 智能更新FilterBar滚动位置（保持用户体验的连续性）
     *
     * @param hasTopMatchedChats 是否存在高匹配会话
     */
    private fun updateFiltersAndStates(hasTopMatchedChats: Boolean) {
        val currentState = uiStateFlow.value
        val oldFilters = currentState.filters
        val newFilters = generateAvailableFilters(hasTopMatchedChats)

        // 避免无效更新，减少不必要的UI刷新
        if (oldFilters == newFilters) {
            XLog.info(TAG, "Filters not changed: $oldFilters")
            return
        }

        sendUiState {
            val updatedFilterSelection = currentState.jobSelectedFilter.toMutableMap()

            // 业务规则：如果TopMatches过滤器被移除，且用户当前选中的是TopMatches，则自动回退到All
            if (!hasTopMatchedChats) {
                currentState.jobSelectedFilter.forEach { (jobId, filter) ->
                    if (filter == Filter.TopMatches) {
                        updatedFilterSelection[jobId] = Filter.All
                        XLog.debug(TAG, "Reset filter to All for jobId: $jobId due to TopMatches removal")
                    }
                }
            }

            // FilterBar滚动位置智能调整
            // 记录需要更新滚动位置的 Job（避免重复更新）
            val jobsNeedScrollUpdate = mutableSetOf<String?>()

            // 检查 Job 的选中索引是否发生变化
            currentState.tabs.forEach { tab ->
                val jobId = tab.jobId
                val originalFilter = currentState.jobSelectedFilter[jobId] ?: Filter.All
                val currentFilter = updatedFilterSelection[jobId] ?: Filter.All

                if (originalFilter == currentFilter) {
                    // 过滤器没有重置，但在新的过滤器列表中选中位置发生变化，也更新滚动位置
                    val oldIndex = oldFilters.indexOf(currentFilter)
                    val newIndex = newFilters.indexOf(currentFilter)

                    if (oldIndex != newIndex && newIndex >= 0) {
                        jobsNeedScrollUpdate.add(jobId)
                        XLog.debug(TAG, "Filter index changed for jobId: $jobId, filter: $currentFilter, oldIndex: $oldIndex -> newIndex: $newIndex")
                    }
                } else {
                    // 过滤器被重置，需要更新滚动位置到新的选中项
                    jobsNeedScrollUpdate.add(jobId)
                }
            }

            // 批量更新需要滚动调整的 Job
            jobsNeedScrollUpdate.forEach { jobId ->
                val selectedFilter = updatedFilterSelection[jobId] ?: Filter.All
                updateFilterBarScrollForJob(jobId, newFilters, selectedFilter)
            }

            copy(
                filters = newFilters,
                jobSelectedFilter = updatedFilterSelection
            )
        }
    }

    /**
     * 当 Filter 列表变更时，更新 FilterBar 的滚动状态以确保选中项可见
     */
    private fun updateFilterBarScrollForJob(jobId: String?, filters: List<Filter>, selectedFilter: Filter) {
        val selectedIndex = filters.indexOf(selectedFilter)
        if (selectedIndex >= 0) {
            filterBarStates[jobId] = LazyListState(selectedIndex)
        }
    }

    private fun getTopMatchesShowInfo() {
        launcherOnIO {
            repos.getTopMatchesShowInfo().getOrNull()?.let {
                sendUiState { copy(topMatchesShowInfo = it.toTopHighlyMatchedModel()) }
            }
        }
    }
}

/**
 * Boss聊天页面的UI状态数据类
 *
 * 设计理念：
 * - 使用不可变数据类保证状态一致性
 * - TopMatches过滤器采用动态显示策略，根据实际数据决定是否可见
 */
data class ConversationPageUiState(
    /** 职位Tab列表，包含"全部"和各个具体职位 */
    val tabs: List<ConversationTab> = listOf(),
    /** 当前选中的职位ID，null表示"全部" */
    val currentJobId: String? = null,
    /** 职位ID到职位名称的映射，用于快速查找职位 */
    val jobs: Map<String, String> = emptyMap(),
    /**
     * 可用的过滤器列表
     * 根据业务状态，动态控制TopMatches过滤器的显示/隐藏
     */
    val filters: List<Filter> = listOf(Filter.All, Filter.New, Filter.InProgress, Filter.Star),
    /**
     * 每个职位的过滤器选择状态
     * Key: jobId（null表示全部职位）
     * Value: 当前选中的过滤器类型
     * 支持用户在不同职位间切换时保持各自的过滤选择
     */
    val jobSelectedFilter: Map<String?, Filter> = mapOf(),
    /** 是否显示通知权限引导弹窗 */
    val showNotificationGuide: Boolean = false,
    /** TopMatches功能的引导信息 */
    val topMatchesShowInfo: TopHighlyMatchedModel? = null,
) : IUiState

val DefaultTab = ConversationTab(
    title = R.string.Employer_message_job_filter_all_positions.toResourceString()
)

sealed class ConversationPageUiIntent : IUiIntent {
    data class SetCurrentJob(val jobId: String?) : ConversationPageUiIntent()
    data class UpdateFilterState(val jobId: String?, val filter: Filter) : ConversationPageUiIntent()
    data class OpenJob(val jobId: String) : ConversationPageUiIntent()

    data class ToggleStar(val securityId: String, val friendId: String, val starred: Boolean) : ConversationPageUiIntent()
    data class DeleteConversation(val friendId: String, val friendIdentity: Int) : ConversationPageUiIntent()

    data object RefreshJobTabs : ConversationPageUiIntent()

    // 添加关闭通知引导的 Intent
    data object DismissNotificationGuide : ConversationPageUiIntent()

    // 添加请求通知权限成功的 Intent
    data object NotificationPermissionGranted : ConversationPageUiIntent()

    data object RequestTopMatches : ConversationPageUiIntent()
}
