package com.twl.meeboss.chat.core.paas

import androidx.annotation.Keep
import com.bzl.im.message.attachment.BIMsgAttachment
import com.bzl.im.utils.toJsonString
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.GsonUtils

class MeeBossBizAttachment() : BIMsgAttachment {

    private var mAttachData = MeeBossBizBean()

    fun getData() = mAttachData

    override fun fromJson(json: String) {
        try {
            GsonUtils.fromJson(json, MeeBossBizBean::class.java)?.let {
                mAttachData = it
            }
        } catch (e: Exception) {
            XLog.error("MeeBossBizAttachment", "fromJson error : ${e.message}")
        }
    }

    override fun toJson(): String {
        return mAttachData.toJsonString()
    }
}


@Keep
data class MeeBossBizBean(
    val countable: Boolean = true,
    val deleted: Boolean = false,
    val failCode: Int = 0,
    val status: Int = MessageStatus.SEND,
    val visible: Boolean = true,
)