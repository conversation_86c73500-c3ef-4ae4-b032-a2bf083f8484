package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.text.SingleLineInfoLayout
import com.twl.meeboss.base.ktx.buildFullName
import com.twl.meeboss.base.ktx.getJoinString
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.bean.ChatResumeBean
import com.twl.meeboss.chat.core.model.message.custom.ChatGreetingResumeMessage
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA

@Composable
fun GreetingResumeCardBody(modifier: Modifier = Modifier,message: ChatGreetingResumeMessage) {
        val context = LocalContext.current
        Column(modifier = modifier) {
            Text(
                text = buildFullName(message.resumeBean?.firstName, message.resumeBean?.lastName),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = TextStyle(
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222,
                )
            )

            listOf(
                message.resumeBean?.eduLevel?.name.notNull(),
                message.resumeBean?.locationDesc.notNull()
            ).filter { it.isNotBlank() }.let {
                if (it.isNotEmpty()) {
                    SingleLineInfoLayout(modifier = Modifier.padding(top = 10.dp), list = it)
                }
            }
            message.resumeBean?.run {
                if(!this.workExpList.isNullOrEmpty()){
                    ResumeCardSummaryArea(item = this)
                }else{
                    CandidateCardDescArea(item = this)
                }
            }
        }
}

@Composable
fun ResumeCardSummaryArea(item: ChatResumeBean) {
    val textHeightState = remember {
        mutableIntStateOf(0)
    }
    item.workExpList?.map { it }?.let { list ->
        getJoinString(
            listOf(list[0].jobTitle, list[0].companyName),
            "  ·  "
        )?.let {
            Row(
                modifier = Modifier.padding(top = 16.dp)
            ) {
                Icon(
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .widthIn(min = 18.dp)
                        .drawBehind {
                            if (list.size >= 2) {
                                drawLine(
                                    color = GRAY_AAAAAA,
                                    start = Offset(
                                        size.width / 2,
                                        size.height / 2
                                    ),
                                    end = Offset(
                                        size.width / 2,
                                        textHeightState.intValue.toFloat() + 12.dp.toPx()
                                    ),
                                    strokeWidth = 1.dp.toPx(),
                                    pathEffect = PathEffect.dashPathEffect(
                                        floatArrayOf(3.dp.toPx(), 2.dp.toPx()),
                                        0f
                                    )
                                )
                            }
                        },
                    imageVector = ImageVector.vectorResource(id = R.drawable.ui_bag_icon),
                    contentDescription = null,
                    tint = Color.Unspecified
                )
                Text(
                    modifier = Modifier.weight(1F),
                    text = it,
                    color = Black484848,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    onTextLayout = {
                        textHeightState.intValue = it.size.height
                    }
                )
            }
        }
        if (list.size >= 2) {
            getJoinString(
                listOf(list[1].jobTitle, list[1].companyName),
                "  ·  "
            )?.let {
                Row(
                    modifier = Modifier.padding(top = 8.dp),
                ) {
                    Icon(
                        modifier = Modifier
                            .padding(end = 8.dp)
                            .widthIn(min = 18.dp)
                            .drawBehind {
                                if (list.size >= 2) {
                                    drawLine(
                                        color = GRAY_AAAAAA,
                                        start = Offset(
                                            size.width / 2,
                                            size.height / 2
                                        ),
                                        end = Offset(
                                            size.width / 2,
                                            0.dp.toPx()
                                        ),
                                        strokeWidth = 1.dp.toPx()
                                    )
                                }
                            },
                        imageVector = ImageVector.vectorResource(id = R.drawable.ui_concentric_circles_icon),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                    Text(
                        modifier = Modifier.weight(1F),
                        text = it,
                        color = Black484848,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        onTextLayout = {
                            textHeightState.intValue = it.size.height
                        }
                    )
                }
            }
        }
    }
}


@Composable
fun CandidateCardDescArea(
    item: ChatResumeBean
) {
    Row(
        modifier = Modifier.padding(top = 8.dp),
    ) {
        Column(
            modifier = Modifier.weight(1F)
        ) {
            item.eduExp?.let {
                getJoinString(
                    listOf(it.majorName, it.schoolName),
                    "  ·  "
                )?.let {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            modifier = Modifier
                                .padding(end = 8.dp)
                                .widthIn(min = 18.dp),
                            imageVector = ImageVector.vectorResource(id = R.drawable.ui_edu_gray_icon),
                            contentDescription = null,
                            tint = Color.Unspecified
                        )
                        Text(
                            text = it,
                            color = Black484848,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
            item.desiredJobTitle?.let {
                val text = buildAnnotatedString {
                    withStyle(style = SpanStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Black888888
                    )
                    ) {
                        append("${it.tag} ")
                    }
                    withStyle(style = SpanStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Black484848
                    )
                    ) {
                        append("${it.desc}")
                    }
                }
                CandidateCardImageTextBar(text, R.drawable.ui_collect_solid_icon)
            }
            item.desiredLocation?.let {
                val text = buildAnnotatedString {
                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Black888888
                        )
                    ) {
                        append("${it.tag} ")
                    }
                    withStyle(
                        style = SpanStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Black484848
                        )
                    ) {
                        append("${it.desc}")
                    }
                }
                CandidateCardImageTextBar(text, R.drawable.ui_address_gray_icon)
            }
        }
    }
}

@Composable
private fun CandidateCardImageTextBar(text: AnnotatedString, @DrawableRes drawableResId: Int) {
    Row(
        modifier = Modifier.padding(top = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            modifier = Modifier
                .padding(end = 8.dp)
                .widthIn(min = 18.dp),
            imageVector = ImageVector.vectorResource(id = drawableResId),
            contentDescription = null,
            tint = Color.Unspecified
        )
        Text(
            text = text,
            color = Black484848,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}
