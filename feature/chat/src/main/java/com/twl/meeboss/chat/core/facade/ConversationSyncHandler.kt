package com.twl.meeboss.chat.core.facade

import com.blankj.utilcode.util.AppUtils
import com.twl.meeboss.chat.api.ChatBossApi
import com.twl.meeboss.chat.api.ChatGeekApi
import com.twl.meeboss.chat.core.model.convert.toConversation
import com.twl.meeboss.chat.repos.ChatBossRepository
import com.twl.meeboss.chat.repos.ChatConversationRepository
import com.twl.meeboss.chat.repos.ChatGeekRepository
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.core.network.getService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 同步会话列表逻辑
 */
class ConversationSyncHandler(private val chatConversationRepository: ChatConversationRepository,
    private val scope: CoroutineScope ) {

    init {
        val versionCode = SpManager.getUserLong(getUpgradeVersionKey(), 0)
        if (UserProvider.isBoss() && versionCode < 112000) { // 1.12 会话数据表升级，强制重新同步
            SpManager.putUserString(getVersionKey(), "")
            SpManager.putUserLong(getUpgradeVersionKey(), AppUtils.getAppVersionCode().toLong())
        }
    }

    private fun getVersionKey(): String {
        return "conversation_version"
    }

    private fun getUpgradeVersionKey(): String {
        return "conversation_upgrade_version"
    }

    fun updateConversation() {
        scope.launch(Dispatchers.IO) {
            val lastVersion = SpManager.getUserString(getVersionKey(), "").notNull()
            val result = if (UserProvider.isGeek()) {
                ChatGeekRepository(getService(ChatGeekApi::class.java)).getConversations(lastVersion)
            } else {
                ChatBossRepository(getService(ChatBossApi::class.java)).getConversations(lastVersion)
            }
            if (result.isSuccess) {
                result.getOrNull()?.content?.forEach { contactFromNet ->
                    contactFromNet.toConversation().apply {
                        val dbConversation = chatConversationRepository.getConversationFromDB(friendId)
                        if (dbConversation == null) {
                            chatConversationRepository.insertConversation(this)
                        } else {
                            chatConversationRepository.updateConversationParams(this)
                        }
                    }

                }
                SpManager.putUserString(getVersionKey(), result.getOrNull()?.maxVersion.notNull())
            }
        }
    }
}