package com.twl.meeboss.chat.module.common.viewmodel


import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.map
import com.blankj.utilcode.util.Utils
import com.twl.meeboss.base.apm.ApmAction
import com.twl.meeboss.base.apm.ApmManager
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.repo.toastErrorIfPresent
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.api.resp.QuickReplyTemplateItem
import com.twl.meeboss.chat.core.bus.MessageEvent
import com.twl.meeboss.chat.core.bus.MessageEventBus
import com.twl.meeboss.chat.core.db.ChatDatabase
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.convert.toMessageModal
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.export.constant.isFriendStatusHasSendExchange
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.module.common.uistate.ChatUIState
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.ktx.notNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


abstract class BaseChatViewModel<UiState : ChatUIState<*>, UiIntent : IUiIntent> : BaseMviViewModel<UiState, UiIntent>()  {
    protected val tag = this::class.java.simpleName

    private val _messages: MutableStateFlow<Flow<PagingData<MessageModel<out ChatMessage>>>> = MutableStateFlow(flow { })
    val messages: StateFlow<Flow<PagingData<MessageModel<out ChatMessage>>>> = _messages

    private val _newMessage = MutableStateFlow<ChatMessage?>(null)
    val newMessage: StateFlow<ChatMessage?> = _newMessage

    val conversationInitResult: MutableLiveData<Conversation> = MutableLiveData()

    private val dao = ChatDatabase.getInstance(Utils.getApp()).messageDao()
    private val showTimeInterval = 5 * 60 * 1000

    var conversation: Conversation? = null

    var chatSource:ChatSource = ChatSource.ContactList

    private var _init = true

    fun getSecurityId() = conversation?.securityId

    var mFriendId: String = ""

    var mFriendIdentity: Int = UserConstants.NONE_IDENTITY

    protected val mMessageFacade = FacadeManager.getInstance().messageFacade

    protected val mConversationFacade = FacadeManager.getInstance().conversationFacade

    val messageEventFlow = MessageEventBus.events.filter { it.friendId == mFriendId }.flowOn(Dispatchers.IO)

    val quickReplayCMids = mutableMapOf<Long, QuickReplyTemplateItem>()

    fun init(friendId: String, friendIdentity: Int) {
        this.mFriendId = friendId
        this.mFriendIdentity = friendIdentity
        updateConversation()
        getConversation()
        registerQuickReplayTrigger()
        registerQuickReplaySendingResult()
    }


    private fun updateConversation() {
        viewModelScope.launch(Dispatchers.IO) {
            mConversationFacade.updateConversationFromNet(
                chatId = mFriendId,
                friendIdentity = mFriendIdentity
            )
        }
    }

    private fun getConversation() {
        viewModelScope.launch {
            mConversationFacade.getConversationAsFlow(mFriendId).collect {
                it?.let {
                    if(it.isUserDeleted()){
                        mConversationFacade.deleteConversation(mFriendId)
                    }
                    if (conversation != it) { //防止死循环
                        conversation = it
                        onConversationChanged(it)
                    }
                    if (_init) {
                        getMessages()
                        initComplete(it)
                        conversationInitResult.postValue(it)
                        updateRead()
                        _init = false
                        syncQuickReplies()
                        addStartChatPoint()
                    }

                }
            }
        }

    }

    private fun addStartChatPoint(){
        conversation?.run {
            ChatPointReporter.enterChat(source = chatSource, this)
        }
    }

    private fun updateRead() {
        viewModelScope.launch(Dispatchers.IO) {
            mMessageFacade.sendReadMessage(mFriendId)
        }
    }

    protected open fun getMessages() {
        viewModelScope.launch(Dispatchers.IO) {
            val pagingDataFlow = Pager(
                config = PagingConfig(
                    pageSize = 50, enablePlaceholders = true,
                    initialLoadSize = 50
                ),
                pagingSourceFactory = {
                    dao.queryMessages(mFriendId)
                }
            )
                .flow
                .map { pagingDataFromDb ->
                    pagingDataFromDb.map { record ->
                        record.toMessageModal().also { msg ->
                            msg.tinyAvatar = conversation?.tinyAvatar ?: conversation?.avatar.notNull()
                            msg.avatar = conversation?.avatar ?: conversation?.tinyAvatar.notNull()
                        }
                    }.insertSeparators { before: MessageModel<out ChatMessage>?, after: MessageModel<out ChatMessage>? ->
                        //对比每两条之间的时间差，如果大于5分钟则显示时间
//                        TLog.debug(tag, "before:${before?.message?.content} \n after:${after?.message?.content}")
                        if (after == null && before != null) {
                            before.message.isShowTime = true
                        } else if (after != null && before != null) {
                            before.message.isShowTime = before.message.addTime - after.message.addTime > showTimeInterval
                        }
                        return@insertSeparators null
                    }
                }
                .cachedIn(viewModelScope)

            _messages.emit(pagingDataFlow)
        }

    }

    private fun registerQuickReplayTrigger() {
        messageEventFlow.filter { it is MessageEvent.MessageReceived || it is MessageEvent.MessageSentSuccessfully || it is MessageEvent.MessageSendFailed }
            .onEach {
                if (it is MessageEvent.MessageSentSuccessfully) {
                    quickReplayCMids.remove(it.message.cmid)?.let { quickReply ->
                        quickReplyTemplateClick(mFriendId, mFriendIdentity, quickReply.type, quickReply.scene, it.message.mid)
                    }
                } else if (it is MessageEvent.MessageSendFailed) {
                    quickReplayCMids.remove(it.message.cmid)
                }
            }
            .onEach {
                if (it !is MessageEvent.MessageSendFailed && uiStateFlow.value.quickReplies.isNotEmpty()) {
                    syncQuickReplies()
                }
            }
            .launchIn(viewModelScope)
    }

    fun syncQuickReplies() {
        launcherOnIO {
            val quickReplies = getQuickReplies()
            sendUiState {
                copy(
                    quickReplies = quickReplies
                ) as UiState
            }
        }
    }

    private fun registerQuickReplaySendingResult() {
        messageEventFlow.filter { it is MessageEvent.MessageSentSuccessfully || it is MessageEvent.MessageSendFailed}
            .onEach {
                if (it is MessageEvent.MessageSentSuccessfully) {
                    quickReplayCMids.remove(it.message.cmid)?.let { quickReply ->
                        quickReplyTemplateClick(mFriendId, mFriendIdentity, quickReply.type, quickReply.scene, it.message.mid)
                    }
                } else if (it is MessageEvent.MessageSendFailed){
                    quickReplayCMids.remove(it.message.cmid)
                }
            }
            .launchIn(viewModelScope)
    }

    private suspend fun getQuickReplies(): List<QuickReplyTemplateItem> {
        val quickReplyTemplate = FacadeManager.getInstance().conversationFacade.getQuickReplyTemplate(mFriendId, mFriendIdentity, conversation?.jobId ?: "")
        return quickReplyTemplate.getOrNull() ?: emptyList()
    }

    private suspend fun quickReplyTemplateClick(friendId: String, friendIdentity: Int, type: Int, scene: String, mid: Long): Boolean = withContext(Dispatchers.IO){
        val result =  FacadeManager.getInstance().conversationFacade.quickReplyTemplateClick(friendId, friendIdentity, type, scene, mid)
        result.toastErrorIfPresent()
        result.isSuccess
    }

    abstract fun initComplete(contact: Conversation)

    abstract fun onConversationChanged(conversation: Conversation)

    fun isFriendStatusHasSendExchange() = conversation?.friendStatus?.isFriendStatusHasSendExchange()?:false

    fun onDestroy(){
        if(conversation == null){
            ApmManager.apmReport(ApmAction.NO_CONVERSATION_WHEN_CHAT)
        }
    }

}
