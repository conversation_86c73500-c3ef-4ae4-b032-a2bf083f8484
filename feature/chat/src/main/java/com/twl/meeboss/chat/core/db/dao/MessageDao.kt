package com.twl.meeboss.chat.core.db.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.twl.meeboss.chat.core.model.message.MessageRecord
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.export.constant.MessageStatus
import kotlinx.coroutines.flow.Flow


@Dao
interface MessageDao {

    @Query("select * from message where chatId = :uid and bodyType != 0 and bodyType!=7 GROUP BY mid ORDER BY MAX(addTime) DESC")
    fun queryMessages(uid: String): PagingSource<Int, MessageRecord>

    //    @Insert(onConflict = OnConflictStrategy.REPLACE)
    //    suspend fun insertMessages(messageRecords: List<MessageRecord>)
    //
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertMessage(messageRecord: MessageRecord): Long

    /**
     * 用于判断是否有本用户发送的消息，有一些限制
     * 1.仅判断是否发送过 文本、图片、文件、语音，因为系统消息的fromId是默认本用户的。
     * 2.仅用于双聊
     */
    @Query("SELECT EXISTS (SELECT 1 FROM message WHERE chatId = :friendId AND fromId!=:friendId AND bodyType IN (1, 3, 4, 5))")
    fun queryHasMsgFromSelf(friendId: String): Flow<Boolean>

    //
    //    @Query("SELECT max(seq) FROM message WHERE state!=:failureStatus AND sid > 0")
    //    suspend fun getMaxSeq(failureStatus: Byte): Long
    //
    @Query("UPDATE message SET status = :status WHERE id =:id ")
    fun updateMessageStatus(id: Long, status: Int)

    //
    //    @Query("UPDATE message SET seq = :seq, sid =:sid WHERE createTime =:createTime ")
    //    suspend fun updateServerInfo(createTime: Long, sid: Long, seq: Long)
    //
    //    @Query("UPDATE message SET state = :state WHERE  sid != 0 and sid <= :sid and friendUid" +
    //            " = (Select friendUid from message where sid = :sid) and senderUid = :senderId")
    //    suspend fun updateRead(sid: Long, state: Int, senderId: Long)
    //
    @Query("SELECT mid FROM message WHERE chatId =:friendId and fromId=:friendId ORDER BY mid desc LIMIT 1")
    fun getMaxRecID(friendId: String): Long

    @Query("SELECT max(mid) FROM message")
    fun getMaxMessageId(): Long

    //
    @Query("SELECT * FROM message WHERE mid =:mid  ORDER BY id desc LIMIT 1")
    fun query(mid: Long): MessageRecord?

    @Query("SELECT * FROM message WHERE cmid =:cMid AND chatId=:chatId ORDER BY id desc LIMIT 1")
    fun queryByCMid(cMid: Long,chatId: String): MessageRecord?

    @Query("UPDATE message SET status=3 WHERE chatId=:chatId AND toId =:friendId ANd status in (1,2) AND mid <= :maxMid")
    suspend fun updateMessageToRead(friendId: String,chatId: String, maxMid:Long): Int

    @Delete
    fun deleteMessage(message: MessageRecord)

    @Query("select * from message where status = :status ORDER BY addTime DESC")
    fun getAllMessagesByStatus(@MessageStatus status: Int):List<MessageRecord>

    //
    //    @Query("SELECT * FROM read")
    //    suspend fun getReads(): List<ReadStatus>?
    //
    //    @Insert(onConflict = OnConflictStrategy.REPLACE)
    //    suspend fun insertRead(readStatus: ReadStatus)
    //
    //    @Delete
    //    suspend fun removeRead(readStatus: ReadStatus)
    //
    //    @Query("UPDATE message SET state=:newState WHERE senderUid=:selfId AND state =:original")
    //    suspend fun updateFailures(selfId: Long, original: Int, newState: Int): Int
    //
    //    @Query("select (select max(seq) + 1 from message where seq < a.seq) as startSeq, (seq - 1) as endSeq from message as a  where a.seq >(select max(seq) + 1 from message where seq < a.seq)")
    //    suspend fun queryPatch(): List<MessagePatch>?
    //
    //    @Query("SELECT id, friendUid as userId FROM message WHERE state = :newState ORDER BY id desc LIMIT :count")
    //    suspend fun queryFailcommon_feedbackures(count: Int, newState: Int): List<FailureKey?>?
    //
    @Query("SELECT count(1) FROM message Where chatId =:chatId AND cmid != :cMid AND mid != :mid and addTime > :time AND visible =1 AND deleted = 0")
    fun queryRecentCount(chatId: String, mid: Long,cMid: Long,time: Long): Int

    //
    //    @Query("UPDATE message SET state=:newState , isVisible =:visible WHERE friendUid=:friendId")
    //    suspend fun clearChat(friendId: Long, newState: Int, visible: Int): Int
    //
    //    @Update
    //    suspend fun updateMessage(messageRecord: MessageRecord)
    //
    @Query("select * from message where id = :id")
    fun findMessageById(id: Int): MessageRecord?
    //    @Query("UPDATE message SET isDownloading = 0")
    //    suspend fun resetFileMessageStatus()
    //    @Query("SELECT * FROM message WHERE createTime =:cid  ORDER BY id desc LIMIT 1")
    //    suspend fun queryByCid(cid: Long): MessageRecord?

    @Query("delete from message where chatId = :friendId")
    fun deleteAllMessages(friendId: String)

    @Query("select * from message where chatId = :chatId and localMessageType = :type ORDER BY id ASC, addTime ASC")
    fun queryMessagesByType(chatId: String,@LocalMessageType type:Int): List<MessageRecord>

}