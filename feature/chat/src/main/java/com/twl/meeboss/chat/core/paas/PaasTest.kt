package com.twl.meeboss.chat.core.paas

import com.twl.meeboss.common.BuildConfig
import java.util.Collections


/**
 * 方便测试分辨消息是否是paas发送和接收
 * 正式环境需要删除
 */
object PaasTest {

    private val PAAS_MSG_ID = Collections.synchronizedSet(HashSet<Long>())

    fun onMessageReceive(biMessageCMid: Long) {
        PAAS_MSG_ID.add(biMessageCMid)
    }

    fun onMessageSend(biMessageCMid: Long) {
        PAAS_MSG_ID.add(biMessageCMid)
    }

    fun isPaasMessage(cMid: Long) = BuildConfig.DEBUG && PAAS_MSG_ID.contains(cMid)
}