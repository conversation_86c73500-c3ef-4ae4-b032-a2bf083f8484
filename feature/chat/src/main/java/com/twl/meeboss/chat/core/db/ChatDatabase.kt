package com.twl.meeboss.chat.core.db

import android.content.Context
import androidx.room.Database
import androidx.room.Room.databaseBuilder
import androidx.room.RoomDatabase
import com.blankj.utilcode.util.EncryptUtils
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.chat.BuildConfig
import com.twl.meeboss.chat.core.db.ChatDatabase.Companion.DATABASE_VERSION
import com.twl.meeboss.chat.core.db.dao.ConversationDao
import com.twl.meeboss.chat.core.db.dao.MessageDao
import com.twl.meeboss.chat.core.model.message.MessageRecord
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.common.provider.UserProvider

@Database(entities = [MessageRecord::class, Conversation::class], version = DATABASE_VERSION)
abstract class ChatDatabase : RoomDatabase() {

    abstract fun messageDao(): MessageDao

    abstract fun conversationDao(): ConversationDao


    companion object {
        private const val TAG = "ChatDatabase"
        const val DATABASE_VERSION = 5

        private var mInstance: ChatDatabase? = null

        fun getInstance(context: Context): ChatDatabase {
            if (mInstance == null) {
                synchronized(ChatDatabase::class.java) {
                    mInstance = createDatabase(context)
                }
            }
            return mInstance!!
        }

        private fun createDatabase(context: Context): ChatDatabase {
            val builder: Builder<ChatDatabase> = databaseBuilder<ChatDatabase>(context, ChatDatabase::class.java, getDatabasePath(context))
                .allowMainThreadQueries()
//            if(!BuildConfig.DEBUG){
//                builder.openHelperFactory(createSupportFactory())
//            }
            builder.addMigrations(MIGRATION_1_2)
            builder.addMigrations(MIGRATION_2_3)
            builder.addMigrations(MIGRATION_3_4)
            builder.addMigrations(MIGRATION_4_5)
            TLog.info(TAG,"createDatabase,path =  %s", getDatabasePath(context))
            return builder.build()
        }



        private fun getDatabasePath(context: Context): String {
            val path: String = context.getDatabasePath(getDatabaseName()).getAbsolutePath()
            return path
        }

        private fun getDatabaseName(): String {
            return "chat_${UserProvider.userIdStr()}_${UserProvider.getIdentify()}.db"
        }

        fun removeDatabase() {
            if (mInstance != null) {
                synchronized(ChatDatabase::class.java) {
                    mInstance?.close()
                    mInstance = null
                    TLog.info(TAG,"removeDatabase")

                }
            }
        }

        private fun getPassword(): String {
            val id: String = EncryptUtils.encryptMD5ToString(UserProvider.userIdStr())
            val length = id.length
            val password = if (length > 6) {
                id.substring(length - 6)
            } else {
                id
            }
            if (BuildConfig.DEBUG) {
                return ""
            }
            return password
        }
    }


}