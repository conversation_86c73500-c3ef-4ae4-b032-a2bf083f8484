package com.twl.meeboss.chat.api.resp

import androidx.annotation.Keep

/**
 * @author: musa on 2025/05/26
 * @e-mail: <EMAIL>
 * @desc: 快捷回复模板返回结果
 */
//@Keep
//data class QuickReplyTemplateResp(
//    val quickReplyTemplateList: List<QuickReplyTemplateItem> = emptyList()
//)

@Keep
data class QuickReplyTemplateItem(
    val type: Int = 0,
    val scene: String = "",
    val content: String = ""
){
    companion object {
        const val SCENT_EMPLOYER_CHAT_VISA_SPONSOR_ANSWER_YES = "employer_chat_visa_sponsor_answer_yes"
        const val SCENT_EMPLOYER_CHAT_VISA_SPONSOR_ANSWER_NO = "employer_chat_visa_sponsor_answer_no"
    }
}