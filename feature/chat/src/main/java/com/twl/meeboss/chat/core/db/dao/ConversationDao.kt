package com.twl.meeboss.chat.core.db.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.export.model.Conversation
import kotlinx.coroutines.flow.Flow

@Dao
interface ConversationDao {
    @Query("SELECT * FROM Conversation ORDER BY updateTime desc")
    fun getContact(): PagingSource<Int, Conversation>

    @Query("SELECT SUM(unReadCount) FROM Conversation")
    fun selectAllUnReadCount(): Int

    //
    //    @Insert(onConflict = OnConflictStrategy.REPLACE)
    //    suspend fun insertConversations(Conversations: List<Conversation>)
    //
    //    @Insert(onConflict = OnConflictStrategy.IGNORE)
    //    suspend fun insertConversation(contacts: Conversation)
    //
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun updateConversation(conversation: Conversation): Long

    @Query("SELECT * FROM Conversation where visible =1 AND (:jobId IS NULL OR :jobId = '' OR jobId = :jobId) ORDER BY updateTime desc")
    fun getAllVisibleConversationsByJobId(jobId: String?): PagingSource<Int, Conversation>

    @Query("SELECT * FROM Conversation where friendStatus = 2 AND visible=1 AND (:jobId IS NULL OR :jobId = '' OR jobId = :jobId) ORDER BY updateTime desc")
    fun getNewGreetingConversationsByJobId(jobId: String?): PagingSource<Int, Conversation>

    @Query("SELECT * FROM Conversation where friendStatus =3 AND visible=1 AND (:jobId IS NULL OR :jobId = '' OR jobId = :jobId) ORDER BY updateTime desc")
    fun getInCommunicateConversationsByJobId(jobId: String?): PagingSource<Int, Conversation>

    @Query("SELECT * FROM Conversation where visible=1 AND starred=1 AND (:jobId IS NULL OR :jobId = '' OR jobId = :jobId) ORDER BY updateTime desc")
    fun getStarredConversationsByJobId(jobId: String?): PagingSource<Int, Conversation>

    @Query("SELECT * FROM Conversation where visible=1 AND topMatched=1 AND (:jobId IS NULL OR :jobId = '' OR jobId = :jobId) ORDER BY updateTime desc")
    fun getTopMatchedConversationsByJobId(jobId: String?): PagingSource<Int, Conversation>

    @Query("SELECT COUNT(*) > 0 FROM Conversation WHERE visible=1 AND topMatched=1")
    fun hasTopMatchedConversations(): Flow<Boolean>

    /**
     * 获取所有有会话的职位 ID 列表
     */
    @Query("""
        SELECT DISTINCT jobId 
        FROM Conversation 
        WHERE (jobId IS NOT NULL AND jobId != '')
          AND friendIdentity = ${UserConstants.GEEK_IDENTITY}
          AND visible = 1
        GROUP BY jobId
        HAVING COUNT(*) > 0
    """)
    fun observeJobIdsWithConversations(): Flow<List<String>>

    //
    //    @Query("SELECT * FROM Conversation where unReadCount > 0 ORDER BY conversationTime desc")
    //    suspend fun getUnReadContact(): PagingSource<Int, Conversation>
    //
    @Query("UPDATE Conversation SET unReadCount = 0 WHERE friendId = :friendId")
    suspend fun clearUnRead(friendId: String)


    @Query("SELECT sum(unReadCount)  from Conversation where visible = 1 AND blacklist = 0 and unReadCount > 0")
    fun selectUnReadCount(): Flow<Int>

    //
    //    @Query("SELECT * from Conversation where STATUS = -99")
    //    suspend fun selectUnCompleteContact(): List<Conversation>?
    //
    //    @Query("SELECT * from Conversation where id = :uid")
    //    suspend fun selectContactById(uid: Long): Flow<Conversation?>
    //
    @Query("SELECT * from Conversation where friendId = :friendId")
    fun getConversation(friendId: String): Conversation?

    @Query("SELECT * from Conversation where friendId = :friendId")
    fun getConversationAsFlow(friendId: String): Flow<Conversation?>

    @Query("Update Conversation Set draft = :draft where friendId = :friendId")
    fun saveDraft(draft: String, friendId: String)

    @Query("Update Conversation Set draft = :draft ,updateTime = :updateTime where friendId = :friendId")
    fun saveDraftAndUpdateTime(draft: String, friendId: String, updateTime: Long)

    @Query("Update Conversation Set lastMessageStatus = :status where friendId = :friendId")
    fun updateConversationLastMsgStatus(friendId: String, @MessageStatus status: Int)

    @Query("Update Conversation Set starred = :starred where friendId = :friendId")
    fun starContact(friendId: String, starred: Boolean)

    //
    //    @Transaction
    //    suspend fun updateContact(
    //            uid: Long,
    //            currentUid: Long,
    //            createTime: Long,
    //            messageType: Int,
    //            messageStatus: Int,
    //            message: MessageRecord,
    //            isSend: Boolean
    //    ) {
    //        val contact = Conversation()
    //        contact.id = uid
    //        contact.status = MessageConstants.CON_STATE_NONE
    //        insertContact(contact)
    //        if (messageStatus < MessageConstants.MSG_STATE_READ) {
    //            if (message.senderUid != currentUid) {
    //                addUnReadCount(uid)
    //            }
    //        }
    //        var msg = message.msg ?: ""
    //
    //        when (messageType) {
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_CONTACT_REQUEST -> {
    //                if (message.state != MessageConstants.MSG_STATE_READ) {
    //                    updateContactReceiveRequestStatus(true, uid)
    //                }
    //                msg = Gson().fromJson(message.compressiveContent, MessageRequestContact::class.java).text
    //                        ?: ""
    //            }
    //
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_CONTACT -> {
    //                val contact = Gson().fromJson(message.compressiveContent, MessageContact::class.java)
    //                msg = "${contact.phoneTitle}：${contact.phoneNumber} ${contact.emailTitle}：${contact.email}"
    //            }
    //
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_LETTER_REQUEST -> {
    //                if (message.state != MessageConstants.MSG_STATE_READ) {
    //                    updateContactReceiveRequestStatus(true, uid)
    //                }
    //                msg = Gson().fromJson(message.compressiveContent, MessageRequestCoverLetter::class.java).text
    //            }
    //
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_RESUME_REQUEST -> {
    //                if (message.state != MessageConstants.MSG_STATE_READ) {
    //                    updateContactReceiveRequestStatus(true, uid)
    //                }
    //                msg = Gson().fromJson(message.compressiveContent, MessageRequestResume::class.java).text
    //            }
    //
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_DOWNLOAD_RESUME -> {
    //                msg = Gson().fromJson(message.compressiveContent, MessageDownloadResume::class.java).fileName
    //                        ?: ""
    //                if (message.state != MessageConstants.MSG_STATE_READ) {
    //                    updateContactReceiveResumeStatus(true, uid)
    //                }
    //            }
    //
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_DOWNLOAD_LETTER -> {
    //                msg = Gson().fromJson(message.compressiveContent, MessageDownloadLetter::class.java).fileName
    //                        ?: ""
    //            }
    //
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_SYSTEM_NOTICE -> {
    //                msg = Gson().fromJson(message.compressiveContent, MessageSystemNotice::class.java).text
    //            }
    //
    //            //如果是牛人卡片就更新联系人的基本信息
    //            MessageConstants.COMPRESSIVE_CARD_TYPE_GEEK -> {
    //                val geek =
    //                        Gson().fromJson(message.compressiveContent, GeekCardInfo::class.java)
    //                updateContactBaseInfo(
    //                        geek.firstName + " " + geek.lastName,
    //                        geek.avatar ?: "",
    //                        geek.jobFunction,
    //                        "",
    //                        uid
    //                )
    //            }
    //
    //            else -> {
    //
    //            }
    //        }
    //
    //
    //        updateContactLastMessage(
    //                uid,
    //                msg,
    //                messageStatus,
    //                createTime,
    //                message.id.toLong()
    //        )
    //        dealContactStatus(uid, message)
    //    }
    //
    //
    //    fun dealContactStatus(uid: Long, message: MessageRecord) {
    //        if (message.senderUid != uid) {
    //
    //            if (message.compressiveType == MessageConstants.COMPRESSIVE_CARD_TYPE_POSITION || message.compressiveType == MessageConstants.COMPRESSIVE_CARD_TYPE_GEEK) {
    //                updateContactStatus(uid, MessageConstants.CON_STATE_START_BY_ME_NO_READ)
    //            } else {
    //                if (message.state == MessageConstants.MSG_STATE_READ) {
    //                    updateContactStatus(
    //                            uid,
    //                            MessageConstants.CON_STATE_START_BY_ME_NO_REPLY,
    //                            true,
    //                            arrayListOf(MessageConstants.CON_STATE_START_BY_ME_NO_READ)
    //                    )
    //                }
    //                updateContactStatus(
    //                        uid,
    //                        MessageConstants.CON_STATE_START_BY_OTHER_COMPLETED,
    //                        true,
    //                        arrayListOf(
    //                                MessageConstants.CON_STATE_START_BY_OTHER_NO_REPLY,
    //                                MessageConstants.CON_STATE_START_BY_OTHER_NO_READ
    //                        )
    //                )
    //            }
    //        } else {
    //            if (message.compressiveType == MessageConstants.COMPRESSIVE_CARD_TYPE_POSITION || message.compressiveType == MessageConstants.COMPRESSIVE_CARD_TYPE_GEEK) {
    //                updateContactStatus(uid, MessageConstants.CON_STATE_START_BY_OTHER_NO_READ)
    //            } else {
    //                if (message.state == MessageConstants.MSG_STATE_READ) {
    //                    updateContactStatus(
    //                            uid,
    //                            MessageConstants.CON_STATE_START_BY_OTHER_NO_REPLY,
    //                            true,
    //                            arrayListOf(MessageConstants.CON_STATE_START_BY_OTHER_NO_READ)
    //                    )
    //                }
    //                updateContactStatus(
    //                        uid,
    //                        MessageConstants.CON_STATE_START_BY_ME_COMPLETED,
    //                        true,
    //                        arrayListOf(
    //                                MessageConstants.CON_STATE_START_BY_ME_NO_REPLY,
    //                                MessageConstants.CON_STATE_START_BY_ME_NO_READ
    //                        )
    //                )
    //            }
    //        }
    //    }
    //
    @Query("UPDATE Conversation SET firstName =:firstName,securityId =:securityId," +
            " lastName = :lastName, avatar = :avtarUrl,tinyAvatar=:tinyAvatar,schoolName=:schoolName," +
            " jobTitle = :jobTitle, highestEduLevelDesc = :highestEduLevelDesc,companyName = :companyName, bossPosition = :bossPosition, salaryDesc = :salaryDesc, " +
            " userStatus = :userStatus, friendStatus = :friendStatus, friendSource = :friendSource, starred = :starred, topMatched = :topMatched, visible = :visible " +
            ",friendLastReadMsgId=:friendLastReadMsgId,lastDeletedMsgId=:lastDeletedMsgId,friendIdentity=:friendIdentity" +
            ",jobId=:jobId,expectId=:expectId,userStatusPrompt=:userStatusPrompt" +
            " WHERE friendId = :friendId")
    suspend fun updateConversationByNet(
        friendId: String,
        firstName: String,
        lastName: String,
        avtarUrl: String,
        tinyAvatar: String,
        schoolName: String,
        companyName: String,
        jobTitle: String,
        highestEduLevelDesc: String,
        bossPosition: String,
        salaryDesc: String,
        userStatus: Int,
        friendStatus: Int,
        starred: Boolean,
        topMatched: Boolean,
        friendSource: Int,
        visible: Boolean,
        friendLastReadMsgId: Long,
        lastDeletedMsgId: Long,
        friendIdentity:Int,
        securityId: String,
        jobId:String,
        expectId:String,
        userStatusPrompt: String
    )


    //    @Query("UPDATE Conversation SET msgStatus = 3 WHERE id = :uid and messageId = :messageId")
    //    suspend fun updateLastMessageToFailed(uid: Long, messageId: Long)
    //
    @Query("UPDATE Conversation SET unReadCount = unReadCount + 1 WHERE  friendId= :friendId")
    fun addUnReadCount(friendId: String)
    //
    //    //改变状态 有的情况需要依赖之前的状态
    //    @Query("UPDATE Conversation SET status = :status WHERE id = :uid and (:needBeforeStatus = 0 or (:needBeforeStatus = 1 and status in (:beforeStatus)))")
    //    suspend fun updateContactStatus(
    //            uid: Long,
    //            status: Int,
    //            needBeforeStatus: Boolean,
    //            beforeStatus: ArrayList<Int>
    //    )

    @Query("UPDATE Conversation SET lastMessageContent = :lastMessageContent, updateTime = :updateTime ," +
            "lastMessageStatus = :lastMessageStatus ,lastMid = :lastMid ,lastCMid =:lastCMid ,lastMessageBodyType =:lastMessageBodyType ," +
            "lastMessageLocalType =:lastMessageLocalType ,lastMessageIsMine=:lastMessageIsMine ,friendIdentity=:friendIdentity " +
            "WHERE friendId = :friendId AND updateTime <= :updateTime")
    fun updateContactByMyMessage(
        friendId: String,
        friendIdentity: Int,
        lastMessageContent: String,
        updateTime: Long,
        lastMessageStatus: Int,
        lastMid: Long,
        lastCMid: Long,
        lastMessageBodyType: Int,
        lastMessageLocalType: Int,
        lastMessageIsMine: Boolean
    ): Int

    @Query("UPDATE Conversation SET lastMessageContent = :lastMessageContent, updateTime = :updateTime , " +
            "lastMessageStatus = :lastMessageStatus, lastMid = :lastMid ,lastCMid =:lastCMid ,lastMessageBodyType =:lastMessageBodyType ," +
            "lastMessageLocalType =:lastMessageLocalType ,lastMessageIsMine=:lastMessageIsMine ,unreadCount=:unreadCount ,friendIdentity=:friendIdentity " +
            "WHERE friendId = :friendId AND updateTime <= :updateTime")
    fun updateContactByFriendMessage(
        friendId: String,
        friendIdentity: Int,
        lastMessageContent: String,
        updateTime: Long,
        lastMessageStatus: Int,
        lastMid: Long,
        lastCMid: Long,
        lastMessageBodyType: Int,
        lastMessageLocalType: Int,
        unreadCount: Int,
        lastMessageIsMine: Boolean
    ): Int

    //
    //    @Query("UPDATE Conversation SET name =:name, avatar = :avtarUrl, position = :position, company = :company where id  = :uid")
    //    suspend fun updateContactBaseInfo(
    //            name: String,
    //            avtarUrl: String,
    //            position: String,
    //            company: String,
    //            uid: Long
    //    )
    //
    //    @Query("UPDATE Conversation SET status = 2 WHERE messageId = :messageId and status = 0")
    //    suspend fun updateContactStatusOnReceiveRead(messageId: Long)
    //
    //    @Query("UPDATE Conversation SET resumeRequestStatus = :status WHERE id = :uid")
    //    suspend fun updateContactSendRusumeStatus(uid: Long, status: Int)
    //
    //    @Query("UPDATE Conversation SET contactRequestStatus = :status WHERE id = :uid")
    //    suspend fun updateContactSendContactStatus(uid: Long, status: Int)
    //
    //    @Query("UPDATE Conversation SET coverLetterRequestStatus = :status WHERE id = :uid")
    //    suspend fun updateContactSendLetterStatus(uid: Long, status: Int)
    //
    //    @Query("UPDATE Conversation SET hasReceiveResume = :status WHERE id = :uid")
    //    suspend fun updateContactReceiveResumeStatus(status: Boolean, uid: Long)
    //
    //    @Query("UPDATE Conversation SET hasReceiveRequest = :status WHERE id = :uid")
    //    suspend fun updateContactReceiveRequestStatus(status: Boolean, uid: Long)
    @Query("delete from Conversation  WHERE friendId = :friendId")
    fun deleteConversation(friendId: String)

}