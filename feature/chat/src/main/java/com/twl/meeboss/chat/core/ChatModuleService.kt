package com.twl.meeboss.chat.core

import com.blankj.utilcode.util.ProcessUtils
import com.blankj.utilcode.util.Utils
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.mudule.BaseModuleService
import com.twl.meeboss.chat.core.db.ChatDatabase
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.common.utils.getNewScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object ChatModuleService:BaseModuleService() {
    override fun syncData() {
    }

    override fun onAccountInitialized() {

    }

    override fun onIdentityInitialized(identity: Int) {
        TLog.info("ChatModuleService","onIdentityInitialized: identity = $identity ,mainProcess = ${ProcessUtils.isMainProcess()}")
        ChatDatabase.getInstance(Utils.getApp().applicationContext)//先创建数据库
        FacadeManager.getInstance()
        getNewScope().launch(Dispatchers.Main){
            ConnectManager.instance?.connect()//再连接
        }
    }

    override fun onIdentityRelease(identity: Int) {
        TLog.info("ChatModuleService","onIdentityRelease: identity = $identity")
        ConnectManager.instance?.disconnect()
        FacadeManager.destroy()
        ChatDatabase.removeDatabase()
    }

    override fun onAccountRelease() {
    }

    override fun onSuspend(suspend: Boolean) {
    }
}