package com.twl.meeboss.chat.module.common.components.chatitems

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.ktx.toFormatMessageTime
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.paas.PaasTest
import com.twl.meeboss.chat.module.common.callback.ChatMessageClickCallbackDefault
import com.twl.meeboss.chat.module.common.callback.IChatMessageClickCallback
import com.twl.meeboss.chat.module.common.model.MessageLongClickBean
import com.twl.meeboss.chat.module.common.preview.MessagePreviewProvider
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.COLOR_F3FFED

@Composable
fun ChatItem(modifier: Modifier = Modifier, chatModel: MessageModel<out ChatMessage>,
             onLongClick:(MessageLongClickBean)->Unit = {},
             messageClickCallback: IChatMessageClickCallback = ChatMessageClickCallbackDefault()) {
    Column(modifier = modifier.padding(horizontal = 12.dp).background(color = if(PaasTest.isPaasMessage(chatModel.message.cmid)){COLOR_F3FFED}else{Color.White})) {
        Spacer(modifier = Modifier.height(16.dp))
        if (chatModel.message.isShowTime) {
            Text(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                text = chatModel.message.addTime.toFormatMessageTime(LocalContext.current),
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    color = Black888888,
                )
            )
            Spacer(modifier = Modifier.height(16.dp))
        }
        when (chatModel) {
            is MessageModel.TextForSelf -> {
                TextCardForSelf(model = chatModel,
                    onLongClick = onLongClick,
                    onResendClick = {
                    messageClickCallback.onClickResend(chatModel.message)
                })
            }

            is MessageModel.TextForOther -> {
                TextCardForOther(model = chatModel, onLongClick = onLongClick)
            }

            is MessageModel.GrayHint -> {
                GrayHintCard(model = chatModel)
            }

            is MessageModel.ImageForSelf -> {
                ImageCardForSelf(model = chatModel, onResendClick = {
                    messageClickCallback.onClickResend(chatModel.message)
                }) {
                    messageClickCallback.onPreviewImage(it)
                }
            }

            is MessageModel.ImageForOther -> {
                ImageCardForOther(model = chatModel) {
                    messageClickCallback.onPreviewImage(it)
                }
            }

            is MessageModel.FileForSelf -> {
                FileCardForSelf(
                    model = chatModel,
                    onFileClick = { messageClickCallback.onPreviewFile(it) })
            }

            is MessageModel.FileForOther -> {
                FileCardForOther(
                    model = chatModel,
                    onFileClick = { messageClickCallback.onPreviewFile(it) }
                )
            }

            is MessageModel.NotSupportForSelf -> {
                NotSupportCard()
            }

            is MessageModel.NotSupportForOther -> {
                NotSupportCard()
            }
            is MessageModel.GreetingJobFromGeek -> {
                GreetingJobCardFromGeek(model = chatModel)
            }

            is MessageModel.GreetingJobFromBoss -> {
                GreetingJobCardFromBoss(model = chatModel){
                    messageClickCallback.onClickJobCardInterest(it)
                }
            }

            is MessageModel.GreetingResumeFromGeek -> {
                GreetingResumeFromGeek(model = chatModel)
            }

            is MessageModel.GreetingResumeFromBoss -> {
                GreetingResumeFromBoss(model = chatModel)
            }

            is MessageModel.InterestGrayHint -> {
                InterestGrayHintCard(model = chatModel)
            }

            is MessageModel.RequestEmailForOther -> { //请求交换邮箱
                RequestEmailCardForOther(model = chatModel,
                    onAcceptRequest = {
                        messageClickCallback.onClickEmailRequest(it, true)
                    },
                    onRejectRequest = {
                        messageClickCallback.onClickEmailRequest(it, false)
                    })
            }

            is MessageModel.ExchangeEmailResultForOther -> { //请求交换邮箱结果
                ExchangeEmailResultCardForOther(model = chatModel)
            }

            is MessageModel.RequestPhoneForOther -> { //请求交换电话
                RequestPhoneCardForOther(model = chatModel,
                    onAcceptRequest = {
                        messageClickCallback.onClickPhoneRequest(it, true)
                    },
                    onRejectRequest = {
                        messageClickCallback.onClickPhoneRequest(it, false)
                    })
            }

            is MessageModel.ExchangePhoneResultForOther -> { //请求交换电话结果
                ExchangePhoneResultCardForOther(model = chatModel)
            }

            is MessageModel.ExchangeResumeRequestFromBoss -> { //B请求交换简历
                RequestResumeCardFromBoss(model = chatModel,
                    onAcceptRequest = {
                        messageClickCallback.onClickResumeRequest(it, true)
                    },
                    onRejectRequest = {
                        messageClickCallback.onClickResumeRequest(it, false)
                    })
            }

            is MessageModel.RequestSendResumeFromGeek -> { //C请求发送简历
                SendResumeRequestCardFromGeek(model = chatModel,
                    onAcceptRequest = {
                        messageClickCallback.onClickSendResumeRequest(it, true)
                    },
                    onRejectRequest = {
                        messageClickCallback.onClickSendResumeRequest(it, false)
                    })
            }

            is MessageModel.ExchangeResumeResultForGeek -> { //C显示的发送结果
                ExchangeResumeResultCardForSelf(model = chatModel){
                    messageClickCallback.onClickResumeResult(it)
                }
            }
            is MessageModel.ExchangeResumeResultForBoss -> { //B显示来自C的请求发送简历
                ExchangeResumeResultCardForOther(model = chatModel){
                    messageClickCallback.onClickResumeResult(it)
                }
            }
            is MessageModel.FirstMsgPostJobMessageFromMeeBoss -> {
                ChatOtherBox(model = chatModel) {
                    FirstMsgCardBody(
                        icon = R.drawable.chat_icon_post_job,
                        title = stringResource(R.string.message_employeronboarding_postjobcard_title),
                        content = stringResource(R.string.message_employeronboarding_postjobcard_subtitle),
                        button = stringResource(R.string.message_employeronboarding_postjobcard_button),
                        onJump = {
                            messageClickCallback.onClickFirstMsgPostJob(chatModel.message)
                        }
                    )
                }
            }

            is MessageModel.FirstMsgBrowseCandidatesMessageFromMeeBoss -> {
                ChatOtherBox(model = chatModel) {
                    FirstMsgCardBody(
                        icon = R.drawable.chat_icon_browse_candidates,
                        title = stringResource(R.string.message_employeronboarding_browsecandidatescard_title),
                        content = stringResource(R.string.message_employeronboarding_browsecandidatescard_subtitle),
                        button = stringResource(R.string.message_employeronboarding_browsecandidatescard_button),
                        onJump = {
                            messageClickCallback.onClickFirstMsgBrowseCandidate(chatModel.message)
                        }
                    )
                }
            }

            is MessageModel.FirstMsgBrowseJobsMessageFromMeeBoss -> {
                ChatOtherBox(model = chatModel) {
                    FirstMsgCardBody(
                        icon = R.drawable.chat_icon_browse_jobs,
                        title = stringResource(R.string.message_jobseekeronboarding_browsejobsscard_title),
                        content = stringResource(R.string.message_jobseekeronboarding_browsejobsscard_subtitle),
                        button = stringResource(R.string.message_jobseekeronboarding_browsejobsscard_button),
                        onJump = {
                            messageClickCallback.onClickFirstMsgBrowseJobs(chatModel.message)
                        }
                    )
                }
            }

        }

    }
}

//@Preview
//@Composable
//private fun PreviewChatItem() {
//    Column(modifier = Modifier
//        .fillMaxSize()
//        .background(COLOR_F5F5F5)) {
//        MessagePreviewProvider.getPreviewMessageList().forEach {
//            ChatItem(modifier = Modifier.fillMaxWidth(), it)
//        }
//    }
//
//}
@Preview
@Composable
private fun PreviewChatItem1() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(chatListBg)) {
        MessagePreviewProvider.getPreviewMessageList1().forEach {
            ChatItem(modifier = Modifier.fillMaxWidth(), it)
        }
    }

}

@Preview
@Composable
private fun PreviewChatItemGreetingResume() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(chatListBg)) {
        MessagePreviewProvider.getPreviewGreetingMessageList1().forEach {
            ChatItem(modifier = Modifier.fillMaxWidth(), it)
        }
    }

}
//
//@Preview
//@Composable
//private fun PreviewChatItem2() {
//    Column(modifier = Modifier
//        .fillMaxSize()
//        .background(COLOR_F5F5F5)) {
//        MessagePreviewProvider.getPreviewMessageList2().forEach {
//            ChatItem(modifier = Modifier.fillMaxWidth(), it)
//        }
//    }
//
//}

//@Preview
//@Composable
//private fun PreviewChatItem3() {
//    Column(modifier = Modifier
//        .fillMaxSize()
//        .background(COLOR_F5F5F5)) {
//        MessagePreviewProvider.getPreviewMessageList3().forEach {
//            ChatItem(modifier = Modifier.fillMaxWidth(), it)
//        }
//    }
//
//}
@Preview
@Composable
private fun PreviewChatItem4() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(chatListBg)) {
        MessagePreviewProvider.getPreviewMessageList4().forEach {
            ChatItem(modifier = Modifier.fillMaxWidth(), it)
        }
    }

}@Preview
@Composable
private fun PreviewChatItem5() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(chatListBg)) {
        MessagePreviewProvider.getPreviewMessageList5().forEach {
            ChatItem(modifier = Modifier.fillMaxWidth(), it)
        }
    }

}