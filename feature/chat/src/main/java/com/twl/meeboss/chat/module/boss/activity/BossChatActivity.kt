package com.twl.meeboss.chat.module.boss.activity

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.paging.PagingData
import androidx.paging.compose.collectAsLazyPagingItems
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.BUNDLE_OBJECT
import com.twl.meeboss.base.constants.BUNDLE_SOURCE
import com.twl.meeboss.base.constants.BUNDLE_USER_ID
import com.twl.meeboss.base.constants.BUNDLE_USER_IDENTITY
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.toastShort
import com.twl.meeboss.base.media.pictureselector.PictureSelectInput
import com.twl.meeboss.base.media.pictureselector.SelectPictureContract
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.export.ChatPageRouter
import com.twl.meeboss.chat.export.ChatRouterPath
import com.twl.meeboss.chat.export.constant.isChatUserDeleted
import com.twl.meeboss.chat.export.model.BossChatParams
import com.twl.meeboss.chat.module.boss.activity.callback.BossUIEventDispatcher
import com.twl.meeboss.chat.module.boss.activity.callback.IBossUIClickCallback
import com.twl.meeboss.chat.module.boss.components.BossChatContent
import com.twl.meeboss.chat.module.boss.intent.BossChatUiIntent
import com.twl.meeboss.chat.module.boss.state.BossChatUiEvent
import com.twl.meeboss.chat.module.boss.viewmodel.BossChatViewModel
import com.twl.meeboss.chat.module.common.CommonEventObserver
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.common.preview.MessagePreviewProvider
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

@RouterPage(path = [ChatRouterPath.CHAT_BOSS_DETAIL_PAGE])
@AndroidEntryPoint
class BossChatActivity : BaseMviActivity<BossChatViewModel>() {

    private val mBossUIEventDispatcher: BossUIEventDispatcher by lazy {
        BossUIEventDispatcher(this, viewModel)
    }

    private lateinit var commonEventObserver: CommonEventObserver

    override val viewModel: BossChatViewModel by viewModels()

    override fun preInit(intent: Intent) {
        val friendId = intent.getStringExtra(BUNDLE_USER_ID).notNull()
        val friendIdentity = intent.getIntExtra(BUNDLE_USER_IDENTITY, UserConstants.NONE_IDENTITY)
        viewModel.params = (intent.getSerializableExtra(BUNDLE_OBJECT) as BossChatParams?) ?: BossChatParams()
        viewModel.chatSource = intent.getSerializableExtra(BUNDLE_SOURCE) as ChatSource
        viewModel.init(friendId, friendIdentity)
    }

    override fun initData() {
        mBossUIEventDispatcher.init()
        commonEventObserver = CommonEventObserver(this, viewModel.mFriendId)
        viewModel.conversationInitResult.observe(this){
            if (!it.isSystemUser()) {
                mBossUIEventDispatcher.initWhenConversationCompleted()
            }
        }
        registerVmEvent()
        registerEventBus()
    }

    private fun registerEventBus() {
        liveEventBusObserve(EventBusKey.SYNC_QUICK_REPLIES) { friendId: String ->
            if (friendId == viewModel.mFriendId) {
                viewModel.sendUiIntent(BossChatUiIntent.OnSyncQuickReply)
            }
        }
    }

    override fun onBackPressed() {
        viewModel.sendUiIntent(BossChatUiIntent.OnBackPressed)
        super.onBackPressed()
    }

    private fun registerVmEvent() {
        lifecycleScope.launch {
            viewModel.eventFlow.collect{
                when (it) {
                    is BossChatUiEvent.ShowVisaSponsorSyncDialog -> {
                        showVisaSponsorSyncDialog(it.status)
                    }
                }
            }
        }
    }

    @Composable
    override fun ComposeContent() {
        val messages = viewModel.messages.collectAsState().value.collectAsLazyPagingItems()
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        if(uiState.userStatus.isChatUserDeleted()){
            toastShort(uiState.userStatusPrompt)
            finish()
        }

        val chatImagePickerLauncher = rememberLauncherForActivityResult(SelectPictureContract()) { result ->
            if (!result.isNullOrEmpty()) {
                result.firstOrNull()?.let {
                    Log.d("PhotoPicker", "Chat image selected: ${it.uri}")
                    viewModel.sendUiIntent(BossChatUiIntent.BaseMessage.SendImage(it))
                } ?: Log.e("PhotoPicker", "Contract returned non-empty list but first item was null?")
            } else {
                Log.d("PhotoPicker", "Chat image selection cancelled or failed.")
            }
        }

        messages.run {
            BossChatContent(messages = messages,
                uiState = uiState,
                uiClickCallback = object : IBossUIClickCallback by mBossUIEventDispatcher {
                    override fun onInputStateChanged(intent: ChatInputIntent) {
                        if (intent == ChatInputIntent.SendImage) {
                            Log.d("PhotoPicker", "Chat SendImage intent received, launching picker.")
                            val input = PictureSelectInput(maxSelectionCount = 1, shouldCrop = false)
                            chatImagePickerLauncher.launch(input)
                        } else {
                            mBossUIEventDispatcher.onInputStateChanged(intent)
                        }
                    }
                }
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.onDestroy()
    }

    private fun showVisaSponsorSyncDialog(status: Int) {
        showConfirmDialog(
            title = R.string.employer_chat_visa_sponsor_answer_sync.toResourceString(),
            content = R.string.employer_chat_visa_sponsor_answer_sync_hint.toResourceString(),
            confirmText = R.string.employer_chat_visa_sponsor_answer_sync_yes.toResourceString(),
            cancelText = R.string.employer_chat_visa_sponsor_answer_sync_no.toResourceString(),
            onConfirm = {
                viewModel.sendUiIntent(BossChatUiIntent.OnVisaSponsorSyncConfirm(status))
                ChatPointReporter.sponsorPopupClick(if (status == 1) 1 else 0, viewModel.conversation?.jobId ?: "", 1)
            },
            onCancel = {
                ChatPointReporter.sponsorPopupClick(if (status == 1) 1 else 0, viewModel.conversation?.jobId ?: "", 0)
            }
        )
        ChatPointReporter.sponsorPopupShow(if (status == 1) 1 else 0, viewModel.conversation?.jobId ?: "")
    }

    companion object {
        fun startChat(
            context: Context,
            chatId: String,
            friendIdentity: Int,
            source: ChatSource,
            params: BossChatParams = BossChatParams()
        ) {
            ChatPageRouter.jumpToBossChat(context, chatId, friendIdentity, source, params)
        }
    }


}


@Preview
@Composable
private fun PreviewBossChatContent(@PreviewParameter(MessagePreviewProvider::class)
                                   flow: MutableStateFlow<PagingData<MessageModel<out ChatMessage>>>) {
    BossChatContent(messages = flow.collectAsLazyPagingItems())

}