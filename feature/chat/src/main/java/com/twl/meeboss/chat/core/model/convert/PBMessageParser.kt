package com.twl.meeboss.chat.core.model.convert

import android.annotation.SuppressLint
import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol
import cn.techwolf.international.washington.chat.protocol.WashingtonProtocol.ChatProtocol
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.ktx.report
import com.twl.meeboss.chat.core.constant.PBMessageType
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.EventMessage
import com.twl.meeboss.chat.core.model.message.PatchMessage
import com.twl.meeboss.chat.core.model.message.ReadMessage
import com.twl.meeboss.common.ktx.notNull

class PBMessageParser {
    private val tag = this::class.java.simpleName

    private var mMassageCallback: MessageParserCallback? = null

    fun setCallback(callback: MessageParserCallback) {
        this.mMassageCallback = callback
    }

    fun onReceive(data: ByteArray?) {
        data?.run {
            try {
                val hiChatProtocol = ChatProtocol.parseFrom(data)
                TLog.debug(tag, "onReceive protocol : %s", hiChatProtocol)

                when (hiChatProtocol.messageType) {
                    PBMessageType.PRESENCE -> { //出席

                    }

                    PBMessageType.GENERAL -> { //普通聊天消息，文本图片卡片等
                        parseGeneralMessage(hiChatProtocol.generalMessagesList, true)
                    }

                    PBMessageType.READ -> { //已读消息，同步已读状态
                        parseReadMessage(hiChatProtocol.readMessage)
                    }

                    PBMessageType.EVENT -> { //事件消息
                        parseEventMessage(hiChatProtocol.eventMessage)
                    }

                    PBMessageType.PATCH -> { //同步消息数据
                        parsePatchMessage(hiChatProtocol.patchMessagesList)
                    }
                }
            } catch (e: Exception) {
                e.report("pb_message_parser_exception")
            }
        }
    }

    /**
     * 普通消息
     */
    private fun parseGeneralMessage(messagesList: List<WashingtonProtocol.GeneralMessage>, isRealTimeMessage: Boolean) {
        val chatMessageList: MutableList<ChatMessage> = mutableListOf()
        for (ogMessage in messagesList) {
            val chatMessage: ChatMessage = PB2MessageConvert.parsePbMessage(ogMessage)
            chatMessageList.add(chatMessage)
        }
        TLog.debug(tag,"parseGeneralMessage chatMessageList size: ${chatMessageList.size},callback ==null? ${mMassageCallback == null}")
        mMassageCallback?.onMessage(chatMessageList, isRealTimeMessage)
    }

    private fun parseReadMessage(message: WashingtonProtocol.ReadMessage?){
        message?.run {
            ReadMessage(type = this.type,this.mid,userId = this.userId,identity =this.identity).apply {
                mMassageCallback?.onReadSync(this, true)
            }
        }
    }

    /**
     * 下发事件
     */
    @SuppressLint("WrongConstant")
    private fun parseEventMessage(message: WashingtonProtocol.EventMessage?) {
        val eventMessage = EventMessage(type = message?.type ?: 0, content = message?.content.notNull())
        mMassageCallback?.onEvent(eventMessage)
    }

    /**
     * 消息同步
     */
    private fun parsePatchMessage(messages: List<WashingtonProtocol.PatchMessage>?) {
        val list = mutableListOf<PatchMessage>()
        messages?.forEach { message ->
            list.add(PatchMessage(startMid = message.startMid, endMid = message.endMid, count = message.count))
        }
        if(list.isNotEmpty()){
            mMassageCallback?.onPatch(list)
        }else{
            TLog.info(tag,"parsePatchMessage messages is empty")
        }

    }

    interface MessageParserCallback {

        fun onMessage(messages: List<ChatMessage>, isRealTimeMessage: Boolean)

        fun onReadSync(readMessage: ReadMessage, isRealTimeMessage: Boolean)

        fun onEvent(message: EventMessage)

        fun onPatch(messages: List<PatchMessage>)

    }
}