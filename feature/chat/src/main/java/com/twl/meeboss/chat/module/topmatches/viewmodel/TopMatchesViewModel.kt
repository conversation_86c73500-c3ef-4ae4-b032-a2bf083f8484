package com.twl.meeboss.chat.module.topmatches.viewmodel

import androidx.lifecycle.viewModelScope
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.IUiEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toastErrorIfPresent
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.boss.export.BossServiceRouter
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.api.resp.toTopHighlyMatchedDetailModel
import com.twl.meeboss.chat.repos.ChatConversationRepository
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.export_share.model.CandidateResumeResult
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.model.JobTabBean
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedDetailModel
import com.twl.meeboss.geek.export.GeekServiceRouter
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TopMatchesViewModel @Inject constructor(
    private val chatRepository: ChatConversationRepository
) : BaseMviViewModel<TopMatchesUnifiedUiState, TopMatchesUiIntent>() {

    private val strategyContext: StrategyContext = object : StrategyContext {
        override fun getCurrentState(): TopMatchesUnifiedUiState = uiStateFlow.value
        override fun updateState(updater: (TopMatchesUnifiedUiState) -> TopMatchesUnifiedUiState) {
            sendUiState(updater)
        }

        override fun launchInIO(block: suspend CoroutineScope.() -> Unit) = viewModelScope.launch(Dispatchers.IO) {
            block()
        }


        override suspend fun getTopMatches(): List<TopHighlyMatchedDetailModel>? = <EMAIL>()

        override suspend fun sendEvent(event: TopMatchesEvent) = _eventFlow.emit(event)
    }

    private val strategy: TopMatchesStrategy by lazy {
        if (UserProvider.isGeek()) {
            GeekStrategy(strategyContext, chatRepository)
        } else {
            BossStrategy(strategyContext, chatRepository)
        }
    }

    override fun initUiState(): TopMatchesUnifiedUiState = strategy.initState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is TopMatchesUiIntent.InitIntent -> strategy.handleInitIntent(intent)
            is TopMatchesUiIntent.GetDetailIntent -> strategy.handleGetDetailIntent(intent)
            is TopMatchesUiIntent.NoInterestedIntent -> strategy.handleNoInterestedIntent(intent)
            is TopMatchesUiIntent.QuickMessageIntent -> strategy.handleQuickMessageIntent(intent)
            is TopMatchesUiIntent.CloseGeekSettingTabIntent -> strategy.handleCloseGeekSettingTabIntent(intent)
            is TopMatchesUiIntent.RemoveMatchIntent -> strategy.handleRemoveMatchIntent(intent)
            is TopMatchesUiIntent.GuideDialogVisibilityIntent -> sendUiState { copy(showGuideDialog = intent.show) }
            is TopMatchesUiIntent.NextGuideIntent -> nextGuide()
            is TopMatchesUiIntent.HighlightDialogVisibilityIntent -> sendUiState { copy(showHighlightTipDialog = intent.show) }
        }
    }

    private fun nextGuide() {
        sendUiState {
            when (guideState) {
                TopMatchesGuideState.GuideDialog -> copy(guideState = TopMatchesGuideState.SweepAnimal)
                TopMatchesGuideState.SweepAnimal -> copy(guideState = TopMatchesGuideState.FocusMask)
                TopMatchesGuideState.FocusMask -> {
                    strategy.markAnimPlayed()
                    copy(guideState = TopMatchesGuideState.None)
                }
                else -> this
            }
        }
    }

    private suspend fun getTopMatches(): List<TopHighlyMatchedDetailModel>? {
        val result = chatRepository.getTopMatchesDetail()
        if (result.isFailure) {
            result.exceptionOrNull()?.message?.let { T.ss(it) }
            return null
        }
        return result.getOrNull()?.map { it.toTopHighlyMatchedDetailModel() }
    }
}

sealed interface TopMatchesUiIntent : IUiIntent {
    data object InitIntent : TopMatchesUiIntent
    data class GetDetailIntent(val page: Int) : TopMatchesUiIntent
    data class NoInterestedIntent(val page: Int) : TopMatchesUiIntent
    data class QuickMessageIntent(val page: Int, val greetingSettings: Boolean) : TopMatchesUiIntent
    data class RemoveMatchIntent(val page: Int) : TopMatchesUiIntent
    data class CloseGeekSettingTabIntent(val tabBean: JobTabBean) : TopMatchesUiIntent
    data class GuideDialogVisibilityIntent(val show: Boolean) : TopMatchesUiIntent
    data class HighlightDialogVisibilityIntent(val show: Boolean) : TopMatchesUiIntent
    data object NextGuideIntent : TopMatchesUiIntent
}

// 统一数据模型
sealed interface MatchSpecificDetail
data class JobSpecificDetail(val jobDetail: JobDetailResult?) : MatchSpecificDetail
data class CandidateSpecificDetail(val candidateResume: CandidateResumeResult?) : MatchSpecificDetail

/**
 * 状态
 */
data class TopMatchesUnifiedUiState(
    val guideState: TopMatchesGuideState,
    val matches: List<Pair<TopHighlyMatchedDetailModel, MatchSpecificDetail?>>,
    val showGuideDialog: Boolean = false,
    val showHighlightTipDialog: Boolean = false,
    val isGeek: Boolean = UserProvider.isGeek()
) : IUiState

sealed class TopMatchesEvent: IUiEvent {
    data class ShowFirstMessageDialog(val friendId: String, val jobId: String, val page: Int) : TopMatchesEvent()
}

// 策略接口
interface TopMatchesStrategy {
    /**
     * 初始化状态
     */
    fun initState(): TopMatchesUnifiedUiState
    fun handleInitIntent(intent: TopMatchesUiIntent.InitIntent)
    fun handleGetDetailIntent(intent: TopMatchesUiIntent.GetDetailIntent)
    fun handleNoInterestedIntent(intent: TopMatchesUiIntent.NoInterestedIntent)
    fun handleQuickMessageIntent(intent: TopMatchesUiIntent.QuickMessageIntent)
    fun handleCloseGeekSettingTabIntent(intent: TopMatchesUiIntent.CloseGeekSettingTabIntent) {}
    fun handleRemoveMatchIntent(intent: TopMatchesUiIntent.RemoveMatchIntent)
    fun markAnimPlayed()
}

interface StrategyContext {
    fun getCurrentState(): TopMatchesUnifiedUiState
    fun updateState(updater: (currentState: TopMatchesUnifiedUiState) -> TopMatchesUnifiedUiState)
    fun launchInIO(block: suspend CoroutineScope.() -> Unit): Job
    suspend fun getTopMatches(): List<TopHighlyMatchedDetailModel>?
    suspend fun sendEvent(event: TopMatchesEvent)
}

sealed class TopMatchesGuideState{
    data object None : TopMatchesGuideState()
    data object GuideDialog : TopMatchesGuideState()
    data object SweepAnimal : TopMatchesGuideState()
    data object FocusMask : TopMatchesGuideState()
}

// Geek策略实现
class GeekStrategy(
    private val context: StrategyContext,
    private val chatRepository: ChatConversationRepository
) : TopMatchesStrategy {
    private var initJob : Job? = null

    override fun initState(): TopMatchesUnifiedUiState {
        return TopMatchesUnifiedUiState(
            guideState = if (chatRepository.hasShowedGeekTopHighlyMatchedGuide) TopMatchesGuideState.None else TopMatchesGuideState.GuideDialog,
            isGeek = true,
            matches = listOf(TopHighlyMatchedDetailModel("", "") to null)
        )
    }

    override fun handleInitIntent(intent: TopMatchesUiIntent.InitIntent) {
        initJob = context.launchInIO {
            val topMatchesModels = context.getTopMatches() ?: emptyList()
            val newMatches = topMatchesModels.map { it to null } // 初始时，详情为 null
            context.updateState { currentState ->
                currentState.copy(matches = newMatches)
            }
            ChatPointReporter.f2TabTopMatches(topMatchesModels.size)
        }
    }

    override fun handleGetDetailIntent(intent: TopMatchesUiIntent.GetDetailIntent) {
        context.launchInIO {
            initJob?.join()
            val currentState = context.getCurrentState()
            val securityId = currentState.matches.getOrNull(intent.page)?.first?.securityId?.takeIf { it.isNotEmpty() } ?: return@launchInIO
            val detail = requestJobDetailFromService(securityId)

            context.updateState { cs ->
                val updatedMatches = cs.matches.mapIndexed { index, pair ->
                    if (index == intent.page) {
                        pair.first to JobSpecificDetail(detail)
                    } else {
                        pair
                    }
                }
                cs.copy(matches = updatedMatches)
            }
        }
    }

    override fun handleNoInterestedIntent(intent: TopMatchesUiIntent.NoInterestedIntent) {
        context.launchInIO {
            val thePageDetail = getThePageDetail(intent.page)
            ChatPointReporter.topMatchesJobClick(thePageDetail?.jobDetailBossInfo?.userId ?: "", thePageDetail?.jobDetailJobInfo?.jobId ?: "", 2)

            val securityId = thePageDetail?.securityId ?: return@launchInIO
            val result = chatRepository.noInterestedMatch(securityId)

            if (result.isFailure) {
                result.toastErrorIfPresent()
            } else {
                context.updateState { currentState ->
                    val updatedMatches = currentState.matches.filterIndexed { index, _ -> index != intent.page }
                    currentState.copy(matches = updatedMatches)
                }
                T.ss(R.string.top_matches_not_interested_marked_successfully_toast)
            }


        }
    }

    override fun handleQuickMessageIntent(intent: TopMatchesUiIntent.QuickMessageIntent) {
        context.launchInIO {
            val matchedInfo = getThePageDetail(intent.page)
            ChatPointReporter.topMatchesJobClick(matchedInfo?.jobDetailBossInfo?.userId ?: "", matchedInfo?.jobDetailJobInfo?.jobId ?: "", 1)

            if (!intent.greetingSettings) {
                removeMatch(intent.page)
                T.ss(R.string.top_matches_quick_message_sent_successfully_toast)
            } else {
                matchedInfo ?: return@launchInIO
                context.sendEvent(TopMatchesEvent.ShowFirstMessageDialog(matchedInfo.jobDetailBossInfo?.userId ?: "", matchedInfo.jobDetailJobInfo?.jobId ?: "", intent.page))
            }
        }
    }

    override fun handleCloseGeekSettingTabIntent(intent: TopMatchesUiIntent.CloseGeekSettingTabIntent) {
        context.launchInIO {
            val result = GeekServiceRouter.geekSettingCloseTab(intent.tabBean)
            if (result.isFailure) {
                result.toastErrorIfPresent()
                return@launchInIO
            }
            context.updateState { currentState ->
                val updatedMatches = currentState.matches.map { pair ->
                    val jobDetailSpecific = pair.second as? JobSpecificDetail
                    if (jobDetailSpecific != null) {
                        pair.first to JobSpecificDetail(jobDetailSpecific.jobDetail?.copy(tab = null))
                    } else {
                        pair
                    }
                }
                currentState.copy(matches = updatedMatches)
            }
        }
    }

    override fun handleRemoveMatchIntent(intent: TopMatchesUiIntent.RemoveMatchIntent) {
        removeMatch(intent.page)
    }

    override fun markAnimPlayed() {
        chatRepository.hasShowedGeekTopHighlyMatchedGuide = true
    }

    private fun removeMatch(page: Int) {
        context.updateState { currentState ->
            val updatedMatches = currentState.matches.filterIndexed { index, _ -> index != page }
            currentState.copy(matches = updatedMatches)
        }
    }

    private suspend fun requestJobDetailFromService(securityId: String): JobDetailResult? {
        val result = GeekServiceRouter.requestJobDetail(securityId)
        if (result.isFailure) {
            result.exceptionOrNull()?.message?.let { T.ss(it) }
        }

        return result.getOrNull()
    }

    private fun getThePageDetail(page: Int)  = (context.getCurrentState().matches.getOrNull(page)?.second as? JobSpecificDetail)?.jobDetail
}

// Boss策略实现
class BossStrategy(
    private val context: StrategyContext,
    private val chatRepository: ChatConversationRepository // 示例依赖，可能需要Boss自己的Repository
) : TopMatchesStrategy {
    private var initJob : Job? = null

    override fun initState(): TopMatchesUnifiedUiState {
        return TopMatchesUnifiedUiState(
            guideState = if (chatRepository.hasShowedBossTopHighlyMatchedGuide) TopMatchesGuideState.None else TopMatchesGuideState.GuideDialog,
            isGeek = false,
            matches = listOf(TopHighlyMatchedDetailModel("", "") to null)
        )
    }

    override fun handleInitIntent(intent: TopMatchesUiIntent.InitIntent) {
        initJob = context.launchInIO {
            val topMatchesModels = context.getTopMatches() ?: emptyList()
            val newMatches = topMatchesModels.map { it to null } // 初始时，详情为 null
            context.updateState { currentState -> currentState.copy(matches = newMatches) }
            ChatPointReporter.f2TabTopMatches(topMatchesModels.size)
        }
    }

    override fun handleGetDetailIntent(intent: TopMatchesUiIntent.GetDetailIntent) {
        context.launchInIO {
            initJob?.join()
            val currentState = context.getCurrentState()
            val securityId = currentState.matches.getOrNull(intent.page)?.first?.securityId?.takeIf { it.isNotEmpty() } ?: return@launchInIO
            val candidateResume = getCandidateResume(securityId)

            context.updateState { cs ->
                val updatedMatches = cs.matches.mapIndexed { index, pair ->
                    if (index == intent.page) {
                        pair.first to CandidateSpecificDetail(candidateResume)
                    } else {
                        pair
                    }
                }
                cs.copy(matches = updatedMatches)
            }
        }
    }

    override fun handleNoInterestedIntent(intent: TopMatchesUiIntent.NoInterestedIntent) {
        context.launchInIO {
            val pageDetail = getThePageDetail(intent.page)
            ChatPointReporter.topMatchesGeekClick(pageDetail?.friendId ?: "", pageDetail?.jobId ?: "",2)

            val securityId = pageDetail?.securityId ?: return@launchInIO
            val result = chatRepository.noInterestedMatch(securityId)

            if (result.isFailure) {
                result.toastErrorIfPresent()
            } else {
                context.updateState { currentState ->
                    val updatedMatches = currentState.matches.filterIndexed { index, _ -> index != intent.page }
                    currentState.copy(matches = updatedMatches)
                }
                val friendId = pageDetail.friendId
                sendStringLiveEvent(EventBusKey.NOT_INTERESTED_IN_CANDIDATE, friendId)
                T.ss(R.string.top_matches_not_interested_marked_successfully_toast)
            }
        }
    }

    override fun handleQuickMessageIntent(intent: TopMatchesUiIntent.QuickMessageIntent) {
        context.launchInIO {
            val pageDetail = getThePageDetail(intent.page)
            ChatPointReporter.topMatchesGeekClick(pageDetail?.friendId ?: "",pageDetail?.jobId ?: "",1)

            if (!intent.greetingSettings) {
                removeMatch(intent.page)
                T.ss(R.string.top_matches_quick_message_sent_successfully_toast)
            } else {
                val matchedInfo = pageDetail ?: return@launchInIO
                context.sendEvent(TopMatchesEvent.ShowFirstMessageDialog(matchedInfo.friendId, matchedInfo.jobId ?: "", intent.page))
            }
        }
    }

    override fun handleRemoveMatchIntent(intent: TopMatchesUiIntent.RemoveMatchIntent) {
        removeMatch(intent.page)
    }

    override fun markAnimPlayed() {
        chatRepository.hasShowedBossTopHighlyMatchedGuide = true
    }

    private fun removeMatch(page: Int) {
        context.updateState { currentState ->
            val updatedMatches = currentState.matches.filterIndexed { index, _ -> index != page }
            currentState.copy(matches = updatedMatches)
        }
    }

    private suspend fun getCandidateResume(securityId: String): CandidateResumeResult? {
        val result = BossServiceRouter.getCandidateResume(securityId)
        if (result.isFailure) {
            result.toastErrorIfPresent()
        }
        return result.getOrNull()
    }

    private fun getThePageDetail(page: Int)  = (context.getCurrentState().matches.getOrNull(page)?.second as? CandidateSpecificDetail)?.candidateResume

}