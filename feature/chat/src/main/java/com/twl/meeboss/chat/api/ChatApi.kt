package com.twl.meeboss.chat.api

import com.twl.meeboss.base.model.PageList
import com.twl.meeboss.chat.api.resp.QuickReplyTemplateItem
import com.twl.meeboss.chat.api.resp.ReportReasonTypeResult
import com.twl.meeboss.chat.api.resp.TopHighlyMatchedResp
import com.twl.meeboss.chat.api.resp.TopHighlyMatchesDetailResp
import com.twl.meeboss.chat.core.model.MessageFromNetResult
import com.twl.meeboss.chat.core.model.PaasInfoResult
import com.twl.meeboss.chat.core.model.PatchMessageNetResult
import com.twl.meeboss.chat.export.constant.ReportScene
import com.twl.meeboss.chat.module.common.file.model.FileDownloadInfo
import com.twl.meeboss.chat.module.phrase.model.ChatPhrasesExampleResult
import com.twl.meeboss.chat.module.phrase.model.ChatPhrasesResult
import com.twl.meeboss.chat.module.notification.model.SystemNotificationItemResult
import com.twl.meeboss.core.network.HttpResult
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface ChatApi {
    @GET("api/chat/listMsg")
    suspend fun chatMessageList(
        @Query("startMsgId") startMsgId: Long,
        @Query("endMsgId") endMsgId: Long,
    ): HttpResult<MessageFromNetResult>

    @GET("api/report/listReasonType")
    suspend fun getReportReasonTypeList(): HttpResult<ReportReasonTypeResult>

    @FormUrlEncoded
    @POST("api/report/chat")
    suspend fun reportFriend(
        @Field("reasonType") reasonType: Int,
        @Field("additionalInfo") additionalInfo: String?,
        @Field("securityId") securityId: String,
        @Field("scene") scene:@ReportScene Int
    ): HttpResult<Any>

    @GET("api/notification/list")
    suspend fun getNotificationList(
        @Query("pageSize") pageSize: Int,
        @Query("lastId") lastId: String?,
    ): HttpResult<PageList<SystemNotificationItemResult>>

    @FormUrlEncoded
    @POST("/api/notification/updateRead")
    suspend fun notificationUpdateRead(
        @Field("notificationIds") notificationIds: String?,
    ): HttpResult<Any>

    @GET("api/chat/countMsg")
    suspend fun chatCountMessage(
        @Query("lastConfirmedMid") lastConfirmedMid: Long,
        @Query("lastReceivedMid") lastReceivedMid: Long,
    ): HttpResult<PatchMessageNetResult>

    @FormUrlEncoded
    @POST("api/chat/resend")
    suspend fun resend(@Field("msgId") msgId: Long): HttpResult<Any>

    @GET("api/chat/commonPhrases/example")
    suspend fun getCommonPhrasesExample(): HttpResult<ChatPhrasesExampleResult>
    @GET("api/chat/commonPhrases")
    suspend fun getCommonPhrases(): HttpResult<ChatPhrasesResult>
    @FormUrlEncoded
    @POST("api/chat/commonPhrases/update")
    suspend fun saveCommonPhrases(
        @Field("id") id:String? = null,
        @Field("text") text:String? = null,
    ): HttpResult<Any>
    @FormUrlEncoded
    @POST("api/chat/commonPhrases/delete")
    suspend fun deleteCommonPhrases(
        @Field("id") id:String,
    ): HttpResult<Any>
    @FormUrlEncoded
    @POST("api/chat/commonPhrases/sort")
    suspend fun sortCommonPhrases(
        @Field("ids") ids:String,
    ): HttpResult<Any>

    /**
     * 上报系统通知状态
     */
    @FormUrlEncoded
    @POST("api/notification/system/preference/update")
    suspend fun systemNotificationUpdate(
        @Field("openNotification") openNotification:Boolean,
    ): HttpResult<Any>

    @GET("api/top-matched/show")
    suspend fun requestTopMatchedShow(): HttpResult<TopHighlyMatchedResp>

    @POST("api/top-matched/detail")
    suspend fun requestTopMatchedDetail(): HttpResult<List<TopHighlyMatchesDetailResp>>

    @FormUrlEncoded
    @POST("api/top-matched/notInterested")
    suspend fun requestTopMatchedNotInterested(@Field("securityId") securityId: String): HttpResult<Any>

    @GET("api/chat/v2/config/quickReplyTemplate")
    suspend fun getQuickReplyTemplate(
        @Query("friendId") friendId: String,
        @Query("friendIdentity") friendIdentity: Int,
        @Query("jobId") jobId: String,
    ): HttpResult<List<QuickReplyTemplateItem>>

    @FormUrlEncoded
    @POST("api/file/upload/getDownloadAndPreviewUrls")
    suspend fun getUrlWithExpire(@Field("key") key: String): HttpResult<FileDownloadInfo>

    /**
     * 获取PAAS TOKEN
     */
    @POST("api/user/getPaasInfo")
    suspend fun getPaasInfo(): HttpResult<PaasInfoResult>

    @FormUrlEncoded
    @POST("api/chat/v1/config/quickReplyTemplateClick")
    suspend fun quickReplyTemplateClick(
        @Field("friendId") friendId: String,
        @Field("friendIdentity") friendIdentity: Int,
        @Field("type") type: Int,
        @Field("scene") scene: String,
        @Field("mid") mid: Long,
    ): HttpResult<Any>

}