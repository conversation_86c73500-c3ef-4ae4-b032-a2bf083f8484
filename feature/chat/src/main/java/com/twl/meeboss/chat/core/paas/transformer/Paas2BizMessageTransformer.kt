package com.twl.meeboss.chat.core.paas.transformer

import com.bzl.im.message.MessageConstants
import com.bzl.im.message.attachment.SimpleAttachment
import com.bzl.im.message.attachment.attachdata.BigEmojiAttachData
import com.bzl.im.message.attachment.attachdata.FileAttachData
import com.bzl.im.message.attachment.attachdata.ImageAttachData
import com.bzl.im.message.attachment.attachdata.TextAttachData
import com.bzl.im.message.attachment.attachdata.TextElement
import com.bzl.im.message.model.BIMessage
import com.twl.meeboss.base.ktx.isMySelf
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.FileBody
import com.twl.meeboss.chat.core.model.message.FileMessage
import com.twl.meeboss.chat.core.model.message.NoSupportMessage
import com.twl.meeboss.chat.core.model.message.PictureBody
import com.twl.meeboss.chat.core.model.message.PictureMessage
import com.twl.meeboss.chat.core.model.message.RichElement
import com.twl.meeboss.chat.core.model.message.TextBody
import com.twl.meeboss.chat.core.model.message.TextMessage
import com.twl.meeboss.chat.core.paas.MeeBossBizAttachment
import com.twl.meeboss.chat.export.constant.GeneralMsgType


fun BIMessage.toChatMessage(): ChatMessage = when (getMediaType()) {
    MessageConstants.MESSAGE_TYPE_TEXT, MessageConstants.MESSAGE_TYPE_BIG_EMOJI -> TextMessage().apply {
        textBody = convertTextBody()
    }

    MessageConstants.MESSAGE_TYPE_IMAGE -> PictureMessage().apply {
        pictureBody = convertPictureBody()
    }

    MessageConstants.MESSAGE_TYPE_FILE -> FileMessage().apply {
        fileBody = convertFileBody()
    }

    else -> NoSupportMessage()
}.apply(this)

private fun ChatMessage.apply(biMessage: BIMessage): ChatMessage {
    bodyType = biMessage.getMediaType().toGeneralType()
    mid = biMessage.getMid()
    cmid = biMessage.getCMid()
    fromId = biMessage.getSenderId()
    fromIdentity = biMessage.getSenderIdentity().toInt()
    toId = biMessage.getReceiverId()
    toIdentity = biMessage.getReceiverIdentity().toInt()
    addTime = biMessage.getTime()
    status = biMessage.getStatus()
    //如果是自己发的消息，chatId就是对方的id，否则就是自己的id,但是需要保证,fromId和toId是自己或者对方的id
    chatId = if (fromId.isMySelf()) toId else fromId
    biMessage.getBizAttachment()?.let {
        if (it is MeeBossBizAttachment) {
            val data = it.getData()
            countable = data.countable
            deleted = data.deleted
            visible = data.visible
            failCode = data.failCode
            status = data.status
        }
    }
    return this
}

private fun Int.toGeneralType() = when (this) {
    MessageConstants.MESSAGE_TYPE_TEXT, MessageConstants.MESSAGE_TYPE_BIG_EMOJI -> GeneralMsgType.MSG_TEXT
    MessageConstants.MESSAGE_TYPE_IMAGE -> GeneralMsgType.MSG_PIC
    MessageConstants.MESSAGE_TYPE_FILE -> GeneralMsgType.MSG_FILE
    else -> GeneralMsgType.MSG_EMPTY
}

private fun BIMessage.convertTextBody() = when {
    getMediaType() == MessageConstants.MESSAGE_TYPE_TEXT -> {
        val textAttachData = (getMediaContent() as SimpleAttachment<TextAttachData>).attachdata
        TextBody(
            content = textAttachData?.text ?: "",
            richElementList = textAttachData?.richElements?.map {
                RichElement(
                    elementType = it.elementType,
                    originText = it.originText,
                    enhancedText = it.enhancedText,
                )
            }
        )
    }

    getMediaType() == MessageConstants.MESSAGE_TYPE_BIG_EMOJI -> {
        val attachData = (getMediaContent() as SimpleAttachment<BigEmojiAttachData>).attachdata
        TextBody(
            url = attachData?.url,
            type = attachData?.emojiType ?: 1,
            content = attachData?.content ?: ""
        )
    }

    else -> {
        TextBody()
    }
}


private fun BIMessage.convertPictureBody(): PictureBody {
    if (getMediaType() == MessageConstants.MESSAGE_TYPE_IMAGE) {
        val attachData = (getMediaContent() as SimpleAttachment<ImageAttachData>).attachdata
        return PictureBody(
            tinyUrl = attachData?.tinyImage?.url ?: "",
            tinyWith = attachData?.tinyImage?.width ?: 0,
            tinyHeight = attachData?.tinyImage?.height ?: 0,
            url = attachData?.originImage?.url ?: "",
            width = attachData?.originImage?.width ?: 0,
            height = attachData?.originImage?.height ?: 0,
        )
    }
    return PictureBody()
}

private fun BIMessage.convertFileBody(): FileBody {
    if (getMediaType() == MessageConstants.MESSAGE_TYPE_FILE) {
        val attachData = (getMediaContent() as SimpleAttachment<FileAttachData>).attachdata
        return FileBody(
            fileType = attachData?.fileType ?: 0,
            name = attachData?.name ?: "",
            size = (attachData?.size ?: 0L).toInt(),
            key = attachData?.key ?: "",
            md5 = attachData?.md5 ?: ""
        )

    }
    return FileBody()
}