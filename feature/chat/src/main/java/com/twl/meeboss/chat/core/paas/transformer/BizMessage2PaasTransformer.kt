package com.twl.meeboss.chat.core.paas.transformer

import com.bzl.im.message.MessageBuilder
import com.bzl.im.message.attachment.attachdata.ImageAttachData
import com.bzl.im.message.model.BIMessage
import com.bzl.im.message.model.BIMessageImpl
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.PictureBody
import com.twl.meeboss.chat.core.model.message.PictureMessage
import com.twl.meeboss.chat.core.model.message.TextBody
import com.twl.meeboss.chat.core.model.message.TextMessage
import com.twl.meeboss.chat.export.constant.LocalMessageType


fun ChatMessage.toPaasMessage(): BIMessage {
    return when (localMessageType) {
        LocalMessageType.MSG_TEXT -> {
            if (this is TextMessage && textBody?.type == 1) {
                this.toEmojiPaasMessage()
            } else {
                (this as TextMessage).toTextPaasMessage()
            }
        }

        LocalMessageType.MSG_PIC -> (this as PictureMessage).toImagePaasMessage()
        else -> BIMessageImpl()
    }.also {
        it.setMid(cmid)
        it.setCMid(cmid)
        it.setTime(cmid)
    }
}

fun TextMessage.toTextPaasMessage(): BIMessageImpl {
    val textBody = textBody ?: TextBody()
    return MessageBuilder.createTextMessage(
        sender = getSender(),
        receiver = getReceiver(),
        text = textBody.content
    )
}

fun PictureMessage.toImagePaasMessage(): BIMessageImpl {
    val pictureBody = pictureBody ?: PictureBody()
    return MessageBuilder.createImageMessage(
        sender = getSender(),
        receiver = getReceiver(),
        tinyImage = ImageAttachData.ChatImageInfoBean(
            url = pictureBody.tinyUrl,
            width = pictureBody.tinyWith,
            height = pictureBody.tinyHeight,
            imgId = 0L
        ),
        originImage = ImageAttachData.ChatImageInfoBean(
            url = pictureBody.url,
            width = pictureBody.width,
            height = pictureBody.height,
            imgId = 0L
        ),
        null,
        toId
    )
}


fun TextMessage.toEmojiPaasMessage(): BIMessageImpl {
    val textBody = textBody ?: TextBody()
    return MessageBuilder.createEmojiMessage(
        sender = getSender(),
        receiver = getReceiver(),
        url = textBody.url?:"",
        content = textBody.content,
        emojiType = textBody.type
    )
}

fun ChatMessage.getSender() = MessageBuilder.MessageSender(
    uid = fromId,
    identity = fromIdentity.toString(),
    source = "3"
)

fun ChatMessage.getReceiver() = MessageBuilder.MessageReceiver(
    chatId = toId,
    identity = toIdentity.toString(),
    source = "3"
)
