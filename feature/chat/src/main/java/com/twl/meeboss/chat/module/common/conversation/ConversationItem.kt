package com.twl.meeboss.chat.module.common.conversation

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.ktx.buildFullNameOrUnknown
import com.twl.meeboss.base.ktx.isSystem
import com.twl.meeboss.base.ktx.to99Plus
import com.twl.meeboss.base.ktx.toFormatConversationTime
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.uitls.StickerHelper
import com.twl.meeboss.chat.export.ChatServiceRouter
import com.twl.meeboss.chat.export.constant.FriendStatus
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.module.common.components.chatitems.stickerContentMaps
import com.twl.meeboss.chat.module.common.conversation.preview.PreviewConversationsProvider
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.vibrate
import com.twl.meeboss.core.ui.layer.Layer
import com.twl.meeboss.core.ui.layer.LayerContainer
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.COLOR_BD222B
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.theme.Secondary

@Composable
fun ConversationItem(
    modifier: Modifier = Modifier,
    conversation: Conversation,
    layer: Layer = conversationPopMenu(),
    onClick: (IntOffset) -> Unit = {}
) {
    val context = LocalContext.current
    val onClickUpdated by rememberUpdatedState(newValue = onClick)
    var layoutCoordinates: LayoutCoordinates? by remember { mutableStateOf(null) }

    Row(modifier = modifier
        .onGloballyPositioned {
            layoutCoordinates = it
        }
        .pointerInput(layer, context) {
            detectTapGestures(
                onPress = {
                    layer.detach()
                },
                onTap = {
                    ChatServiceRouter.startChat(
                        context = context as FragmentActivity,
                        friendId = conversation.friendId,
                        friendIdentity = conversation.friendIdentity,
                        source = ChatSource.ContactList
                    )
                },
                onLongPress = if (conversation.isSystemUser()) {
                    null
                } else {
                    {
                        val layout = layoutCoordinates
                        if (layout?.isAttached == true) {
                            val offset = layout.localToWindow(it)
                            onClickUpdated(IntOffset(offset.x.toInt(), offset.y.toInt()))
                            context.vibrate()
                        }
                    }
                }
            )
        }
    ) {
        ConversationAvatar(conversation)

        Column(modifier = Modifier
            .weight(1F)
            .padding(top = 16.dp, start = 10.dp)
        ) {
            ConversationTitleRow(
                context = context,
                conversation = conversation,
                highlightTopMatches = false
            )
            //职位
            ConversationSubtitleRow(
                text = conversation.getUserInfoInConversation()
            )
            LastMessageContentWrapper(
                conversation = conversation
            )
        }
        Spacer(modifier = Modifier.width(16.dp))

    }
}

@Composable
private fun ConversationAvatar(conversation: Conversation) {
    Box(
        modifier = Modifier
            .padding(start = 16.dp, top = 16.dp, bottom = 16.dp)
    ) {
        GlideImage(
            imageModel = { conversation.tinyAvatar ?: conversation.avatar.notNull() },
            imageOptions = ImageOptions(contentDescription = "user avatar"),
            previewPlaceholder = painterResource(id = R.mipmap.base_avatar_placeholder),
            component = rememberImageComponent {
                +PlaceholderPlugin.Loading(painterResource(id = R.mipmap.base_avatar_placeholder))
                +PlaceholderPlugin.Failure(painterResource(id = R.mipmap.base_avatar_placeholder))
            },
            modifier = Modifier
                .padding(end = 2.dp)
                .size(58.dp)
                .clip(CircleShape)
        )
        if (conversation.unreadCount > 0) {
            Row(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .clip(RoundedCornerShape(24.dp))
                    .background(Color.Red)
                    .border(1.dp, Color.White, RoundedCornerShape(24.dp))
            ) {
                Text(
                    maxLines = 1,
                    fontSize = 10.sp,
                    lineHeight = 16.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(horizontal = 5.dp)
                        .defaultMinSize(minWidth = 6.dp)
                        .height(16.dp),
                    text = conversation.unreadCount.to99Plus()
                )
            }
        }
    }
}

@Composable
private fun ConversationTitleRow(context: Context, conversation: Conversation, highlightTopMatches: Boolean) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Row(modifier = Modifier.weight(1F)) {
            // 姓名
            Text(
                modifier = Modifier.weight(1F, fill = false),
                text = buildFullNameOrUnknown(context, conversation.firstName, conversation.lastName),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222
                )
            )
            if (highlightTopMatches && conversation.topMatched) {
                Image(
                    painter = painterResource(R.drawable.chat_ic_conversation_top_matched),
                    contentDescription = stringResource(R.string.messages_list_filter_top_matches),
                    modifier = Modifier
                        .padding(start = 6.dp)
                        .size(20.dp)
                )
            }
        }

        // 时间
        Text(
            text = conversation.updateTime.toFormatConversationTime(context),
            style = TextStyle(
                fontSize = 12.sp,
                lineHeight = 18.sp,
                fontWeight = FontWeight.Normal,
                color = GRAY_AAAAAA
            ),
            modifier = Modifier.padding(start = 20.dp)
        )
    }
}

@Composable
private fun ConversationSubtitleRow(text: String) {
    Text(
        text = text,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        style = TextStyle(
            fontSize = 13.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight(400),
            color = Black888888,
        )
    )
}

@Composable
fun LastMessageContentWrapper(modifier: Modifier = Modifier, conversation: Conversation) {
    when {
        conversation.draft.isNotBlank() -> { //如果草稿不为空
            LastMessageContent(modifier = modifier, preTextColor = COLOR_BD222B, preText = stringResource(id = com.twl.meeboss.chat.R.string.chat_draft), content = conversation.draft)
        }

        conversation.lastMessageIsMine -> { //我自己发送的消息
            if (LocalMessageType.isSendByClient(conversation.lastMessageLocalType)) { //必须是我本地发出的
                when (conversation.lastMessageStatus) {
                    MessageStatus.SENDING -> {
                        LastMessageContent(modifier,
                            preText = stringResource(id = com.twl.meeboss.chat.R.string.chat_sending),
                            preTextColor = Black888888,
                            content = conversation.lastMessageContent)
                    }

                    MessageStatus.READ -> {
                        LastMessageContent(modifier,
                            preText = stringResource(id = R.string.common_read),
                            preTextColor = Black888888,
                            content = conversation.lastMessageContent)
                    }

                    MessageStatus.FAILED -> {
                        LastMessageContent(modifier,
                            preText = stringResource(id = com.twl.meeboss.chat.R.string.chat_send_failed),
                            preTextColor = COLOR_BD222B,
                            content = conversation.lastMessageContent)
                    }

                    MessageStatus.SEND, MessageStatus.RECEIVE -> {
                        LastMessageContent(modifier,
                            preText = stringResource(id = R.string.common_sent),
                            preTextColor = Black888888,
                            content = conversation.lastMessageContent)
                    }

                    else -> { //未知状态，不显示状态
                        LastMessageContent(content = conversation.lastMessageContent)
                    }

                }
            } else { //不是本地客户端发的，也就是服务器模拟发出来的
                LastMessageContent(content = conversation.lastMessageContent)
            }
        }

        else -> {
            when {
                //new greeting,最近一条消息是对方的，且我还没有回复
                conversation.friendStatus == FriendStatus.NEW_GREETING -> {
                    LastMessageContent(modifier,
                        preText = stringResource(id = R.string.common_new_greeting_singular),
                        preTextColor = Black888888,
                        content = conversation.lastMessageContent)
                }
                //交换简历
                conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_REQUEST_RESUME ||
                        conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_RESUME_RESULT ||
                        conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_SEND_RESUME_REQUEST -> {
                    LastMessageContent(modifier,
                        preText = stringResource(id = R.string.common_resume),
                        preTextColor = Secondary,
                        content = conversation.lastMessageContent)
                }
                //交换邮箱
                conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_REQUEST_EMAIL ||
                        conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_EMAIL_RESULT -> {
                    LastMessageContent(modifier,
                        preText = stringResource(id = R.string.common_email),
                        preTextColor = Secondary,
                        content = conversation.lastMessageContent)
                }
                //交换电话
                conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_REQUEST_PHONE ||
                        conversation.lastMessageLocalType == LocalMessageType.EXCHANGE_PHONE_RESULT -> {
                    LastMessageContent(modifier,
                        preText = stringResource(id = R.string.common_phone_number),
                        preTextColor = Secondary,
                        content = conversation.lastMessageContent)
                }
                //感兴趣灰条
                conversation.lastMessageLocalType == LocalMessageType.INTEREST_GRAY_HINT -> {
                    LastMessageContent(modifier,
                        preText = stringResource(id = R.string.common_interested),
                        preTextColor = Secondary,
                        content = conversation.lastMessageContent)
                }

                else -> {
                    LastMessageContent(content = conversation.lastMessageContent)
                }
            }
        }
    }
}

@Composable
fun LastMessageContent(modifier: Modifier = Modifier, preTextColor: Color = Black222222, preText: String = "", content: String) {
    val context = LocalContext.current
    Row(modifier = modifier
        .fillMaxWidth()
        .padding(top = 5.dp)) {
        if (preText.isNotBlank()) {
            Text(
                text = "[${preText}]",
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight.Normal,
                    color = preTextColor
                )
            )
            Spacer(modifier = Modifier.width(6.dp))
        }

        //                Spacer(modifier =Modifier.width(3.5.dp))
        Text(
            modifier = Modifier.padding(end = 6.dp),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            text = StickerHelper.buildAnnotatedString(content),
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 20.sp,
                fontWeight = FontWeight.Normal,
                color = Black222222
            ),
            inlineContent = stickerContentMaps
        )
    }
}

@Composable
fun BossConversationItem(
    jobs: Map<String, String>,
    conversation: Conversation,
    modifier: Modifier = Modifier,
    onLongPress: () -> Unit = {}
) {
    val context = LocalContext.current
    Row(modifier = modifier
        .pointerInput(context) {
            detectTapGestures(
                onTap = {
                    ChatServiceRouter.startChat(
                        context = context as FragmentActivity,
                        friendId = conversation.friendId,
                        friendIdentity = conversation.friendIdentity,
                        source = ChatSource.ContactList
                    )
                },
                onLongPress = if (conversation.isSystemUser()) {
                    null
                } else {
                    { onLongPress() }
                }
            )
        },
        verticalAlignment = Alignment.CenterVertically
    ) {
        ConversationAvatar(conversation)

        Column(modifier = Modifier
            .weight(1F)
            .padding(start = 10.dp)
        ) {
            ConversationTitleRow(
                context = context,
                conversation = conversation,
                highlightTopMatches = true
            )
            val subtitle = if (conversation.friendIdentity.isSystem()) {
                conversation.getUserInfoInConversation()
            } else {
                jobs[conversation.jobId]
            }
            if (subtitle != null) {
                ConversationSubtitleRow(text = subtitle)
            }
            LastMessageContentWrapper(conversation = conversation)
        }
        Spacer(modifier = Modifier.width(16.dp))
    }
}

/**
 * 美国预览
 */
@Preview(locale = "en-US")
@Composable
private fun PreviewConversationItem(@PreviewParameter(PreviewConversationsProvider::class) conversations: List<Conversation>) {
    LayerContainer {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)) {
            conversations.forEachIndexed { index, conversation ->
                ConversationItem(conversation = conversation)
            }
        }
    }
}

@Preview(locale = "en-US")
@Composable
private fun PreviewBossConversationItem(@PreviewParameter(PreviewConversationsProvider::class) conversations: List<Conversation>) {
    LayerContainer {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)) {
            conversations.forEach { conversation ->
                BossConversationItem(jobs = emptyMap(), conversation = conversation)
            }
        }
    }
}
