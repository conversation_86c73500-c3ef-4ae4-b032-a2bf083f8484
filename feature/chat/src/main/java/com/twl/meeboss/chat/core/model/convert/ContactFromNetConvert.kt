package com.twl.meeboss.chat.core.model.convert

import com.twl.meeboss.base.ktx.isBoss
import com.twl.meeboss.base.ktx.isGeek
import com.twl.meeboss.base.ktx.isSystem
import com.twl.meeboss.chat.export.model.ContactFromNet
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.common.ktx.notNull

/**
 * @author: 冯智健
 * @date: 2024年08月15日 19:29
 * @description:
 */
fun ContactFromNet.toConversation(): Conversation {
    Conversation(
        securityId = securityId,
        friendId = friendId.notNull(),
        friendIdentity = friendIdentity,
        firstName = baseInfo?.firstName,
        lastName = baseInfo?.lastName,
        avatar = baseInfo?.avatar,
        tinyAvatar = baseInfo?.tinyAvatar,
        userStatus = userStatus,
        userStatusPrompt = userStatusPrompt,
        friendStatus = friendStatus,
        topMatched = topMatched,
        starred = favored,
        visible = visible,
        blacklist = blacklist,
        friendSource = friendSource,
        friendLastReadMsgId = friendLastReadMsgId,
        lastDeletedMsgId = lastDeletedMsgId,
        jobId = jobId,
        expectId = expectId
    ).apply {
        return when {
            friendIdentity.isBoss() -> {
                copy(
                    companyName = bossInfo?.companyName,
                    bossPosition = bossInfo?.bossPosition,
                    jobTitle = jobInfo?.jobTitle,
                    salaryDesc = jobInfo?.salaryDesc
                )
            }
            friendIdentity.isGeek() -> {
                copy(
                    schoolName = jobseekerInfo?.schoolName,
                    companyName = jobseekerInfo?.companyName,
                    jobTitle = jobseekerInfo?.jobTitle,
                    highestEduLevelDesc = jobseekerInfo?.highestEduLevelDesc,
                    locationDesc = jobseekerInfo?.locationDesc
                )
            }
            friendIdentity.isSystem() -> {
                copy(
                    companyName = systemInfo?.displayName,
                    jobTitle = systemInfo?.teamName
                )
            }
            else -> {
                this
            }
        }
    }
}