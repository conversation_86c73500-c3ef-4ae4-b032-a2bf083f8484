package com.twl.meeboss.chat.module.common.file

import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.FileIOUtils
import com.blankj.utilcode.util.PathUtils
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.FileBody
import com.twl.meeboss.chat.repos.ChatRepository
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.common.utils.appScope
import com.twl.meeboss.core.network.HttpCore
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Request
import okhttp3.ResponseBody
import okio.Buffer
import okio.BufferedSource
import okio.ForwardingSource
import okio.Source
import okio.buffer
import java.io.File
import java.io.IOException
import javax.inject.Inject

@HiltViewModel
class FilePreviewViewModel @Inject constructor(
    val repo: ChatRepository,
) : BaseMviViewModel<FilePreviewUiState, FilePreviewUiIntent>() {

    var fileBody: FileBody? = null

    override fun initUiState(): FilePreviewUiState = FilePreviewUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is FilePreviewUiIntent.LoadFile -> {
                loadFileInfo()
            }

            is FilePreviewUiIntent.DownloadFile -> {
                downloadFile()
            }
            is FilePreviewUiIntent.OpenInAnotherApp -> {
                sendUiState {
                    copy(navigation = FilePreviewNavigation.OpenInAnotherApp(localFilePath))
                }
            }

            is FilePreviewUiIntent.NavigationHandled -> {
                sendUiState { copy(navigation = null) }
            }
        }
    }

    private fun loadFileInfo() {
        viewModelScope.launch {
            fileBody?.let { file ->
                val targetFilePath = ChatFileManger.getDownloadFilePath(file)
                val isDownloaded = ChatFileManger.isFileExist(file)
                sendUiState {
                    copy(
                        fileName = file.name,
                        fileSize = formatFileSize(file.size.toLong()),
                        status = if (isDownloaded) {
                            FileStatus.DOWNLOADED
                        } else {
                            FileStatus.NONE
                        },
                        localFilePath = targetFilePath,
                        downloadProgress = if (isDownloaded) 1f else 0f
                    )
                }

                ChatFileManger.getFileDownloadProgress(file.md5).collect{ progress->
                    if(progress > 0 && progress < 1f){
                        sendUiState {
                            copy(
                                status = FileStatus.DOWNLOADING,
                                downloadProgress = progress
                            )
                        }
                    }else if(progress == -1f){
                        sendUiState {
                            copy(
                                status = FileStatus.FAILED,
                                downloadProgress = 0f
                            )
                        }
                    }else if(progress == 1f){
                        sendUiState {
                            copy(
                                status = FileStatus.DOWNLOADED,
                                downloadProgress = 1f
                            )
                        }
                    }
                }
            }
        }
    }


    private fun downloadFile() {
        viewModelScope.launch {
            try {
                val status = uiStateFlow.value.status
                // 防止重复下载
                if (status == FileStatus.DOWNLOADING || status == FileStatus.DOWNLOADED) {
                    return@launch
                }
                val fileBody = fileBody?:return@launch
                sendUiState { copy(status = FileStatus.DOWNLOADING, downloadProgress = 0f) }
                val downloadInfo = repo.getUrlWithExpire(fileBody.key)

                if (downloadInfo.isSuccess) {
                    downloadInfo.getOrNull()?.let { fileDownloadInfo ->
                        // 开始下载文件
                        ChatFileManger.startDownloadFile(fileDownloadInfo.downloadUrl,fileBody)
                    }
                } else {
                    sendFileDownloadFailed()
                }
            } catch (e: Exception) {
                sendFileDownloadFailed()
            }
        }
    }

    private suspend fun downloadFileWithProgress(url: String) {
        try {
            // 创建下载目录
            val downloadDir = getDownloadDirectory()
            if (!downloadDir.exists() && !downloadDir.mkdirs()) {
                sendFileDownloadFailed()
                return
            }

            // 创建目标文件
            val targetFile = File(downloadDir, fileBody?.name ?: "download_file")
            if (targetFile.exists()) {
                targetFile.delete()
            }

            if (!targetFile.createNewFile()) {
                sendFileDownloadFailed()
                return
            }

            // 创建带进度监听的请求
            val request = Request.Builder()
                .url(url)
                .get()
                .build()

            val response = HttpCore.callThirdParty(request)

            if (response.isSuccessful) {
                response.body?.let { responseBody ->
                    // 创建带进度监听的ResponseBody
                    val progressResponseBody =
                        ProgressResponseBody(responseBody) { bytesRead, totalBytes ->
                            val progress = if (totalBytes > 0) {
                                bytesRead.toFloat() / totalBytes.toFloat()
                            } else {
                                0f
                            }
                            // 在主线程更新进度
                            viewModelScope.launch {
                                sendUiState {
                                    copy(downloadProgress = progress.coerceIn(0f, 1f))
                                }
                            }
                        }

                    // 写入文件
                    progressResponseBody.byteStream().use { inputStream ->
                        FileIOUtils.writeFileFromIS(targetFile, inputStream)
                    }

                    // 验证文件是否下载完整
                    if (targetFile.exists() && targetFile.length() > 0) {
                        // 下载完成
                        sendUiState {
                            copy(
                                status = FileStatus.DOWNLOADED,
                                downloadProgress = 1f,
                                localFilePath = targetFile.absolutePath
                            )
                        }
                    } else {
                        targetFile.delete()
                        sendFileDownloadFailed()
                    }
                } ?: run {
                    sendFileDownloadFailed()
                }
            } else {
                sendFileDownloadFailed()
            }
        } catch (e: Exception) {
            sendFileDownloadFailed()
        }
    }

    private fun sendFileDownloadFailed(){
        T.ss(R.string.messages_file_download_failed.toResourceString())
        sendUiState { copy(status = FileStatus.FAILED, downloadProgress = 0f) }
    }

    private fun getDownloadDirectory(): File {
        // 使用外部存储的Downloads目录下的attachment子目录
        return File(PathUtils.getExternalAppDownloadPath() + File.separator + "attachment" + File.separator + fileBody?.md5)
    }

    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "$bytes B"
            bytes < 1024 * 1024 -> "%.1f KB".format(bytes / 1024.0)
            bytes < 1024 * 1024 * 1024 -> "%.1f MB".format(bytes / (1024.0 * 1024))
            else -> "%.1f GB".format(bytes / (1024.0 * 1024 * 1024))
        }
    }
}

/**
 * 带进度监听的ResponseBody包装类
 */
class ProgressResponseBody(
    private val responseBody: ResponseBody,
    private val progressListener: (bytesRead: Long, totalBytes: Long) -> Unit
) : ResponseBody() {

    private var bufferedSource: BufferedSource? = null

    override fun contentType() = responseBody.contentType()

    override fun contentLength() = responseBody.contentLength()

    override fun source(): BufferedSource {
        if (bufferedSource == null) {
            bufferedSource = source(responseBody.source()).buffer()
        }
        return bufferedSource!!
    }

    private fun source(source: Source): Source {
        return object : ForwardingSource(source) {
            var totalBytesRead = 0L

            @Throws(IOException::class)
            override fun read(sink: Buffer, byteCount: Long): Long {
                val bytesRead = super.read(sink, byteCount)
                totalBytesRead += if (bytesRead != -1L) bytesRead else 0
                progressListener(totalBytesRead, responseBody.contentLength())
                return bytesRead
            }
        }
    }
}

data class FilePreviewUiState(
    val fileName: String = "",
    val fileSize: String = "",
    val status: FileStatus = FileStatus.NONE,
    val downloadProgress: Float = 0f,
    val localFilePath: String = "",
    val navigation: FilePreviewNavigation? = null
) : IUiState

enum class FileStatus {
    NONE, DOWNLOADING, FAILED, DOWNLOADED
}

sealed class FilePreviewUiIntent : IUiIntent {
    data object LoadFile : FilePreviewUiIntent()
    data object DownloadFile : FilePreviewUiIntent()
    data object OpenInAnotherApp : FilePreviewUiIntent()
    data object NavigationHandled : FilePreviewUiIntent()
}

sealed class FilePreviewNavigation {
    data class OpenInAnotherApp(val filePath: String) : FilePreviewNavigation()
}