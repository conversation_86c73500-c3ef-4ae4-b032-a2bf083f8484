package com.twl.meeboss.chat.module.boss.components

import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.paging.PagingData
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.ktx.requestNotificationPermission
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.module.boss.bottomsheet.BossConversationContextMenuBottomSheet
import com.twl.meeboss.chat.module.boss.viewmodel.ConversationPageUiIntent
import com.twl.meeboss.chat.module.boss.viewmodel.ConversationPageUiState
import com.twl.meeboss.chat.module.boss.viewmodel.ConversationPageViewModel
import com.twl.meeboss.chat.module.boss.viewmodel.ConversationTab
import com.twl.meeboss.chat.module.boss.viewmodel.Filter
import com.twl.meeboss.chat.module.common.components.NotificationActionButton
import com.twl.meeboss.chat.module.common.conversation.BossConversationList
import com.twl.meeboss.chat.module.common.conversation.ConversationTopMatches
import com.twl.meeboss.chat.module.common.conversation.preview.PreviewConversationsProvider
import com.twl.meeboss.chat.module.common.dialog.showDeleteConversationDialog
import com.twl.meeboss.chat.module.topmatches.activity.TopMatchesActivity
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.common.utils.vibrate
import com.twl.meeboss.core.ui.component.tabview.TabBar
import com.twl.meeboss.core.ui.theme.COLOR_00FFFFFF
import com.twl.meeboss.core.ui.theme.COLOR_06605A
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_D5F6E5
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.theme.COLOR_F5FFFFFF
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.ellipsizeText
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.observeLifecycleEvents
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

private const val TAG = "BossConversationPage"

interface PageDataFactory {
    fun scrollState(): LazyListState
    fun conversationsFlow(): Flow<PagingData<Conversation>>
    fun filterBarState(): LazyListState
}

interface ConversationInteractor {
    val onSelectJob: (String?) -> Unit
    val onRequestPermission: () -> Unit
    val onDismissNotice: () -> Unit
    val onOpenJob: (String) -> Unit
    val onLongPressConversation: (Conversation) -> Unit
    val onFilterSelected: (String?, Filter) -> Unit
    val pageDataProvider: (String?, Filter) -> PageDataFactory

    companion object {
        operator fun invoke(
            onSelectJob: (String?) -> Unit = {},
            onRequestPermission: () -> Unit = {},
            onDismissNotice: () -> Unit = {},
            onOpenJob: (String) -> Unit = {},
            onLongPressConversation: (Conversation) -> Unit = {},
            onFilterSelected: (String?, Filter) -> Unit = { _, _ -> },
            pageDataFactory: (String?, Filter) -> PageDataFactory
        ): ConversationInteractor {
            return object : ConversationInteractor {
                override val onSelectJob: (String?) -> Unit = onSelectJob
                override val onRequestPermission: () -> Unit = onRequestPermission
                override val onDismissNotice: () -> Unit = onDismissNotice
                override val onOpenJob: (String) -> Unit = onOpenJob
                override val onLongPressConversation: (Conversation) -> Unit = onLongPressConversation
                override val onFilterSelected: (String?, Filter) -> Unit = onFilterSelected
                override val pageDataProvider: (String?, Filter) -> PageDataFactory = pageDataFactory
            }
        }
    }
}

/**
 * 老板会话列表页入口组件
 *
 * @param vm 会话页面ViewModel
 */
@Composable
fun BossConversationListPage(
    vm: ConversationPageViewModel = viewModel()
) {
    XLog.debug(TAG, "Initializing BossConversationListPage")
    vm.observeLifecycleEvents(LocalLifecycleOwner.current.lifecycle)

    val context = LocalContext.current
    val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()

    val noticeCount by vm.notificationCount.observeAsState()
    val showBadge by remember { derivedStateOf { (noticeCount ?: 0) > 0 } }

    val owner = LocalLifecycleOwner.current
    LaunchedEffect(owner) {
        owner.liveEventBusObserve(BossEventBusKey.UPDATE_JOB_LIST) { _: String ->
            vm.sendUiIntent(ConversationPageUiIntent.RefreshJobTabs)
        }
        owner.liveEventBusObserve(BossEventBusKey.DELETE_JOB_SUCCESS) { _: String ->
            vm.sendUiIntent(ConversationPageUiIntent.RefreshJobTabs)
        }
        owner.liveEventBusObserve(EventBusKey.TOP_MATCHES_READ_DOT_SYNC) { _: String ->
            vm.sendUiIntent(ConversationPageUiIntent.RequestTopMatches)
        }
    }

    val onSelectJob: (String?) -> Unit = remember {
        { jobId ->
            XLog.debug(TAG, "Setting current job to: $jobId after user selection")
            if (jobId != null && uiState.currentJobId != jobId) {
                uiState.tabs
                    .find { tab -> tab.jobId == jobId }
                    .let { tab ->
                        if (tab?.jobId != null && !tab.salary.isNullOrBlank()) {
                            ChatPointReporter.f2TabPositionClick(tab.title, tab.jobStatus, tab.deleted)
                            val title = context.ellipsizeText(tab.title)
                            val salary = context.ellipsizeText(tab.salary)
                            T.ssd("${title}\n${salary}")
                        }
                    }
            }
            vm.sendUiIntent(ConversationPageUiIntent.SetCurrentJob(jobId))
        }
    }

    // 权限请求回调
    val onRequestPermission = remember {
        {
            XLog.debug(TAG, "Notification permission requested")
            (context as FragmentActivity).requestNotificationPermission(
                success = {
                    XLog.debug(TAG, "Notification permission granted")
                    vm.sendUiIntent(ConversationPageUiIntent.NotificationPermissionGranted)
                }
            )
        }
    }

    val onDismissNoticeGuide = remember {
        {
            XLog.debug(TAG, "Notification guide dismissed")
            vm.sendUiIntent(ConversationPageUiIntent.DismissNotificationGuide)
        }
    }

    val onFilterSelected: (String?, Filter) -> Unit = remember {
        { jobId, filter ->
            XLog.debug(TAG, "Filter selected: $filter for jobId: $jobId")
            // 埋点索引基于当前可用过滤器列表中的位置
            val filterIndex = uiState.filters.indexOf(filter) + 1
            ChatPointReporter.f2TabSubListClick(filterIndex)
            vm.sendUiIntent(ConversationPageUiIntent.UpdateFilterState(jobId, filter))
        }
    }

    val pageDataProvider: (String?, Filter) -> PageDataFactory = remember {
        { jobId, filter ->
            object : PageDataFactory {
                override fun scrollState() = vm.getListState(jobId, filter)
                override fun conversationsFlow() = vm.getListFlow(jobId, filter)
                override fun filterBarState() = vm.getFilterBarState(jobId)
            }
        }
    }

    val onOpenJob: (String) -> Unit = remember {
        { jobId ->
            XLog.debug(TAG, "View job requested: $jobId")
            BossPageRouter.jumpToBossJobPreviewActivity(jobId = jobId)
        }
    }

    val onLongPressConversation: (Conversation) -> Unit = remember {
        { conversation ->
            val starred = conversation.starred
            val activity = context as FragmentActivity
            BossConversationContextMenuBottomSheet.show(
                activity = activity,
                starred = starred,
                onStarChange = {
                    val newStar = !starred
                    vm.sendUiIntent(
                        ConversationPageUiIntent.ToggleStar(
                            securityId = conversation.securityId ?: "",
                            friendId = conversation.friendId,
                            starred = newStar
                        )
                    )
                    ChatPointReporter.f2TabStar(
                        friendId = conversation.friendId,
                        starred = newStar,
                        jobId = conversation.jobId ?: ""
                    )
                },
                onDelete = {
                    activity.showDeleteConversationDialog {
                        vm.sendUiIntent(
                            ConversationPageUiIntent.DeleteConversation(
                                friendId = conversation.friendId,
                                friendIdentity = conversation.friendIdentity
                            )
                        )
                    }
                }
            )
            context.vibrate()
        }
    }

    val uiInteractor = remember {
        object : ConversationInteractor {
            override val onSelectJob = onSelectJob
            override val onRequestPermission = onRequestPermission
            override val onDismissNotice = onDismissNoticeGuide
            override val onOpenJob = onOpenJob
            override val onLongPressConversation = onLongPressConversation
            override val onFilterSelected = onFilterSelected
            override val pageDataProvider = pageDataProvider
        }
    }

    ConversationPageContent(
        showNoticeBadge = showBadge,
        uiState = uiState,
        uiInteract = uiInteractor
    )
}

@Composable
private fun ConversationPageContent(
    showNoticeBadge: Boolean,
    uiState: ConversationPageUiState,
    uiInteract: ConversationInteractor,
) {
    val tabs = uiState.tabs
    val jobIndex = tabs.indexOfFirst { it.jobId == uiState.currentJobId }.coerceAtLeast(0)

    Column(
        modifier = Modifier
            .background(Color.White)
            .fillMaxSize()
    ) {
        HeaderTabBar(
            tabs = tabs,
            selectedTabIndex = jobIndex,
            showNoticeBadge = showNoticeBadge,
            onSelectJob = uiInteract.onSelectJob
        )

        SlidingContent(
            targetIndex = jobIndex
        ) { page ->
            TabContent(
                uiState = uiState,
                selectedJobIndex = page,
                uiInteract = uiInteract
            )
        }
    }
}

@Composable
private fun HeaderTabBar(
    tabs: List<ConversationTab>,
    selectedTabIndex: Int,
    showNoticeBadge: Boolean,
    onSelectJob: (String?) -> Unit
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        if (tabs.isNotEmpty()) {
            TabBar(
                options = tabs.map { it.title },
                currentIndex = selectedTabIndex,
                tabIndicatorPercent = 0.6F,
                edgePadding = 8.dp,
                extraEndPadding = 52.dp,
                onTabClick = {
                    if (selectedTabIndex != it) {
                        val job = tabs.getOrNull(it)
                        onSelectJob(job?.jobId)
                    }
                }
            )
        }
        Box(
            modifier = Modifier
                .background(
                    Brush.horizontalGradient(
                        colors = listOf(COLOR_00FFFFFF, COLOR_F5FFFFFF, Color.White)
                    )
                )
                .heightIn(min = 40.dp)
                .width(100.dp)
                .align(Alignment.CenterEnd)
        ) {
            NotificationActionButton(
                showBadge = showNoticeBadge,
                modifier = Modifier.align(Alignment.CenterEnd)
            )
        }
    }
}

@Composable
private fun TabContent(
    uiState: ConversationPageUiState,
    selectedJobIndex: Int,
    uiInteract: ConversationInteractor
) {
    val context = LocalContext.current
    val currentTab = uiState.tabs.getOrNull(selectedJobIndex)
    val jobId = currentTab?.jobId
    val showGuide = uiState.showNotificationGuide

    val filters = uiState.filters
    val selectedFilter = uiState.jobSelectedFilter[jobId] ?: Filter.All
    val pdf = uiInteract.pageDataProvider(jobId, selectedFilter)

    val isAll = selectedJobIndex == 0 && selectedFilter == Filter.All

    BossConversationList(
        scrollState = pdf.scrollState(),
        jobs = if (jobId == null) uiState.jobs else emptyMap(),
        conversationsFlow = pdf.conversationsFlow(),
        header = {
            PageHeader(
                tab = currentTab,
                filters = filters,
                selectedFilter = selectedFilter,
                filterBarState = pdf.filterBarState(),
                showGuide = showGuide && selectedJobIndex == 0,
                uiInteract = uiInteract
            )

            uiState.topMatchesShowInfo?.takeIf { isAll && it.showTopMatchs }?.run {
                ConversationTopMatches(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(bottom = 8.dp),
                    title = title,
                    subTitle = subTitle,
                    avatars = icons,
                    showRedDot = showRedDot
                ) {
                    TopMatchesActivity.start(context)
                }
            }
        },
        onLongPress = uiInteract.onLongPressConversation,
        emptyText = stringResource(id = selectedFilter.emptyHintRes),
        emptyButtonText = "",
    )
}

@Composable
private fun SlidingContent(
    targetIndex: Int,
    content: @Composable (page: Int) -> Unit
) {
    AnimatedContent(
        targetState = targetIndex,
        transitionSpec = {
            if (targetState > initialState) {
                // Index increased: new content slides in from right, old content slides out to left
                slideInHorizontally { fullWidth -> fullWidth } togetherWith
                        slideOutHorizontally { fullWidth -> -fullWidth }
            } else if (targetState < initialState) {
                // Index decreased: new content slides in from left, old content slides out to right
                slideInHorizontally { fullWidth -> -fullWidth } togetherWith
                        slideOutHorizontally { fullWidth -> fullWidth }
            } else {
                // Index is the same: no slide animation
                EnterTransition.None togetherWith ExitTransition.None
            }
        },
    ) { pageIndex -> content(pageIndex) }
}

/**
 * 每个 TAB 页面的头部
 */
@Composable
private fun PageHeader(
    tab: ConversationTab?,
    filters: List<Filter>,
    selectedFilter: Filter,
    filterBarState: LazyListState,
    showGuide: Boolean,
    uiInteract: ConversationInteractor,
) {
    Column {
        if (showGuide) {
            NotificationGuideBar(
                onRequestPermission = uiInteract.onRequestPermission,
                onDismiss = uiInteract.onDismissNotice
            )
        }
        if (tab?.jobId != null) {
            JobStatusBar(
                jobId = tab.jobId,
                jobStatus = tab.jobStatus,
                statusReason = tab.statusReason,
                deleted = tab.deleted,
            ) {
                uiInteract.onOpenJob(tab.jobId)
            }
        }
        FilterBar(
            filters = filters,
            selectedFilter = selectedFilter,
            listState = filterBarState,
            onFilterSelected = { newFilter ->
                uiInteract.onFilterSelected(tab?.jobId, newFilter)
            }
        )
    }
}

@Composable
private fun JobStatusBar(
    jobId: String,
    jobStatus: Int,
    statusReason: String?,
    deleted: Boolean,
    onOpenJob: () -> Unit
) {
    if (deleted) {
        StatusBanner(R.string.employer_message_job_filter_deleted_hint)
        return
    }

    val context = LocalContext.current
    when (jobStatus) { // 10.正常 20.审核中 30.待发布 40.审核失败 50.已关闭
        40 -> {
            StatusBanner(
                statusInfo = R.string.employer_message_job_filter_review_failed_hint,
                actionButton = R.string.employer_message_job_filter_review_failed_button
            ) {
                BossPageRouter.jumpToBossJobPostResultActivity(context, jobStatus, jobId, statusReason ?: "")
                ChatPointReporter.f2TabPromptClick(jobStatus, jobId)
            }
        }

        50 -> {
            StatusBanner(
                statusInfo = R.string.employer_message_job_filter_offline_hint,
                actionButton = R.string.common_view
            ) {
                onOpenJob()
                ChatPointReporter.f2TabPromptClick(jobStatus, jobId)
            }
        }

        else -> {}
    }
}

/**
 * 过滤栏组件
 * 显示新消息和进行中过滤按钮
 *
 * @param modifier 组件修饰符
 * @param selectedFilter 当前选中的过滤器值
 * @param onFilterSelected 过滤器选择回调
 */
@Composable
fun FilterBar(
    filters: List<Filter>,
    selectedFilter: Filter,
    listState: LazyListState,
    modifier: Modifier = Modifier,
    onFilterSelected: (Filter) -> Unit
) {
    LazyRow(
        state = listState,
        modifier = modifier.padding(top = 16.dp, bottom = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        items(filters) { filter ->
            FilterButton(
                text = stringResource(filter.titleRes),
                isSelected = selectedFilter == filter,
                onClick = { onFilterSelected(filter) }
            )
        }
    }
}

/**
 * 过滤按钮组件
 *
 * @param text 按钮文本
 * @param isSelected 是否选中
 * @param onClick 点击回调
 */
@Composable
private fun FilterButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .background(
                color = if (isSelected) Color(0xFFE8F8EE) else Color.White,
                shape = CircleShape
            )
            .border(
                width = if (isSelected) 1.5.dp else 1.dp,
                color = if (isSelected) Color(0xFF00BC5E) else Color(0xFFEBEBEB),
                shape = CircleShape
            )
            .noRippleClickable { onClick() }
            .padding(horizontal = 20.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = TextStyle(
                fontSize = 14.sp,
                color = if (isSelected) Color(0xFF16AC3C) else COLOR_484848
            )
        )
    }
}

/**
 * 通知权限引导栏
 * 提示用户开启通知权限
 *
 * @param onRequestPermission 请求权限回调
 * @param onDismiss 关闭提示回调
 */
@Composable
private fun NotificationGuideBar(
    onRequestPermission: () -> Unit,
    onDismiss: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(64.dp)
            .background(COLOR_D5F6E5)
            .noRippleClickable(onClick = onRequestPermission),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ui_dailog_close),
            contentDescription = "",
            tint = Secondary,
            modifier = Modifier
                .padding(start = 12.dp, end = 8.dp)
                .noRippleClickable(onClick = onDismiss)
        )

        Text(
            text = stringResource(id = R.string.setting_chat_tab_notification_guide),
            style = TextStyle(
                fontSize = 13.sp,
                lineHeight = 20.sp,
                fontWeight = FontWeight.Medium,
                color = Secondary,
            ),
            modifier = Modifier.weight(1f),
        )

        Icon(
            painter = painterResource(id = R.drawable.ui_right_arrow),
            contentDescription = "",
            modifier = Modifier.padding(start = 8.dp, end = 12.dp),
            tint = COLOR_06605A,
        )
    }
}

@Composable
private fun StatusBanner(
    @StringRes statusInfo: Int,
    @StringRes actionButton: Int = -1,
    onAction: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 48.dp)
            .background(COLOR_F5F5F5)
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = stringResource(statusInfo),
            style = TextStyle(
                fontSize = 13.sp,
                lineHeight = 18.sp,
                fontWeight = FontWeight.Medium,
                color = COLOR_222222,
            ),
            modifier = Modifier.weight(1f),
        )

        if (actionButton != -1) {

            Spacer(modifier = Modifier.size(12.dp))

            Text(
                text = stringResource(actionButton),
                modifier = Modifier
                    .border(width = 1.dp, color = COLOR_DDDDDD, shape = CircleShape)
                    .padding(horizontal = 12.dp, vertical = 6.dp)
                    .noRippleClickable { onAction() },
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = COLOR_222222,
                )
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewConversationPageContent(
    @PreviewParameter(PreviewConversationTabProvider::class)
    tabs: List<ConversationTab>
) {
    val mockUiState = ConversationPageUiState(
        tabs = tabs,
        currentJobId = null,
        showNotificationGuide = true,
        filters = Filter.entries,
    )

    val pdp = object : PageDataFactory {
        override fun scrollState() = LazyListState()
        override fun conversationsFlow() = flowOf(PagingData.from(PreviewConversationsProvider.getConversationTestList()))
        override fun filterBarState() = LazyListState()
    }

    ConversationPageContent(
        showNoticeBadge = true,
        uiState = mockUiState,
        uiInteract = ConversationInteractor { _, _ -> pdp }
    )
}

@Preview
@Composable
private fun PreviewNotificationBar() {
    NotificationGuideBar({}, {})
}

@Preview
@Composable
private fun PreviewStatusBar() {
    Column {
        StatusBanner(R.string.employer_message_job_filter_offline_hint, R.string.employer_message_job_filter_offline_button)
        Spacer(Modifier.size(8.dp))
        StatusBanner(R.string.employer_message_job_filter_review_failed_hint, R.string.common_view)
        Spacer(Modifier.size(8.dp))
        StatusBanner(R.string.employer_message_job_filter_deleted_hint)
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewFilterBar() {
    var selectedFilter by remember { mutableStateOf(Filter.All) }
    XTheme {
        FilterBar(
            filters = listOf(Filter.All, Filter.TopMatches, Filter.New, Filter.InProgress, Filter.Star),
            selectedFilter = selectedFilter,
            listState = rememberLazyListState(),
            onFilterSelected = { selectedFilter = it }
        )
    }
}

private class PreviewConversationTabProvider : PreviewParameterProvider<List<ConversationTab>> {
    override val values: Sequence<List<ConversationTab>>
        get() = sequenceOf(
            emptyList(),
            listOf(ConversationTab(title = "All")),
            listOf(ConversationTab(title = "Marketing", jobId = "", deleted = true)),
            listOf(
                ConversationTab(title = "All", jobId = ""),
                ConversationTab(title = "Android Expert Engineer", jobId = "", jobStatus = JobStatus.REVIEW_FAILED),
                ConversationTab(title = "Java Developer", jobStatus = JobStatus.CLOSED)
            )
        )
}
