package com.twl.meeboss.chat.router

import androidx.activity.ComponentActivity
import androidx.compose.runtime.Composable
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.sankuai.waimai.router.annotation.RouterService
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.repo.toastErrorIfPresent
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.api.ChatBossApi
import com.twl.meeboss.chat.api.ChatGeekApi
import com.twl.meeboss.chat.core.ChatClient
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.chat.core.model.convert.toConversation
import com.twl.meeboss.chat.export.ChatRouterPath
import com.twl.meeboss.chat.export.IChatService
import com.twl.meeboss.chat.export.api.IChatClient
import com.twl.meeboss.chat.export.model.AddFriendResult
import com.twl.meeboss.chat.export.model.BossChatParams
import com.twl.meeboss.chat.export.model.ContactFromNet
import com.twl.meeboss.chat.export.model.GeekChatParams
import com.twl.meeboss.chat.export.model.StartChatResult
import com.twl.meeboss.chat.module.boss.activity.BossChatActivity
import com.twl.meeboss.chat.module.common.components.MessageScreen
import com.twl.meeboss.chat.module.boss.components.BossConversationListPage
import com.twl.meeboss.chat.module.common.dialog.ChatBlockDialog
import com.twl.meeboss.chat.module.geek.activity.GeekChatActivity
import com.twl.meeboss.chat.module.geek.components.GeekConversationListPage
import com.twl.meeboss.chat.module.notification.manager.SystemNotificationManager
import com.twl.meeboss.chat.repos.ChatBossRepository
import com.twl.meeboss.chat.repos.ChatGeekRepository
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.activity.FoundationActivity
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GuidanceType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@RouterService(
    interfaces = [IChatService::class],
    key = [ChatRouterPath.CHAT_SERVICE],
    singleton = true
)
class ChatServiceImpl : IChatService {

    override fun startChat(
        context: ComponentActivity,
        friendId: String,
        friendIdentity: Int,
        source: ChatSource,
        callback: (StartChatResult) -> Unit
    ) {
        context.lifecycleScope.launch {
            if (UserProvider.isGeek()) {
                GeekChatActivity.startChat(context, friendId, friendIdentity, source)
            } else {
                BossChatActivity.startChat(context, friendId, friendIdentity, source)
            }
            callback(StartChatResult(true))
        }
    }

    override fun checkStartChat(
        context: ComponentActivity,
        securityId: String,
        friendId: String,
        friendIdentity: Int,
        isFriend: Boolean,
        source: ChatSource,
        addFriendSource: Int,
        callback: (StartChatResult) -> Unit
    ) {
        showLoadingDialog(context)
        context.lifecycleScope.launch {
            if (UserProvider.isGeek()) {
                ChatGeekRepository(getService(ChatGeekApi::class.java)).validateBeforeChat(
                    securityId
                ).onSuccess {
                    dismissLoadingDialog(context)
                    context.lifecycleScope.launch(Dispatchers.Main) {
                        if (it.type == 1) {
                            GeekPageRouter.jumpToGeekChatGuidanceActivity(context,
                                guidanceType = GuidanceType.ChatGuidance,
                                isFormQuickComplete = false,
                                securityId = securityId,
                                friendId = friendId,
                                isFriend = isFriend,
                                chatSource = source)
                            callback(StartChatResult(false))
                        } else if (it.type == 2) {
                            ChatBlockDialog(it).show()
                        } else {
                            if (isFriend && addFriendSource == 0) {
                                startChat(context, friendId, friendIdentity, source, callback)
                            } else {
                                addFriendAndStartChat(context, securityId, source, addFriendSource, callback)
                            }

                        }
                    }
                }.onFailure {
                    dismissLoadingDialog(context)
                    callback(StartChatResult(false))
                    T.ss(it.message)
                }
            } else if (UserProvider.isBoss()) {
                ChatBossRepository(getService(ChatBossApi::class.java)).validateBeforeChat(
                    securityId
                ).onSuccess {
                    dismissLoadingDialog(context)
                    if (it.type == 2) {
                        ChatBlockDialog(it).show()
                    } else {
                        if (isFriend && addFriendSource == 0) {
                            startChat(context, friendId, friendIdentity, source, callback)
                        } else {
                            addFriendAndStartChat(context, securityId, source, addFriendSource, callback)
                        }
                    }
                }.onFailure {
                    dismissLoadingDialog(context)
                    callback(StartChatResult(false))
                    T.ss(it.message)
                }

            }
        }

    }

    override suspend fun checkAndAddFriendOnly(
        context: ComponentActivity,
        securityId: String,
        friendId: String,
        isFriend: Boolean,
        source: ChatSource,
        addFriendSource: Int
    ): AddFriendResult = withContext(Dispatchers.Main) {
        showLoadingDialog(context)
        if (UserProvider.isGeek()) {
            val resp = withContext(Dispatchers.IO){ ChatGeekRepository(getService(ChatGeekApi::class.java)).validateBeforeChat(securityId) }
            val result = resp.getOrNull()

            if (result == null) {
                dismissLoadingDialog(context)
                resp.toastErrorIfPresent()
                return@withContext AddFriendResult()
            }

            when (result.type) {
                1 -> {
                    GeekPageRouter.jumpToGeekChatGuidanceActivity(context,
                        guidanceType = GuidanceType.ChatGuidance,
                        isFormQuickComplete = false,
                        securityId = securityId,
                        friendId = friendId,
                        isFriend = isFriend,
                        chatSource = source)
                    return@withContext AddFriendResult()
                }
                2 -> {
                    ChatBlockDialog(result).show()
                    return@withContext AddFriendResult()
                }
                else -> {
                    return@withContext addFriendOnly(context, securityId, source, false, addFriendSource)
                }
            }

        } else if (UserProvider.isBoss()) {
            val resp = withContext(Dispatchers.IO){ ChatBossRepository(getService(ChatBossApi::class.java)).validateBeforeChat(securityId) }
            val result = resp.getOrNull()

            if (result == null) {
                dismissLoadingDialog(context)
                resp.toastErrorIfPresent()
                return@withContext AddFriendResult()
            }
            if (result.type == 2) {
                ChatBlockDialog(result).show()
                return@withContext AddFriendResult()
            } else {
                return@withContext addFriendOnly(context, securityId, source, false, addFriendSource)
            }
        }

        return@withContext AddFriendResult()
    }

    override suspend fun addFriendOnly(context: ComponentActivity, securityId: String, source: ChatSource, showLoading: Boolean, addFriendSource: Int): AddFriendResult = withContext(Dispatchers.Main){
        if (showLoading) {
            showLoadingDialog(context)
        }
        if (UserProvider.isGeek()) {
            val resp = withContext(Dispatchers.IO) {ChatGeekRepository(getService(ChatGeekApi::class.java)).addFriend(securityId, addFriendSource)}
            val result = resp.getOrNull()
            if (result == null) {
                dismissLoadingDialog(context)
                resp.toastErrorIfPresent()
                return@withContext AddFriendResult()
            }
            createConversationWhenChat(result.friendId, result.friendIdentity, result)
            dismissLoadingDialog(context)
            sendStringLiveEvent(EventBusKey.GEEK_ADD_FRIEND_SUCCESS, securityId)
            return@withContext AddFriendResult(true, result.greetingSettings)

        } else {
            val resp = withContext(Dispatchers.IO) { ChatBossRepository(getService(ChatBossApi::class.java)).addFriend(securityId, addFriendSource)}
            val result = resp.getOrNull()
            if (result == null) {
                dismissLoadingDialog(context)
                resp.toastErrorIfPresent()
                return@withContext AddFriendResult()
            }
            createConversationWhenChat(result.friendId, result.friendIdentity, result)
            dismissLoadingDialog(context)
            sendStringLiveEvent(EventBusKey.BOSS_ADD_FRIEND_SUCCESS, securityId)
            return@withContext AddFriendResult(isSuccess = true, greetingSettings = result.greetingSettings)
        }
    }

    private fun showLoadingDialog(context: ComponentActivity) {
        if (context is FoundationActivity) {
            context.showLoadingDialog()
        }
    }

    private fun dismissLoadingDialog(context: ComponentActivity) {
        if (context is FoundationActivity) {
            context.dismissLoadingDialog()
        }
    }

    /**
     * B或者C添加好友并打开聊天
     */
    override fun addFriendAndStartChat(
        context: ComponentActivity,
        securityId: String,
        source: ChatSource,
        addFriendSource: Int,
        callback: (StartChatResult) -> Unit
    ) {
        showLoadingDialog(context)
        context.lifecycleScope.launch {
            if (UserProvider.isGeek()) {
                ChatGeekRepository(getService(ChatGeekApi::class.java)).addFriend(securityId, addFriendSource)
                        .onSuccess {
                            createConversationWhenChat(it.friendId, it.friendIdentity, it)
                            dismissLoadingDialog(context)
                            GeekChatActivity.startChat(
                                context = context,
                                chatId = it.friendId,
                                friendIdentity = it.friendIdentity,
                                source = source,
                                params = GeekChatParams(
                                    greetingSettings = it.greetingSettings
                                )
                            )
                            sendStringLiveEvent(EventBusKey.GEEK_ADD_FRIEND_SUCCESS, securityId)
                            callback(StartChatResult(isSuccess = true))
                        }.onFailure {
                            dismissLoadingDialog(context)
                            callback(StartChatResult(isSuccess = false))
                            T.ss(it.message)
                        }
            } else {
                ChatBossRepository(getService(ChatBossApi::class.java)).addFriend(securityId, addFriendSource)
                    .onSuccess {
                        createConversationWhenChat(it.friendId, it.friendIdentity, it)
                        dismissLoadingDialog(context)
                        BossChatActivity.startChat(
                            context,
                            it.friendId,
                            it.friendIdentity,
                            source,
                            BossChatParams(
                                greetingSettings = it.greetingSettings
                            )
                        )
                        sendStringLiveEvent(EventBusKey.BOSS_ADD_FRIEND_SUCCESS, securityId)
                        callback(StartChatResult(isSuccess = true))
                    }.onFailure {
                        dismissLoadingDialog(context)
                        callback(StartChatResult(isSuccess = false))
                        T.ss(it.message)
                    }
            }

        }
    }

    private suspend fun createConversationWhenChat(friendId: String, friendIdentity: Int, contact: ContactFromNet?) {
        contact?.let {
            FacadeManager.getInstance().conversationFacade.getConversation(friendId, friendIdentity, false)
            val conversation = it.toConversation()
            FacadeManager.getInstance().conversationFacade.updateConversationFromNet(conversation)
        }
    }

    override fun getChatClient(): IChatClient {
        return ChatClient
    }

    @Composable
    override fun MessagesScreen(
        tabs: List<Int>,
        pageContent: @Composable (Int) -> Unit
    ) {
        MessageScreen(tabs = tabs, pageContent = pageContent)
    }

    @Composable
    override fun BossConversationPage() {
        BossConversationListPage()
    }

    @Composable
    override fun GeekConversationPage(isGeekComplete: Boolean) {
        GeekConversationListPage(isComplete = isGeekComplete)
    }

    override fun getUnreadCount(): Flow<Int> {
        return FacadeManager.getInstance().conversationFacade.getUnreadMessageCount()
    }

    override fun getJobsWithConversation(): Flow<Set<String>> {
        return FacadeManager.getInstance().conversationFacade.getJobsWithConversation()
    }

    override fun getSystemNotificationCount(): MutableLiveData<Int> {
        return SystemNotificationManager.getNotificationCount()
    }
}