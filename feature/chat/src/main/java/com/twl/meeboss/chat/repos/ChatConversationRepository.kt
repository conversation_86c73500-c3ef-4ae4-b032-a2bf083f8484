package com.twl.meeboss.chat.repos

import android.util.Log
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.foundation.repo.BaseRepository
import com.twl.meeboss.base.ktx.isMySelf
import com.twl.meeboss.chat.api.ChatApi
import com.twl.meeboss.chat.api.ChatBossApi
import com.twl.meeboss.chat.api.ChatGeekApi
import com.twl.meeboss.chat.core.db.dao.ConversationDao
import com.twl.meeboss.chat.core.model.convert.toConversation
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.getNewScope
import com.twl.meeboss.core.network.getService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

private const val HAS_SHOWED_GEEK_TOP_HIGHLY_MATCHED_GUIDE = "has_showed_geek_top_highly_matched_guide"
private const val HAS_SHOWED_BOSS_TOP_HIGHLY_MATCHED_GUIDE = "has_showed_boss_top_highly_matched_guide"
class ChatConversationRepository @Inject constructor(
    private val mConversationDao: ConversationDao,
    private val chatApi: ChatApi,
) : BaseRepository() {

    var hasShowedGeekTopHighlyMatchedGuide: Boolean
        set(value) {
            SpManager.putUserBoolean(HAS_SHOWED_GEEK_TOP_HIGHLY_MATCHED_GUIDE, value)
        }
        get() = SpManager.getUserBoolean(HAS_SHOWED_GEEK_TOP_HIGHLY_MATCHED_GUIDE, false)

    var hasShowedBossTopHighlyMatchedGuide: Boolean
        set(value) {
            SpManager.putUserBoolean(HAS_SHOWED_BOSS_TOP_HIGHLY_MATCHED_GUIDE, value)
        }
        get() = SpManager.getUserBoolean(HAS_SHOWED_BOSS_TOP_HIGHLY_MATCHED_GUIDE, false)

    suspend fun updateConversation(message: ChatMessage, isSend: Boolean) {
        getConversation(
            friendId = message.chatId,
            friendIdentity = if (isSend) {
                message.toIdentity
            } else {
                message.fromIdentity
            },
            autoUpdate = true
        ).run {
            updateConversationMsgParams(message = message, conversation = this, isSend = isSend)
        }
    }

    /**
     * 更新联系人最后一条消息状态
     */
    suspend fun updateContactLastMessageStatus(
        mid: Long,
        friendId: String,
        @MessageStatus status: Int
    ) {
        mConversationDao.getConversation(friendId)?.run {
            if (mid >= this.lastMid) {
                mConversationDao.updateConversation(
                    this.copy(
                        lastMessageStatus = status,
                    )
                )
            }
        }

    }

    /**
     * 更新联系人最后一条消息状态
     */
    suspend fun updateContactLastMessageStatusByCmid(
        cmid: Long,
        friendId: String,
        @MessageStatus status: Int
    ) {
        mConversationDao.getConversation(friendId)?.run {
            if (cmid == this.lastCMid) {
                mConversationDao.updateConversationLastMsgStatus(friendId, status = status)
            }
        }

    }

    suspend fun clearUnreadCount(friendId: String) {
        mConversationDao.clearUnRead(friendId)
    }

    suspend fun insertConversation(conversation: Conversation) {
        mConversationDao.updateConversation(conversation)
    }

    suspend fun getConversationFromDB(friendId: String): Conversation? {
        return mConversationDao.getConversation(friendId)
    }

    suspend fun getConversation(
        friendId: String,
        friendIdentity: Int,
        autoUpdate: Boolean = true
    ): Conversation {
        var conversation = mConversationDao.getConversation(friendId)
        if (conversation == null) {
            conversation = Conversation(friendId = friendId, friendIdentity = friendIdentity)
            insertConversation(conversation = conversation)
            if (autoUpdate) {
                getNewScope().launch {
                    updateConversationFromNet(friendId = friendId, friendIdentity = friendIdentity)
                }
            }
        }
        return conversation
    }

    fun getConversationAsFlow(friendId: String) = mConversationDao.getConversationAsFlow(friendId)


    suspend fun updateConversationFromNet(friendId: String, friendIdentity: Int): Conversation? {
        val conversation = if (UserProvider.isGeek()) {
            getService(ChatGeekApi::class.java).geekGetFriend(friendId, friendIdentity).getOrNull()
                ?.toConversation()
        } else {
            getService(ChatBossApi::class.java).bossGetFriend(friendId, friendIdentity).getOrNull()
                ?.toConversation()
        }
        val localConversation = mConversationDao.getConversation(friendId)
        if (localConversation == null) {
            if (conversation != null) {
                mConversationDao.updateConversation(conversation)
            }
        } else {
            if (conversation != null) {
                updateConversationParams(conversation)
            }
        }
        return mConversationDao.getConversation(friendId)
    }

    /**
     * 更新会话基本参数
     */
    suspend fun updateConversationParams(conversation: Conversation?) {
        if (conversation != null) {
            mConversationDao.updateConversationByNet(
                friendId = conversation.friendId,
                firstName = conversation.firstName.notNull(),
                lastName = conversation.lastName.notNull(),
                avtarUrl = conversation.avatar.notNull(),
                tinyAvatar = conversation.tinyAvatar.notNull(),
                schoolName = conversation.schoolName.notNull(),
                companyName = conversation.companyName.notNull(),
                bossPosition = conversation.bossPosition.notNull(),
                jobTitle = conversation.jobTitle.notNull(),
                salaryDesc = conversation.salaryDesc.notNull(),
                highestEduLevelDesc = conversation.highestEduLevelDesc.notNull(),
                userStatus = conversation.userStatus,
                friendStatus = conversation.friendStatus,
                starred = conversation.starred,
                topMatched = conversation.topMatched,
                friendSource = conversation.friendSource,
                visible = conversation.visible,
                friendLastReadMsgId = conversation.friendLastReadMsgId,
                lastDeletedMsgId = conversation.lastDeletedMsgId,
                friendIdentity = conversation.friendIdentity,
                securityId = conversation.securityId.notNull(),
                jobId = conversation.jobId.notNull(),
                expectId = conversation.expectId.notNull(),
                userStatusPrompt = conversation.userStatusPrompt.notNull(),
            )

        }

    }

    /**
     * 更新会话消息参数，基本上是最后一条消息相关
     */
    fun updateConversationMsgParams(
        message: ChatMessage,
        conversation: Conversation,
        isSend: Boolean
    ) {
        TLog.info(TAG, "updateConversationMsgParams message : $message")
        TLog.info(TAG, "updateConversationMsgParams conversation : $conversation")
        TLog.info(TAG, "updateConversationMsgParams isSend : $isSend")
        if (isSend) { //发送消息的情况
            val result = mConversationDao.updateContactByMyMessage(
                friendId = conversation.friendId,
                friendIdentity = conversation.friendIdentity,
                lastMessageContent = message.getSummary(),
                updateTime = message.addTime,
                lastMessageStatus = message.status,
                lastMessageBodyType = message.bodyType,
                lastMessageLocalType = message.localMessageType,
                lastMid = message.mid,
                lastCMid = message.cmid,
                lastMessageIsMine = message.fromId.isMySelf()
            )
            TLog.info(TAG, "updateConversationMsgParams ${message.status},result = ${result}")

        } else { //接受到消息的情况
            if (message.visible && !message.deleted) {
                val unreadMsgCount =
                    if (message.countable && message.status < MessageStatus.READ && !message.fromId.isMySelf()) {
                        conversation.unreadCount + 1
                    } else {
                        conversation.unreadCount
                    }
                val result = mConversationDao.updateContactByFriendMessage(
                    friendId = conversation.friendId,
                    friendIdentity = conversation.friendIdentity,
                    lastMessageContent = message.getSummary(),
                    updateTime = message.addTime,
                    lastMessageStatus = message.status,
                    lastMessageBodyType = message.bodyType,
                    lastMessageLocalType = message.localMessageType,
                    lastMid = message.mid,
                    lastCMid = message.cmid,
                    unreadCount = unreadMsgCount,
                    lastMessageIsMine = message.fromId.isMySelf()
                )
                Log.i(TAG, "updateConversationMsgParams ${message.status},result = ${result}")
                Log.i(TAG, "updateConversationMsgParams unreadMsgCount = $unreadMsgCount")

            }

        }
    }

    /**
     * 全部会话
     */
    fun getAllConversations(jobId: String? = null): Flow<PagingData<Conversation>> {
        TLog.info(TAG, "getAllConversations jobId=$jobId")
        return Pager(
            config = PagingConfig(100),
            pagingSourceFactory = {
                TLog.info(TAG, "getAllConversations factory jobId=$jobId")
                mConversationDao.getAllVisibleConversationsByJobId(jobId)
            }
        ).flow
    }

    /**
     * 新的打招呼会话
     */
    fun getNewGreetingConversations(jobId: String? = null): Flow<PagingData<Conversation>> {
        TLog.info(TAG, "getNewGreetingConversations jobId=$jobId")
        return Pager(
            config = PagingConfig(100),
            pagingSourceFactory = {
                mConversationDao.getNewGreetingConversationsByJobId(jobId)
            }
        ).flow
    }

    /**
     * 在沟通会话
     */
    fun getInCommunicationConversations(jobId: String? = null): Flow<PagingData<Conversation>> {
        TLog.info(TAG, "getInCommunicationConversations jobId=$jobId")
        return Pager(
            config = PagingConfig(100),
            pagingSourceFactory = {
                mConversationDao.getInCommunicateConversationsByJobId(jobId)
            }
        ).flow
    }

    /**
     * 星标会话
     */
    fun getStarredConversations(jobId: String? = null): Flow<PagingData<Conversation>> {
        TLog.info(TAG, "getStarredConversations jobId=$jobId")
        return Pager(
            config = PagingConfig(100),
            pagingSourceFactory = {
                mConversationDao.getStarredConversationsByJobId(jobId)
            }
        ).flow
    }

    /**
     * 高匹配会话
     */
    fun getTopMatchedConversations(jobId: String? = null): Flow<PagingData<Conversation>> {
        TLog.info(TAG, "getTopMatchedConversations jobId=$jobId")
        return Pager(
            config = PagingConfig(100),
            pagingSourceFactory = {
                mConversationDao.getTopMatchedConversationsByJobId(jobId)
            }
        ).flow
    }

    /**
     * 检查是否存在高匹配会话
     */
    fun hasTopMatchedConversations(): Flow<Boolean> {
        return mConversationDao.hasTopMatchedConversations()
            .distinctUntilChanged()
    }

    /**
     * 观察所有有会话的职位ID列表
     */
    fun observeJobIdsWithConversations(): Flow<Set<String>> {
        return mConversationDao.observeJobIdsWithConversations()
            .map { it.toHashSet() }
            .distinctUntilChanged()
    }

    fun getUnreadCount(): Flow<Int> {
        return mConversationDao.selectUnReadCount()
    }

    fun saveDraft(draft: String, friendId: String) {
        if (draft.isBlank()) {
            mConversationDao.saveDraft(draft, friendId)
        } else {
            mConversationDao.saveDraftAndUpdateTime(draft, friendId, System.currentTimeMillis())

        }
    }

    fun starContact(friendId: String, starred: Boolean) {
        mConversationDao.starContact(friendId, starred)
    }

    suspend fun getTopMatchesShowInfo() = safeCallApi {
        chatApi.requestTopMatchedShow()
    }

    suspend fun getTopMatchesDetail() = safeCallApi {
        chatApi.requestTopMatchedDetail()
    }

    suspend fun noInterestedMatch(securityId: String) = safeCallApi {
        chatApi.requestTopMatchedNotInterested(securityId)
    }

    fun deleteConversation(friendId: String) {
        mConversationDao.deleteConversation(friendId)
    }

    suspend fun getQuickReplyTemplate(friendId: String, friendIdentity: Int, jobId: String) = safeCallApi{
        chatApi.getQuickReplyTemplate(friendId, friendIdentity, jobId)
    }

    suspend fun quickReplyTemplateClick(friendId: String, friendIdentity: Int, type: Int, scene: String, mid: Long) = safeCallApi{
        chatApi.quickReplyTemplateClick(friendId, friendIdentity, type, scene, mid)
    }

}