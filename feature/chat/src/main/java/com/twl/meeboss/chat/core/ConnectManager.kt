package com.twl.meeboss.chat.core

import android.app.Activity
import android.app.NotificationManager
import android.os.SystemClock
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.EncryptUtils
import com.blankj.utilcode.util.Utils
import com.blankj.utilcode.util.Utils.OnAppStatusChangedListener
import com.bzl.im.BIMClient
import com.bzl.im.auth.BILoginInfo
import com.bzl.im.auth.status.StatusCode
import com.bzl.im.config.SDKOptions
import com.bzl.im.config.log.BILog
import com.bzl.im.message.BISendCallback
import com.bzl.im.message.attachment.paser.BizAttachmentParser
import com.bzl.im.user.model.BIUser
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.app.AppLifecycleManager.isForeground
import com.twl.meeboss.base.ktx.launcherOnIO
import com.twl.meeboss.chat.core.model.PaasInfoResult
import com.twl.meeboss.chat.api.ChatApi
import com.twl.meeboss.chat.core.constant.PresenceType
import com.twl.meeboss.chat.core.facade.FacadeManager
import com.twl.meeboss.chat.core.facade.MessageProcessor
import com.twl.meeboss.chat.core.model.convert.Message2PBConvert
import com.twl.meeboss.chat.core.model.convert.PBMessageParser
import com.twl.meeboss.chat.core.paas.MeeBossMsgAttachmentParser
import com.twl.meeboss.chat.core.paas.PaasTest
import com.twl.meeboss.chat.core.paas.transformer.toChatMessage
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.preference.SpKey
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.provider.ContextProvider
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.DeviceExtUtils
import com.twl.meeboss.common.utils.getNewScope
import com.twl.meeboss.core.network.HttpParamsProvider.httpAppVersionName
import com.twl.meeboss.core.network.config.HttpConfigManager
import com.twl.meeboss.core.network.config.MqttSSLSocketFactoryManager
import com.twl.meeboss.core.network.getService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.concurrent.Volatile


class ConnectManager {
    private var status = ERROR
    private val chatApi by lazy { getService(ChatApi::class.java) }
    private var mPaasInfo: PaasInfoResult = PaasInfoResult()
        set(value) {
            field = value
            SpManager.run {
                putGlobalString(SpKey.KEY_U_PAAS_UID, value.paasUid)
                putGlobalString(SpKey.KEY_U_PAAS_USER_TOKEN, value.userToken)
            }
        }

    private val messageParser: PBMessageParser by lazy {
        PBMessageParser().also {
            it.setCallback(MessageProcessor())
        }
    }

    private val mStatusObserver = MutableLiveData<Byte>()

    private val mPCObserver = MutableLiveData(false)

    init {
        mPaasInfo.run {
            SpManager.run {
                paasUid = getGlobalString(SpKey.KEY_U_PAAS_UID, "").notNull()
                userToken = getGlobalString(SpKey.KEY_U_PAAS_USER_TOKEN, "").notNull()
            }
        }
        initPaasSdk()
    }

    private fun initPaasSdk() {
        BIMClient.init(
            context = Utils.getApp().applicationContext,
            myStub = object : BIUser {
                override fun getIdentity(): Int {
                    return UserProvider.getIdentify()
                }

                override fun getSource(): String {
                    return "3"
                }

                override fun getUserId(): String {
                    return UserProvider.userIdStr()
                }
            },
            sdkOptions = SDKOptions().apply {
                qos = 1
                log = BLogImpl()
                customHost =
                    HttpConfigManager.getNetEnvironment().baseHttpUrl.substringBeforeLast("/")
                mqttSSLSocketFactory =
                    MqttSSLSocketFactoryManager.createMqttSSLSocketFactory(ContextProvider.getAppContext())
            },
            statusListener = { statusData ->
                when (statusData.statusCode) {
                    StatusCode.MQTT_CONNECTING -> onConnectionConnecting()
                    StatusCode.MQTT_CONN_SUCCESS -> onConnectionConnected()
                    StatusCode.MQTT_CONN_DISCONNECT -> onConnectionDisconnected(statusData.statusCode)
                    else -> {
                        onConnectionFailed()
                        if (statusData.statusCode == StatusCode.MQTT_ERROR_CODE) {
                            if (statusData.isTokenInvalidated()) { // Token 过期，刷新 Token
                                getPaasInfo()
                            }
                        }
                    }
                }
            }
        )

        BizAttachmentParser.registerCustomBizAttachmentParser(MeeBossMsgAttachmentParser())
        BIMClient.onReceiveUnrecognizedMessage = { topic, data ->
            TLog.info(TAG, "接收到新的 Topic $topic 数据")
            if (topic == TOPIC) {
                messageParser.onReceive(data)
            }
        }

        BIMClient.getMessageService().addMessageReceivedListener { _, data ->
            data.forEach {
                PaasTest.onMessageReceive(it.getCMid())
                FacadeManager.getInstance().messageFacade.onMessage(it.toChatMessage())
            }
        }

        AppUtils.registerAppStatusChangedListener(object : OnAppStatusChangedListener {
            override fun onForeground(activity: Activity?) {
                sendPresence(PresenceType.MSG_PRESENCE_VISIBLE)
                if (UserProvider.isLogin()) {
                    getNewScope().launcherOnIO {
                        chatApi.systemNotificationUpdate(isNotificationEnabled())
                    }
                }
            }

            override fun onBackground(activity: Activity?) {
                sendPresence(PresenceType.MSG_PRESENCE_GONE)
            }
        })
        TLog.info("BIMClient", "init")
    }

    private fun doConnect() {
        try {
            val clientId = clientId
            val username = userName
            val password = password
            BIMClient.login(object : BILoginInfo {
                override fun getClientId(): String {
                    return clientId
                }

                override fun getPassword(): String {
                    return password
                }

                override fun getUsername(): String {
                    return username
                }
            })
            mPCObserver.postValue(false)
            TLog.debug(
                TAG,
                "connect() called with: clientId = [$clientId], username = [$username], password = [$password]"
            )
        } catch (e: Throwable) {
            TLog.error(TAG, "connect() ${e.message}")
            e.printStackTrace()
        }

    }

    fun connect() {
        if (mPaasInfo.isIllegal()) {
            getPaasInfo()
        } else {
            doConnect()
        }

    }

    fun getStatus(): Int {
        return mStatusObserver.getValue()?.toInt() ?: 0
    }

    fun disconnect() {
        mPaasInfo = PaasInfoResult()
        BIMClient.logout()
    }


    private var mLastTime: Long = 0


    private fun sendPresence(type: Int) {
        if (status != SUCCESS) {
            return
        }
        TLog.info(TAG, "sendPresence type : %s", type)
        BIMClient.getMessageService().sendTopicMessage(
            TOPIC,
            Message2PBConvert.createOnLinePresence(type),
            false,
            object : BISendCallback {
                override fun onFailure() {
                    TLog.error(TAG, "sendPresence onFailure(): type = $type")
                }

                override fun onSuccess(mid: Long, time: Long, seq: Long) {
                    TLog.info(TAG, "sendPresence onSuccess(): type = $type")
                }
            })
    }

    private fun onConnectionConnected() {
        mStatusObserver.postValue(SUCCESS)
        status = SUCCESS
        val time = SystemClock.elapsedRealtime()
        if ((time - mLastTime) > INTERVAL) {
            mLastTime = time
            sendPresence(PresenceType.MSG_PRESENCE_ONLINE)
            val type =
                if (isForeground()) PresenceType.MSG_PRESENCE_VISIBLE else PresenceType.MSG_PRESENCE_GONE
            sendPresence(type)
        }
        TLog.debug(TAG, "onConnectionConnected()")
    }

    private fun onConnectionFailed() {
        mStatusObserver.postValue(ERROR)
        status = ERROR
        TLog.debug(TAG, "onConnectionFailed() ")
    }

    private fun onConnectionConnecting() {
        mStatusObserver.postValue(CONNECTING)
        status = CONNECTING
    }

    private fun onConnectionDisconnected(code: Int) {
        mStatusObserver.postValue(ERROR)
        status = ERROR
        if (code == 1) {
            AccountManager.logout()
            TLog.error(TAG, "onConnectionDisconnected() logout = $code")
        }
        TLog.debug(TAG, "onConnectionDisconnected() called with: code = $code")
    }

    private val userName: String
        get() {
            return "${mPaasInfo.paasUid}_${DeviceExtUtils.getCountryString()}_${
                AppUtils.getAppVersionCode().httpAppVersionName()
            }_android"
        }

    private val password: String
        get() = mPaasInfo.userToken


    private val clientId: String
        get() {
            var clientId = userName + System.currentTimeMillis()
            clientId = EncryptUtils.encryptMD5ToString(clientId)
            clientId = clientId.substring(0, 20)
            return "AN-$clientId"
        }


    companion object {
        private const val TAG = "ConnectManager"
        const val PROTOCOL_VERSION: String = "2"
        private const val INTERVAL: Long = 10

        /**
         * MQTT连接成功
         */
        const val SUCCESS: Byte = 1

        /**
         * MQTT连接失败
         */
        const val ERROR: Byte = 2

        /**
         * MQTT连接中
         */
        const val CONNECTING: Byte = 3

        /**
         * MQTT连接中
         */
        const val TOPIC = "chat"

        @Volatile
        private var gConnectManager: ConnectManager? = null

        val instance: ConnectManager?
            get() {
                if (gConnectManager == null) {
                    synchronized(ConnectManager::class.java) {
                        if (gConnectManager == null) {
                            gConnectManager = ConnectManager()
                        }
                    }
                }
                return gConnectManager
            }
    }

    fun isNotificationEnabled(): Boolean {
        val notificationManager = ContextProvider.getContext().getSystemService(
            NotificationManager::class.java
        )
        if (notificationManager == null) {
            return false
        }
        // 判断应用通知是否开启
        return notificationManager.areNotificationsEnabled()
    }

    private fun getPaasInfo() {
        getNewScope().launcherOnIO {
            getPaasInfoWithRetry(maxRetries = 10) // 最多重试10次
        }
    }

    private suspend fun getPaasInfoWithRetry(
        maxRetries: Int = Int.MAX_VALUE,
        currentRetry: Int = 0
    ) {
        if (currentRetry >= maxRetries) {
            XLog.error(TAG, "达到最大重试次数，停止重试")
            return
        }

        try {
            val paasInfo = chatApi.getPaasInfo()
            if (paasInfo.isSuccess) {
                paasInfo.getOrNull()?.let {
                    mPaasInfo = it
                    getNewScope().launch(Dispatchers.Main) {
                        doConnect()
                    }
                    XLog.info(TAG, "获取PaasInfo成功，重试次数: $currentRetry")
                }
            } else {
                XLog.info(TAG, "获取PaasInfo失败，3秒后进行第${currentRetry + 1}次重试")
                delay(1000)
                getPaasInfoWithRetry(maxRetries, currentRetry + 1)
            }
        } catch (e: Exception) {
            XLog.info(TAG, "获取PaasInfo异常: ${e.message}，3秒后进行第${currentRetry + 1}次重试")
            delay(1000)
            getPaasInfoWithRetry(maxRetries, currentRetry + 1)
        }
    }
}

private class BLogImpl : BILog.ILog {
    override fun log(level: Int, tag: String, format: String, vararg args: Any) {
        TLog.error(tag, String.format(format, *args))
    }
}