package com.twl.meeboss.chat.module.common.file

import com.blankj.utilcode.util.FileIOUtils
import com.blankj.utilcode.util.PathUtils
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.message.FileBody
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.common.utils.appScope
import com.twl.meeboss.core.network.HttpCore
import com.twl.meeboss.core.ui.utils.toResourceString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Request
import java.io.File

object ChatFileManger {
    private val downloadProgressMap = hashMapOf<String, MutableStateFlow<Float>>()

    fun getFileDownloadProgress(md5: String): Flow<Float> {
        downloadProgressMap[md5] = downloadProgressMap[md5] ?: MutableStateFlow(0f)
        return downloadProgressMap[md5]!!
    }

    fun isFileExist(fileBody: FileBody): Boolean {
        val file = File(getDownloadDirectory(fileBody), fileBody.name)
        return file.exists() && file.length() == fileBody.size.toLong()
    }

    fun getDownloadFilePath(fileBody: FileBody):String = File(getDownloadDirectory(fileBody), fileBody.name).absolutePath

    fun startDownloadFile(downloadUrl: String, fileBody: FileBody) {
        appScope.launch {
            try {
                // 开始下载文件
                withContext(Dispatchers.IO) {
                    downloadFileWithProgress(downloadUrl, fileBody)
                }
            } catch (e: Exception) {
                sendFileDownloadFailed(fileBody.md5)
            }
        }
    }

    private suspend fun downloadFileWithProgress(url: String, fileBody: FileBody) {
        try {
            // 创建下载目录
            val downloadDir = getDownloadDirectory(fileBody)
            if (!downloadDir.exists() && !downloadDir.mkdirs()) {
                sendFileDownloadFailed(fileBody.md5)
                return
            }

            // 创建目标文件
            val targetFile = File(downloadDir, fileBody.name)
            if (targetFile.exists()) {
                targetFile.delete()
            }

            if (!targetFile.createNewFile()) {
                sendFileDownloadFailed(fileBody.md5)
                return
            }

            downloadProgressMap[fileBody.md5]?.value = 0f
            // 创建带进度监听的请求
            val request = Request.Builder()
                .url(url)
                .get()
                .build()

            val response = HttpCore.callThirdParty(request)

            if (response.isSuccessful) {
                response.body?.let { responseBody ->
                    // 创建带进度监听的ResponseBody
                    val progressResponseBody =
                        ProgressResponseBody(responseBody) { bytesRead, totalBytes ->
                            val progress = if (totalBytes > 0) {
                                bytesRead.toFloat() / totalBytes.toFloat()
                            } else {
                                0f
                            }
                            downloadProgressMap[fileBody.md5]?.value = progress
                        }

                    // 写入文件
                    progressResponseBody.byteStream().use { inputStream ->
                        FileIOUtils.writeFileFromIS(targetFile, inputStream)
                    }

                    // 验证文件是否下载完整
                    if (targetFile.exists() && targetFile.length() > 0) {
                        // 下载完成
                        downloadProgressMap[fileBody.md5]?.value = 1f
                    } else {
                        targetFile.delete()
                        sendFileDownloadFailed(fileBody.md5)
                    }
                } ?: run {
                    sendFileDownloadFailed(fileBody.md5)
                }
            } else {
                sendFileDownloadFailed(fileBody.md5)
            }
        } catch (e: Exception) {
            sendFileDownloadFailed(fileBody.md5)
        }
    }

    private fun getDownloadDirectory(fileBody: FileBody): File {
        // 使用外部存储的Downloads目录下的attachment子目录
        return File(PathUtils.getExternalAppDownloadPath() + File.separator + "attachment" + File.separator + fileBody.md5)
    }

    private fun sendFileDownloadFailed(md5: String) {
        T.ss(R.string.messages_file_download_failed.toResourceString())
        downloadProgressMap[md5]?.value = -1f
    }
}