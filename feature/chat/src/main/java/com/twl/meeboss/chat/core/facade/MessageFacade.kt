package com.twl.meeboss.chat.core.facade

import android.util.Log
import com.bzl.im.BIMClient
import com.bzl.im.message.BISendCallback
import com.bzl.im.utils.toJsonString
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.ktx.launcherOnIO
import com.twl.meeboss.chat.core.ConnectManager
import com.twl.meeboss.chat.core.bus.MessageEvent
import com.twl.meeboss.chat.core.bus.MessageEventBus
import com.twl.meeboss.chat.core.constant.ActionMsgType
import com.twl.meeboss.chat.core.db.ChatDatabase
import com.twl.meeboss.chat.core.model.MessageCallback
import com.twl.meeboss.chat.core.model.convert.Message2PBConvert
import com.twl.meeboss.chat.core.model.convert.MessageUtils
import com.twl.meeboss.chat.core.model.message.ActionMessage
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.MessageRecord
import com.twl.meeboss.chat.core.model.message.ReadMessage
import com.twl.meeboss.chat.core.paas.PaasTest
import com.twl.meeboss.chat.core.paas.transformer.toPaasMessage
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.repos.ChatMessageRepository
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.ProcessHelper
import com.twl.meeboss.common.utils.getNewScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch


class MessageFacade(private val messageCallback: MessageCallback) {
    private val tag = this.javaClass.simpleName

    private val mRepository: ChatMessageRepository by lazy {
        ChatMessageRepository(
            mMessageDao = ChatDatabase.getInstance(ProcessHelper.getContext()).messageDao()
        )
    }

    private val scope = getNewScope()

    private val sendScope = getNewScope()

    fun sendMessageScope(): CoroutineScope {
        return sendScope
    }


    fun init() {
        updateAllSendingMessageToFail()
    }

    fun onMessage(message: ChatMessage) {
        if (message is ActionMessage) {
            onActionMessage(message)
        }
        if (!mRepository.onReceive(message)) {
            messageCallback.onReceiveMessage(message)
        }
        MessageEventBus.tryPost(MessageEvent.MessageReceived(message))
        TLog.debug(tag, "onMessage: ${message.toJsonString()}")
    }

    fun onActionMessage(message: ActionMessage) {
        when (message.actionBody?.actionType) {
            ActionMsgType.UPDATE_MESSAGE -> {
                try {
                    MessageUtils.jsonToMessage(message.actionBody?.actionContent.notNull())?.let {
                        onMessage(it)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            ActionMsgType.UPDATE_CONVERSATION -> {
                mRepository.deleteMessage(message)
            }

            else -> {

            }
        }
    }

    fun onReadMessage(message: ReadMessage) {
        scope.launcherOnIO {
            if (message.type == 2) {
                mRepository.updateMessageToRead(message.userId, message.userId, message.mid)
                messageCallback.onReceiveRead(message)
            } else {
                messageCallback.onReceiveRead(message)
            }

        }

    }

    /**
     * 发送消息
     */
    fun sendMessage(message: ChatMessage) {
        messageCallback.onSendMessage(message)
        val id = mRepository.insertMessage(message)
        message.id = id
        TLog.debug(tag, "sendMessage id =  ${id},send message = ${message}")
        internalSend(message)
    }

    fun sendTemporaryMessage(message: ChatMessage): Long {
        messageCallback.onSendMessage(message)
        val id = mRepository.insertMessage(message)
        message.id = id
        TLog.debug(tag, "sendTemporaryMessage id =  ${id},send message = ${message}")
        return id
    }

    fun sendReadMessage(friendId: String) {
        TLog.debug(tag, "sendReadMessage friendId : %s", friendId)
        val maxReceiveId = mRepository.getMaxRecID(friendId)
        BIMClient.getMessageService().sendTopicMessage(
            ConnectManager.TOPIC,
            Message2PBConvert.readMessageToByte(
                friendMaxMessageId = maxReceiveId,
                friendId = friendId
            ),
            true,
            object : BISendCallback {
                override fun onFailure() {
                    TLog.debug(tag, "sendReadMessage onFailure(): friendId = %s", friendId)
                }

                override fun onSuccess(mid: Long, time: Long, seq: Long) {
                    TLog.debug(tag, "sendReadMessage onSuccess(): friendId = %s", friendId)
                }
            }
        )
        messageCallback.onSendRead(friendId)

    }

    fun getMaxMessageID(): Long {
        return mRepository.getMaxMessageId()
    }


    private fun internalSend(messageRecord: ChatMessage) {
        TLog.debug(tag, "internalSend: %s", messageRecord.toString())
        PaasTest.onMessageSend(messageRecord.cmid)
        BIMClient.getMessageService().sendMessage(
            message = messageRecord.toPaasMessage(),
            resend = false,
            callback = object : BISendCallback {
                override fun onFailure() {
                    onSendFailure(messageRecord)
                    Log.i(tag, "onFailure: ${messageRecord}")
                }

                override fun onSuccess(mid: Long, time: Long, seq: Long) {
                    TLog.debug(
                        tag,
                        "onSuccess  mid : $mid ,time : $time ,seq : $seq "
                    )
                    mRepository.mMessageDao.queryByCMid(messageRecord.cmid, messageRecord.chatId)
                        ?.let {
                            messageRecord.status = it.status
                            messageRecord.mid = mid
                            messageRecord.addTime = time
                            if (messageRecord.status == MessageStatus.SENDING || messageRecord.status == MessageStatus.FAILED) {
                                messageRecord.status = MessageStatus.SEND
                            }
                            mRepository.updateMessage(messageRecord)
                            messageCallback.onSendMessageSuccess(messageRecord)
                            MessageEventBus.tryPost(MessageEvent.MessageSentSuccessfully(messageRecord))
                            TLog.debug(
                                tag,
                                "onSuccess: %d , %s",
                                messageRecord.id,
                                messageRecord.content
                            )
                        }
                }
            }
        )
    }


    fun onSendFailure(messageRecord: ChatMessage) {
        messageRecord.status = MessageStatus.FAILED
        mRepository.updateMessageStatus(
            messageRecord.id,
            messageRecord.status
        )
        MessageEventBus.tryPost(MessageEvent.MessageSendFailed(messageRecord))
        TLog.error(tag, "onFailure:  %d ,%s", messageRecord.id, messageRecord.content)
        messageCallback.onSendFailure(messageRecord)
    }

    fun resendMessage(message: ChatMessage) {
        deleteMessage(message)
        message.id = 0
        message.addTime = System.currentTimeMillis()
        message.cmid = System.currentTimeMillis()
        message.mid = message.cmid
        sendMessage(message)
    }

    fun deleteMessage(message: ChatMessage) {
        mRepository.deleteMessage(message)
    }

    private fun updateAllSendingMessageToFail() {
        scope.launch(Dispatchers.IO) {
            mRepository.queryAllSendingMessage().forEach {
                mRepository.updateMessageStatus(it.id, MessageStatus.FAILED)
                messageCallback.onSendFailure(it)
            }
        }
    }

    fun deleteAllMessages(friendId: String) {
        scope.launcherOnIO {
            mRepository.deleteAllMessages(friendId)
        }
    }

    suspend fun queryMessageByType(
        chatId: String,
        @LocalMessageType type: Int
    ): List<MessageRecord> {
        return mRepository.queryMessageByType(chatId, type)
    }

    fun onDestroy() {
        scope.cancel("destroy")
    }

}