package com.twl.meeboss.chat.module.boss.components

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.paging.PagingData
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.blankj.utilcode.util.KeyboardUtils
import com.twl.meeboss.chat.R
import com.twl.meeboss.chat.core.model.MessageModel
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.module.boss.activity.callback.BossUIClickCallbackDefault
import com.twl.meeboss.chat.module.boss.activity.callback.IBossUIClickCallback
import com.twl.meeboss.chat.module.boss.state.BossChatUIState
import com.twl.meeboss.chat.module.common.components.ChatMessageList
import com.twl.meeboss.chat.module.common.components.ChatTopSection
import com.twl.meeboss.chat.module.common.components.CopyMessageCover
import com.twl.meeboss.chat.module.common.components.input.ChatInputIntent
import com.twl.meeboss.chat.module.common.components.input.ChatInputType
import com.twl.meeboss.chat.module.common.components.input.ChatUserInput
import com.twl.meeboss.chat.module.common.model.ChatTopAction
import com.twl.meeboss.chat.module.common.model.MessageLongClickBean
import com.twl.meeboss.chat.module.common.preview.MessagePreviewProvider
import com.twl.meeboss.chat.module.common.uistate.ChatUIState
import com.twl.meeboss.chat.utils.ChatPointReporter
import com.twl.meeboss.common.utils.vibrate
import com.twl.meeboss.core.ui.component.XDivider
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.utils.noRippleClickable
import kotlinx.coroutines.flow.MutableStateFlow


@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun BossChatContent(
    modifier: Modifier = Modifier,
    messages: LazyPagingItems<MessageModel<out ChatMessage>>,
    uiState: ChatUIState<BossChatUIState> = ChatUIState<BossChatUIState>(mState = BossChatUIState()),
    uiClickCallback: IBossUIClickCallback = BossUIClickCallbackDefault()
) {
    val context = LocalContext.current
    var longClickBean: MessageLongClickBean? by remember { mutableStateOf(null) }
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Scaffold(
            containerColor = Color.White,
            modifier = modifier
                .fillMaxSize()
                .background(Color.White),
            topBar = {
                ChatTopSection(
                    modifier = Modifier.fillMaxWidth(),
                    title = uiState.title,
                    showSubTitle = !uiState.isSystemUser,
                    subTitle = uiState.subTitle,
                    showActions = !uiState.isSystemUser,
                    starred = uiState.mState.starred,
                    showMore = !uiState.isSystemUser,
                    topActions = uiState.topActions,
                    onActionClick = uiClickCallback::onClickTopButton,
                    onMoreClick = uiClickCallback::onClickMore,
                    onStarClick = uiClickCallback::onStarClick,
                    onClickBack = uiClickCallback::onClickBack
                )
            },
            bottomBar = {
                Column {
                    LazyRow(contentPadding = PaddingValues(12.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        itemsIndexed(uiState.quickReplies){index, item ->
                            Box(modifier = Modifier
                                .background(color = COLOR_F5F5F5, shape = RoundedCornerShape(100.dp))
                                .padding(horizontal = 16.dp, vertical = 10.dp)
                                .noRippleClickable {
                                    uiClickCallback.sendQuickReply(item, index)
                                    ChatPointReporter.quickReplyClick(item.content)
                                }
                            ) {
                                Text(text = item.content, color = COLOR_222222, fontSize = 14.sp)
                            }
                        }
                    }

                    if (uiState.quickReplies.isNotEmpty()) {
                        XDivider()
                    }

                    ChatUserInput(uiState = uiState.chatInputUiState) {
                        uiClickCallback.onInputStateChanged(it)
                    }
                }
            }) { padding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .background(Color.White)
            ) {
                ChatMessageList(
                    modifier = Modifier.pointerInteropFilter {
                        // 触摸事件触发清除焦点
                        KeyboardUtils.hideSoftInput(context as Activity)
                        uiClickCallback.onInputStateChanged(ChatInputIntent.ChangePanelShow(false))
                        uiClickCallback.onInputStateChanged(
                            ChatInputIntent.ChangeInputType(
                                ChatInputType.EMPTY_PANEL
                            )
                        )
                        false // 不消费事件，允许后续事件传递
                    }, scrollState = rememberLazyListState(), messages = messages,
                    messageClickCallback = uiClickCallback,
                    onLongClick = {
                        context.vibrate()
                        longClickBean = it
                    }
                )
            }
        }

        CopyMessageCover(bean = longClickBean){
            longClickBean = null
        }
    }

}

@Preview
@Composable
private fun GeekChatContentPreview(
    @PreviewParameter(MessagePreviewProvider::class)
    flow: MutableStateFlow<PagingData<MessageModel<out ChatMessage>>>
) {
    val uiState = ChatUIState<BossChatUIState>(
        mState = BossChatUIState(),
        topActions = listOf(
            ChatTopAction.BossRequestResume(
                text = stringResource(id = R.string.chat_top_request_resume),
                iconRes = com.twl.meeboss.chat.R.drawable.chat_icon_resume,
                clickable = true
            ),
            ChatTopAction.BossRequestPhone(
                text = stringResource(id = R.string.chat_top_exchange_phone),
                iconRes = com.twl.meeboss.chat.R.drawable.chat_icon_phone_number,
                clickable = true
            ),
            ChatTopAction.BossRequestEmail(
                text = stringResource(id = R.string.chat_top_exchange_email),
                iconRes = com.twl.meeboss.chat.R.drawable.chat_icon_email,
                clickable = true
            )
        ),
        quickReplies = listOf(),
    )
    BossChatContent(
        modifier = Modifier.fillMaxSize(),
        flow.collectAsLazyPagingItems(),
        uiState = uiState
    )
}

