package com.twl.meeboss.chat.utils

import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.common.ktx.notNull

object ChatPointReporter {

    //用户点击小闹钟进入消息通知列表
    fun f2TabNotificationsClick(){
        PointHelper.reportPoint("f2tab-notifications-click")
    }

    //用户在消息通知列表点击查看一条消息
    fun f2TabNotificationsCheck(messageId:String){
        PointHelper.reportPoint("f2tab-notifications-check"){
            addEncryptP(messageId)
        }
    }

    /**
     * 用户在f2tab点击子列表
     */
    fun f2TabSubListClick(tab:Int){
        PointHelper.reportPoint("f2tab-sublist-click"){
            addP(tab.toString())
        }
    }

    /**
     * 用户在f2tab点击子列表
     */
    fun f2TabPositionClick(jobTitle: String, @JobStatus jobStatus: Int, deleted: Boolean){
        PointHelper.reportPoint("f2tab-position-switch"){
            addP(jobTitle)
            val p2 = if (deleted) {
                "3"
            } else {
                when (jobStatus) {
                    JobStatus.REVIEW_FAILED -> "2"
                    JobStatus.CLOSED -> "1"
                    else -> "0"
                }
            }
            addP2(p2)
        }
    }

    /**
     * 用户点击F2有沟通职位的提示条
     */
    fun f2TabPromptClick(@JobStatus jobStatus: Int, jobId: String) {
        PointHelper.reportPoint("f2tab-prompt-click") {
            when (jobStatus) {
                JobStatus.REVIEW_FAILED -> addP("2")
                JobStatus.CLOSED -> addP("1")
                else -> {}
            }
            addEncryptP2(jobId)
        }
    }

    /**
     * 用户在f2tab点击星标
     */
    fun f2TabStar(friendId: String, starred: Boolean, jobId: String) {
        PointHelper.reportPoint("f2tab-star") {
            addEncryptP(friendId)
            addP2(if (starred) "1" else "2")
            addEncryptP3(jobId)
        }
    }

    /**
     * 打招呼语设置弹窗展示
     * @param source 区分来源：1，开聊；2，点击mark as interested；3，Top Matches
     */
    fun greetingGuideShow(source:Int){
        PointHelper.reportPoint("greeting-guide-show"){
            addP(source.toString())
        }
    }

    /**
     * 打招呼语设置弹窗上，点击自定义编辑招呼语
     * @param source 区分来源：1，开聊；2，点击mark as interested；3，Top Matches
     */
    fun greetingGuideEdit(source:Int){
        PointHelper.reportPoint("greeting-guide-edit"){
            addP(source.toString())
        }
    }

    /**
     * 打招呼语设置弹窗上，点击send按钮
     * @param source 区分来源：1，开聊；2，点击mark as interested；3，Top Matches
     * @param isSelected 是否勾选为默认：1，勾选；2，未勾选
     * @param index 记录勾选了第几条系统推荐招呼语；若自定义返回0。
     */
    fun greetingGuideSend(source:Int,isSelected:Boolean,index:Int){
        PointHelper.reportPoint("greeting-guide-send"){
            addP(source.toString())
            addP2(if (isSelected) "1" else "2")
            addP3(index.toString())
        }
    }

    fun enterChat(source: ChatSource,conversation: Conversation){
        PointHelper.reportPoint("chatwindow-enter"){
            addP(source.value.toString())
            addEncryptP2(conversation.friendId)
            addEncryptP3(conversation.jobId.notNull())
            addEncryptP4(conversation.expectId.notNull())
        }
    }


    /**
     * 引导完善卡片点击
     * @param source 展示来源：1，f1推荐列表；2，JD详情页
     * @param clickAction 1，点击complete去完善；2，点击叉号关闭
     */
    fun completeGuideClick( source: Int,clickAction:Int = 1) {
        PointHelper.reportPoint("complete-guide-click") {
            addP2(source.toString())
            addP3(clickAction.toString())
        }
    }

    /**
     * @param type:1，boss头像；2，牛人头像；3，公司logo；
     * @param source: 场景来源：1，职位详情页；2，简历详情页；3，聊天窗；4，职位模板详情页；5，公司详情
     */
    fun chatAvatarClick(type:Int, source:Int, userId:String, companyId: String) {
        PointHelper.reportPoint("avatar-click") {
            addP(type.toString())
            addP2(source.toString())
            addEncryptP3(userId)
            addEncryptP4(companyId)
        }
    }

    /**
     * 用户点击常用语icon按钮
     * @param actionp:0，无内容；1，有内容
     */
    fun phraseIconClick(actionp:Int) {
        PointHelper.reportPoint("common-phrase-icon") {
            addP(actionp.toString())
        }
    }

    /**
     * 点击常用语消息体
     */
    fun phraseClickItem() {
        PointHelper.reportPoint("common-phrase-click")
    }

    /**
     * 点击常用语发送按钮
     */
    fun phraseClickSend() {
        PointHelper.reportPoint("common-phrase-send")
    }

    /**
     * 进入常用语添加页面
     */
    fun phraseAddPageShow() {
        PointHelper.reportPoint("common-phrase-add")
    }

    /**
     * 常用语页面点击Example按钮
     */
    fun phraseManageExampleClick() {
        PointHelper.reportPoint("common-phrase-example")
    }

    /**
     * 进入常用语管理页面
     */
    fun phraseManagePageShow() {
        PointHelper.reportPoint("common-phrase-manage")
    }

    /**
     * 常用语管理页面点击相应按钮时上报
     * @param type:动作：1，编辑；2，sort；3，左滑删除
     */
    fun phraseManagePageEdit(type:Int) {
        PointHelper.reportPoint("common-phrase-edit") {
            addP(type.toString())
        }
    }

    /**
     * 常用语编辑页面点击保存
     * @param type: 1，编辑；2，新增
     */
    fun phraseEditPageSave(type:Int) {
        PointHelper.reportPoint("common-phrase-save") {
            addP(type.toString())
        }
    }

    /**
     * 聊天框内点击表情图标调起选择器(跟 iOS统一，仅仅调出表情选择器时候埋点)
     */
    fun emojiKeyboardClick(){
        PointHelper.reportPoint("emoji- keyboard")
    }

    /**
     * 长按消息
     */
    fun longClickMessage(){
        PointHelper.reportPoint("chatmessage-press")
    }

    /**
     * @param type 1 复制
     */
    fun chatLongClickAction(type:Int){
        PointHelper.reportPoint("chatmessage-press-click"){
            addP(type.toString())
        }

    }

    /**
     * @param source 按钮页面来源：1，Onboarding First Message引导；2，F2的Job Views Tab；3，无在线职位的F1空列表；4，无在线职位的F3；5，职位管理页面；6，其他
     */
    fun bossClickPostAJob(source:Int,){
        PointHelper.reportPoint("post-a-job"){
            addP(source.toString())

        }
    }

    /**
     * 用户进入Top Matches时上报
     *
     * @param recommendedTopMatchCount 进入时推荐的Top Match个数
     */
    fun f2TabTopMatches(recommendedTopMatchCount: Int) {
        PointHelper.reportPoint("f2tab-topmatches") {
            addP(recommendedTopMatchCount.toString())
        }
    }

    /**
     * 招聘者在Top Matches卡片点击按钮
     *
     * @param jobSeekerUid 对侧Job Seeker uid
     * @param currentJobId 当前Job ID
     * @param buttonType 按钮：1，点击quick message；2，点击Not interested；3，点击退出按钮
     */
    fun topMatchesGeekClick(jobSeekerUid: String, currentJobId: String, buttonType: Int) {
        PointHelper.reportPoint("topmatches-geek-click") {
            addEncryptP(jobSeekerUid)
            addEncryptP2(currentJobId)
            addP3(buttonType.toString())
        }
    }

    /**
     * 求职者在Top Matches页面点击按钮
     *
     * @param recruiterId 对侧Recruiter ID
     * @param jobId Job ID
     * @param buttonType 按钮：1，点击quick message；2，点击Not interested；3，点击退出按钮
     */
    fun topMatchesJobClick(recruiterId: String, jobId: String, buttonType: Int) {
        PointHelper.reportPoint("topmatches-job-click") {
            addEncryptP(recruiterId)
            addEncryptP2(jobId)
            addP3(buttonType.toString())
        }
    }

    /**
     * 点击快速回复
     */
    fun quickReplyClick(quickReply:String){
        PointHelper.reportPoint("quickreply-click"){
            addP(quickReply)
        }
    }

    /**
     * Sponsor同步到职位的弹窗弹出时上报
     * @param answer 来源按钮：0, No; 1, Yes
     * @param jobId Job ID
     */
    fun sponsorPopupShow(answer: Int, jobId: String) {
        PointHelper.reportPoint("sponsor-popup-show") {
            addP(answer.toString())
            addEncryptP2(jobId)
        }
    }

    /**
     * 点击Sponsor同步到职位的弹窗的按钮时上报
     * @param answer 来源按钮：0, No; 1, Yes
     * @param jobId Job ID
     * @param buttonType 点击按钮：0, Not now; 1, Yes
     */
    fun sponsorPopupClick(answer: Int, jobId: String, buttonType: Int) {
        PointHelper.reportPoint("sponsor-popup-click") {
            addP(answer.toString())
            addEncryptP2(jobId)
            addP3(buttonType.toString())
        }
    }
}