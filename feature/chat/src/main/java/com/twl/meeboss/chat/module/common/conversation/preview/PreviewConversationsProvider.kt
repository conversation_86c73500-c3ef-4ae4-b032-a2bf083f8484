package com.twl.meeboss.chat.module.common.conversation.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.chat.export.constant.FriendStatus
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.export.model.Conversation
import java.util.UUID

class PreviewConversationsProvider : PreviewParameterProvider<List<Conversation>> {
    override val values: Sequence<List<Conversation>>
        get() = sequenceOf(getConversationTestList())

    companion object {
        fun getConversationTestList() = listOf(
            Conversation(firstName = "John",
                friendId = UUID.randomUUID().toString(),
                draft = "Hello, world!",
                updateTime = System.currentTimeMillis(),
                friendIdentity = UserConstants.BOSS_IDENTITY,
                companyName = "Google",
                bossPosition = "CEO",
                lastName = "Doe", unreadCount = 0,
                lastMessageContent = "Hello, world!",
                lastMessageStatus = MessageStatus.READ,
                topMatched = true
            ), Conversation(firstName = "Abraham Molly Jesmine",
                friendIdentity = UserConstants.GEEK_IDENTITY,
                lastMessageIsMine = true,
                jobTitle = "Software Engineer",
                companyName = "Facebook",
                friendId = UUID.randomUUID().toString(),
                updateTime = System.currentTimeMillis() - (3 * 60 * 60 * 1000),
                lastName = "Lincoln Shannon", unreadCount = 1,
                lastMessageLocalType = LocalMessageType.MSG_TEXT,
                lastMessageContent = "Hi, there!",
                lastMessageStatus = MessageStatus.SENDING,
                topMatched = true
            ),
            Conversation(firstName = "Abraham",
                friendIdentity = UserConstants.GEEK_IDENTITY,
                lastMessageIsMine = true,
                jobTitle = "Software Engineer",
                companyName = "Facebook",
                friendId = UUID.randomUUID().toString(),
                updateTime = System.currentTimeMillis() - (3 * 60 * 60 * 1000),
                lastName = "Lincoln", unreadCount = 1,
                lastMessageLocalType = LocalMessageType.MSG_TEXT,
                lastMessageContent = "Hi, there!",
                lastMessageStatus = MessageStatus.FAILED
            ),Conversation(firstName = "Jane",
                friendId = UUID.randomUUID().toString(),
                friendIdentity = UserConstants.GEEK_IDENTITY,
                highestEduLevelDesc = "Master",
                schoolName = "Harvard",
                lastName = "Doe", unreadCount = 9,
                updateTime = System.currentTimeMillis() - (24 * 60 * 60 * 1000 + (5 * 60 * 1000)),
                lastMessageContent = "hHhhHHAHAHAHAH",
                friendStatus = FriendStatus.NEW_GREETING,
                lastMessageStatus = MessageStatus.READ
            ), Conversation(firstName = "Donald",
                friendId = UUID.randomUUID().toString(),
                updateTime = System.currentTimeMillis() - (2L * 30 * 24 * 60 * 60 * 1000 + (15 * 24 * 60 * 60 * 1000)),
                lastName = "Trump", unreadCount = 10,
                friendIdentity = UserConstants.GEEK_IDENTITY,
                highestEduLevelDesc = "Master",
                schoolName = "Harvard",
                lastMessageLocalType = LocalMessageType.EXCHANGE_EMAIL_RESULT,
                lastMessageContent = "hHhhHHAHAHAHAH",
                lastMessageStatus = MessageStatus.SENDING,
                topMatched = true
            ), Conversation(firstName = "Steven",
                friendIdentity = UserConstants.GEEK_IDENTITY,
                highestEduLevelDesc = "Master",
                lastMessageLocalType = LocalMessageType.EXCHANGE_PHONE_RESULT,
                schoolName = "Harvard",
                friendId = UUID.randomUUID().toString(),
                updateTime = System.currentTimeMillis() - (365L * 24 * 60 * 60 * 1000 + (3L * 30 * 24 * 60 * 60 * 1000 + (10 * 24 * 60 * 60 * 1000))),
                lastName = "Jobs", unreadCount = 100,
                lastMessageContent = "hHhhHHAHAHAHAH",
                lastMessageStatus = MessageStatus.SENDING
            ),
            Conversation(firstName = "Steven",
                friendIdentity = UserConstants.GEEK_IDENTITY,
                highestEduLevelDesc = "Master",
                lastMessageLocalType = LocalMessageType.EXCHANGE_RESUME_RESULT,
                schoolName = "Harvard",
                friendId = UUID.randomUUID().toString(),
                updateTime = System.currentTimeMillis() - (365L * 24 * 60 * 60 * 1000 + (3L * 30 * 24 * 60 * 60 * 1000 + (10 * 24 * 60 * 60 * 1000))),
                lastName = "Jobs", unreadCount = 100,
                lastMessageContent = "hHhhHHAHAHAHAH",
                lastMessageStatus = MessageStatus.SENDING,
                topMatched = true
            ),Conversation(firstName = "Steven",
                friendIdentity = UserConstants.GEEK_IDENTITY,
                highestEduLevelDesc = "Master",
                lastMessageLocalType = LocalMessageType.INTEREST_GRAY_HINT,
                schoolName = "Harvard",
                friendId = UUID.randomUUID().toString(),
                updateTime = System.currentTimeMillis() - (365L * 24 * 60 * 60 * 1000 + (3L * 30 * 24 * 60 * 60 * 1000 + (10 * 24 * 60 * 60 * 1000))),
                lastName = "Jobs", unreadCount = 100,
                lastMessageContent = "hHhhHHAHAHAHAH",
                lastMessageStatus = MessageStatus.SENDING
            )
        )
    }
}