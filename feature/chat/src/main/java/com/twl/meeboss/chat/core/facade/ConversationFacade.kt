package com.twl.meeboss.chat.core.facade

import com.twl.meeboss.base.ktx.isMySelf
import com.twl.meeboss.base.ktx.launcherOnIO
import com.twl.meeboss.chat.api.ChatApi
import com.twl.meeboss.chat.api.resp.QuickReplyTemplateItem
import com.twl.meeboss.chat.core.bus.MessageEvent
import com.twl.meeboss.chat.core.bus.MessageEventBus
import com.twl.meeboss.chat.core.db.ChatDatabase
import com.twl.meeboss.chat.core.model.MessageCallback
import com.twl.meeboss.chat.core.model.message.ChatMessage
import com.twl.meeboss.chat.core.model.message.EventMessage
import com.twl.meeboss.chat.core.model.message.MessageRecord
import com.twl.meeboss.chat.core.model.message.ReadMessage
import com.twl.meeboss.chat.export.constant.GeneralMsgType
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.export.model.Conversation
import com.twl.meeboss.chat.repos.ChatConversationRepository
import com.twl.meeboss.common.utils.ProcessHelper
import com.twl.meeboss.common.utils.getNewScope
import com.twl.meeboss.core.network.getService
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow

class ConversationFacade : MessageCallback {

    private val scope = getNewScope()

    private val mContactRepository by lazy {
        ChatConversationRepository(ChatDatabase.getInstance(ProcessHelper.getContext()).conversationDao(), getService(ChatApi::class.java))
    }
    private val conversationSyncHandler: ConversationSyncHandler by lazy {
        ConversationSyncHandler(mContactRepository,scope)
    }



    init{
        updateConversations()
    }

    fun updateConversations() {
        conversationSyncHandler.updateConversation()
    }

    fun destroy(){
        scope.cancel("destroy")
    }

    private val lock = Any()

    override fun onSendMessage(messageRecord: ChatMessage) {
        synchronized(lock) {
            scope.launcherOnIO {
                if (messageRecord.bodyType == GeneralMsgType.MSG_EMPTY
                    || messageRecord.bodyType == GeneralMsgType.MSG_ACTION
                ) {
                    return@launcherOnIO
                }
                mContactRepository.updateConversation(
                    messageRecord,
                    isSend = true
                )
            }

        }
    }

    override fun onSendMessageSuccess(message: ChatMessage) {
        synchronized(lock) {
            scope.launcherOnIO {
                if (message.bodyType == GeneralMsgType.MSG_EMPTY
                    || message.bodyType == GeneralMsgType.MSG_ACTION
                ) {
                    return@launcherOnIO
                }
                mContactRepository.updateConversation(
                    message = message,
                    isSend = true
                )
            }

        }
    }

    override fun onReceiveMessage(messageRecord: ChatMessage) {
        synchronized(lock) {
            scope.launcherOnIO {
                if (messageRecord.bodyType == GeneralMsgType.MSG_EMPTY
                    || messageRecord.bodyType == GeneralMsgType.MSG_ACTION
                ) {
                    return@launcherOnIO
                }
                mContactRepository.updateConversation(
                    message = messageRecord,
                    isSend = messageRecord.fromId.isMySelf()
                )
            }

        }

    }

    override fun onSendFailure(message: MessageRecord) {
        scope.launcherOnIO {
            mContactRepository.updateContactLastMessageStatusByCmid(message.cmid, message.chatId, MessageStatus.FAILED)
        }
    }

    override fun onReceiveRead(readMessage: ReadMessage) {
        scope.launcherOnIO {
            if(readMessage.type == 2){
                mContactRepository.updateContactLastMessageStatus(readMessage.mid, readMessage.userId, MessageStatus.READ)
            }else{
                mContactRepository.clearUnreadCount(readMessage.userId)
            }
            MessageEventBus.tryPost(MessageEvent.MessagesReadByFriend(readMessage))
        }
    }

    override fun onSendRead(friendId: String) {
        scope.launcherOnIO {
            mContactRepository.clearUnreadCount(friendId)
            MessageEventBus.tryPost(MessageEvent.UnreadCountCleared(friendId))
        }
    }

    override fun onReceiveEvent(messageEvent: EventMessage, uid: Long) {

    }

    fun getConversationAsFlow(friendId: String): Flow<Conversation?> {
        return mContactRepository.getConversationAsFlow(friendId = friendId)
    }

    suspend fun getConversation(
        friendId: String,
        friendIdentity: Int,
        autoUpdate: Boolean
    ): Conversation {
        return mContactRepository.getConversation(friendId, friendIdentity, autoUpdate)
    }

    suspend fun updateConversationFromNet(conversation: Conversation?) {
        conversation?.let {
            mContactRepository.updateConversationParams(it)
        }
    }

    fun updateConversationFromNet(chatId: String, friendIdentity: Int) {
        scope.launcherOnIO {
            mContactRepository.updateConversationFromNet(chatId, friendIdentity)
        }
    }

    fun getUnreadMessageCount(): Flow<Int> {
        return mContactRepository.getUnreadCount()
    }

    fun getJobsWithConversation(): Flow<Set<String>> {
        return mContactRepository.observeJobIdsWithConversations()
    }

    fun saveDraft(text:String,friendId: String){
        mContactRepository.saveDraft(text,friendId)
    }

    fun starContact(friendId: String, starred: Boolean) {
        mContactRepository.starContact(friendId, starred)
    }

    fun deleteConversation(friendId: String){
        mContactRepository.deleteConversation(friendId)
    }

    suspend fun getQuickReplyTemplate(friendId: String, friendIdentity: Int, jobId: String): Result<List<QuickReplyTemplateItem>> {
        return mContactRepository.getQuickReplyTemplate(friendId, friendIdentity, jobId)
    }

    suspend fun quickReplyTemplateClick(friendId: String, friendIdentity: Int, type: Int, scene: String, mid: Long): Result<Any> {
        return mContactRepository.quickReplyTemplateClick(friendId, friendIdentity, type, scene, mid)
    }
}