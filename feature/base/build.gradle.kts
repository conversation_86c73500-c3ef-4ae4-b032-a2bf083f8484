plugins {
    alias(libs.plugins.twl.android.library)
    alias(libs.plugins.twl.android.library.compose)
    alias(libs.plugins.ksp)
}

android {
    namespace = "com.twl.meeboss.base"
}

dependencies {
    api(projects.core.common)
    api(projects.core.database)
    api(libs.impaassdk)
    api(projects.core.network)
    api(projects.core.ui)
    api(libs.permissionx)
    ksp(libs.wm.router.compiler)
    api(libs.badger)
    api(libs.accompanist.systemuicontroller)
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.pictureSelector)
    implementation(libs.pictureSelector.ucrop)
    implementation(libs.pictureSelector.compress)
    implementation(libs.pictureSelector.camerax)
    implementation(libs.appsflyer)
    implementation(libs.installreferrer)
    //region 市场暂时不接入小米和华为渠道
//    implementation(libs.hms.componentverifysdk)
//    implementation(libs.miui.homereferrer)
    //endregion

    /*firebase依赖版本文档:https://firebase.google.com/docs/android/setup?hl=zh-cn#available-libraries*/
    //region firebase分析
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics.ktx)
    //endregion

    //region firebase谷歌登录
    implementation(libs.firebase.auth.ktx)
    implementation(libs.gms.auth)
    //endregion

    //region 谷歌推送FCM
    implementation(libs.firebase.messaging.ktx)
    //endregion

    //AB字段自动生成类
    implementation(libs.symbol.processing.api)
    implementation(libs.meeboss.processors)
    ksp(libs.meeboss.processors)
    implementation(libs.localhtml){
        isChanging = true
    }
}