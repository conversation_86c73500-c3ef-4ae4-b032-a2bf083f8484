package com.twl.meeboss.base.account

import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.ktx.hasSelectIdentity
import com.twl.meeboss.base.model.LoginResult
import com.twl.meeboss.common.IAccount
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.preference.SpKey
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.provider.UserProvider

class AccountCore : IAccountLifecycle, IAccount {

    private val TAG = AccountCore::class.java.simpleName
    private var mLoginResult: LoginResult = LoginResult()
    private val mAccountLifecycles = mutableListOf<IAccountLifecycle>()

    fun registerAccountLifecycle(accountLifecycle: IAccountLifecycle) {
        mAccountLifecycles.add(accountLifecycle)
    }

    fun unregisterAccountLifecycle(accountLifecycle: IAccountLifecycle) {
        mAccountLifecycles.remove(accountLifecycle)
    }

    override fun onAccountInitialized() {
        mAccountLifecycles.forEach {
            it.onAccountInitialized()
        }
    }

    override fun onIdentityInitialized(identity: Int) {
        mAccountLifecycles.forEach {
            it.onIdentityInitialized(identity)
        }
    }

    override fun onIdentityRelease(identity: Int) {
        mAccountLifecycles.forEach {
            it.onIdentityRelease(identity)
        }
    }


    override fun onAccountRelease() {
        mAccountLifecycles.forEach {
            it.onAccountRelease()
        }
    }

    override fun onSuspend(suspend: Boolean) {
        mAccountLifecycles.forEach {
            it.onSuspend(suspend)
        }
    }

    fun initialized() {
        readConfig()
        onLogin()
    }

    private fun readConfig() {
        try {
            mLoginResult.run {
                SpManager.run {
                    identity = getGlobalInt(SpKey.KEY_U_IDENTITY, 0)
                    ticket = getGlobalString(SpKey.KEY_U_TICKET, "")
                    register = getGlobalBoolean(SpKey.KEY_U_REGISTER, false)
                    secretKey = getGlobalString(SpKey.KEY_U_SECRET_KEY, "")
                    userId = getGlobalString(SpKey.KEY_U_ID, "").notNull()
                    firstCompleteStatus = getGlobalInt(SpKey.KEY_U_FIRST_COMPLETE_STATUS, 0)
                }
            }
            TLog.debug(TAG, "AccountCore readConfig success: ticket:${mLoginResult.ticket} secretKey:${mLoginResult.secretKey} identity:${mLoginResult.identity} register:${mLoginResult.register}")
        } catch (e: Exception) {
            TLog.info(TAG, "AccountCore readConfig error: ${e.message}")
        }
    }

    private fun onLogin() {
        if (isLogin()) {
            UserProvider.setAccount(this)
            onAccountInitialized()
            if (mLoginResult.identity.hasSelectIdentity()) {
                onIdentityInitialized(mLoginResult.identity)
            }
        } else {
            TLog.info(TAG, "AccountCore onLogin fail: not login,ticket:${mLoginResult.ticket} secretKey:${mLoginResult.secretKey}")
        }
    }

    fun activateAccount(loginResult: LoginResult, needSave: Boolean = true) {
        mLoginResult = loginResult.copy()
        if (needSave) {
            restoreLoginResult(mLoginResult)
        }
        onLogin()
    }

    fun activeCurrentAccount() {
        restoreLoginResult(mLoginResult)
        onLogin()
    }

    fun setIdentity(identity: Int) {
        val preIdentity = mLoginResult.identity
        mLoginResult = mLoginResult.copy(identity = identity)
        SpManager.putGlobalInt(SpKey.KEY_U_IDENTITY, identity)
        if (preIdentity != identity) {
            if (preIdentity.hasSelectIdentity()) {
                onIdentityRelease(preIdentity)
            }
            if (identity.hasSelectIdentity()) {
                onIdentityInitialized(identity)
            }
        }

    }

    /**
     * 1.切换身份后需要根据后端返回的数据覆盖此状态
     * 2.登录之后根据LoginResult更新此状态
     * 3.首善完成需要本地覆盖此状态
     * 4.登录之后
     */
    fun setFirstCompleteStatus(firstCompleteStatus: Int) {
        mLoginResult = mLoginResult.copy(firstCompleteStatus = firstCompleteStatus)
        SpManager.putGlobalInt(SpKey.KEY_U_FIRST_COMPLETE_STATUS, firstCompleteStatus)
    }

    fun getFirstCompleteStatus(): Int {
        return mLoginResult.firstCompleteStatus
    }

    private fun restoreLoginResult(loginResult: LoginResult) {
        SpManager.run {
            putGlobalString(SpKey.KEY_U_TICKET, loginResult.ticket)
            putGlobalString(SpKey.KEY_U_SECRET_KEY, loginResult.secretKey)
            putGlobalInt(SpKey.KEY_U_IDENTITY, loginResult.identity)
            putGlobalBoolean(SpKey.KEY_U_REGISTER, loginResult.register)
            putGlobalString(SpKey.KEY_U_ID, loginResult.userId)
            putGlobalInt(SpKey.KEY_U_FIRST_COMPLETE_STATUS, loginResult.firstCompleteStatus)
        }
    }

    override fun isLogin(): Boolean {
        return !mLoginResult.ticket.isNullOrBlank() && !mLoginResult.secretKey.isNullOrBlank()
    }

    override fun getIdentify(): Int {
        return mLoginResult.identity
    }

    override fun isGeek(): Boolean {
        return mLoginResult.identity == UserConstants.GEEK_IDENTITY
    }

    override fun isBoss(): Boolean {
        return mLoginResult.identity == UserConstants.BOSS_IDENTITY
    }

    override fun userIdStr(): String {
        return mLoginResult.userId
    }

    override fun userIdNumber(): Long {
        return 0
    }

    override fun getTicket(): String? {
        return mLoginResult.ticket
    }

    override fun getSecretKey(): String? {
        return mLoginResult.secretKey
    }

    fun logout() {
        val preIdentity = mLoginResult.identity
        mLoginResult = LoginResult()
        SpManager.run {
            putGlobalString(SpKey.KEY_U_TICKET, "")
            putGlobalString(SpKey.KEY_U_SECRET_KEY, "")
            putGlobalInt(SpKey.KEY_U_IDENTITY, UserConstants.NONE_IDENTITY)
            putGlobalBoolean(SpKey.KEY_U_REGISTER, false)
            putGlobalLong(SpKey.KEY_U_ID, 0L)
            putGlobalString(SpKey.KEY_U_PAAS_UID, "")
            putGlobalString(SpKey.KEY_U_PAAS_USER_TOKEN, "")
        }
        onIdentityRelease(preIdentity)
        onAccountRelease()
    }
}