package com.twl.meeboss.base.components.dialog

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.core.network.HttpResult
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.dialog.DialogChooseUIType

/**
 * 基础数据viewModel
 */
class CommonDataViewModel : BaseViewModel() {
    private val cache: MutableMap<CommonDataType, CacheCommonData> = mutableMapOf()

    val commonData: MutableLiveData<CacheCommonData?> = MutableLiveData()


    fun getCommonDataByType(type: CommonDataType) {
        val cacheData = cache[type]
        if (cacheData != null && cacheData.data.isNotEmpty()) {
            commonData.postValue(cacheData)
            return
        }
        async {
            val api = getService(BaseApi::class.java)
            val result = if(type == CommonDataType.GEEK_EDUCATION_LEVEL) api.commonDegree() else api.commonListByType(type.type.toString())
            if (result.isSuccess) {
                var dataList = mutableListOf(*(result.getOrNull()?.list?.toTypedArray() ?: arrayOf()))
                val resultData = CacheCommonData(type, dataList)
                cache[type] = resultData
                commonData.postValue(resultData)
                //发送成功
            } else {
                when (result) {
                    is HttpResult.ApiError -> {
                        //                                    T.ss(result.message)
                    }

                    is HttpResult.NetworkError -> {
                        //                                    T.ss(result.error.message)
                    }

                    else -> {

                    }
                }
            }
        }
    }
}

data class CacheCommonData(val type: CommonDataType, val data: List<OptionBean>)

sealed class CommonDataType(
    val type: Int,
    val optional: Boolean = false,
    val selectUIType: @DialogChooseUIType Int = DialogChooseUIType.TYPE_NORMAL,
) {
    data object EDUCATION_LEVEL : CommonDataType(1) //学历等级
    data object LANGUAGE_FLUENCY : CommonDataType(2, optional = false) //语言流畅度
    data object WORKPLACE_TYPE : CommonDataType(3) //workplace type
    data object EMPLOYMENT_TYPE : CommonDataType(4) //employment type
    data object WORK_EXPERIENCE : CommonDataType(5) //expLevel
    data object LEVEL_OF_ROLE : CommonDataType(5) //expLevel
    data object SALARY_TYPE : CommonDataType(6) //salary range
    data object COMPANY_SIZE_TYPE : CommonDataType(7) //company size type
    data object GEEK_EDUCATION_LEVEL : CommonDataType(9) //c 学历等级
    data object WORK_EXPERIENCE_JOB : CommonDataType(10) //发职位Experience level
    data object READY_TO_WORK : CommonDataType(11, optional = false, DialogChooseUIType.TYPE_TICK)
    data object BENEFITS: CommonDataType(15) // 福利
    data object GEEK_VISA_SPONSORSHIP : CommonDataType(16, optional = false)//牛人是否需要签证担保
    data object BOSS_VISA_SPONSORSHIP : CommonDataType(17, optional = false) //Boss是否提供签证担保
}
