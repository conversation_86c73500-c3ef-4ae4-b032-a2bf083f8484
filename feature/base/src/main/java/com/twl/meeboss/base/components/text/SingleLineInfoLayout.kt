package com.twl.meeboss.base.components.text

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.component.layout.FlowLayout
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.COLOR_CCCCCC

@Composable
fun SingleLineInfoLayout(modifier: Modifier = Modifier, list: List<String>) {
    FlowLayout(
        modifier = modifier,
        maxLine = 1
    ) {
        list.withIndex().forEach { (index, value) ->
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (index != 0) {
                    VerticalDivider(
                        modifier = Modifier
                            .padding(horizontal = 8.dp)
                            .height(10.dp),
                        thickness = 0.5.dp,
                        color = COLOR_CCCCCC
                    )
                }
                Text(
                    text = value,
                    color = Black484848,
                    fontSize = 13.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewSingleLineInfoLayout() {
    Column(modifier = Modifier
        .fillMaxSize()
        .background(Color.White)) {
        SingleLineInfoLayout(modifier = Modifier.padding(15.dp), listOf("Android", "Java", "Kotlin", "Jetpack"))
    }
}