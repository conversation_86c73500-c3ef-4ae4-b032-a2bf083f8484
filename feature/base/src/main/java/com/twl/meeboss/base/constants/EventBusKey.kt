package com.twl.meeboss.base.constants

class EventBusKey {
    companion object{
        //首页tab切换
        const val CHANGE_MAIN_TAB = "change_main_tab"

        const val UPDATE_USER_PHONE_NUMBER = "update_user_phone_number"
        //C添加好友成功
        const val GEEK_ADD_FRIEND_SUCCESS = "geek_add_friend_success"
        //C端jobDetail页面添加好友成功(因为jd接口返回的securityId和外面传进来的securityId不同,所以只能在jd页做一次中转)
        const val JD_GEEK_ADD_FRIEND_SUCCESS = "jd_geek_add_friend_success"
        //C端收藏职位状态改变
        const val GEEK_COLLECT_JOB_CHANGE = "GEEK_COLLECT_JOB_CHANGE"
        //B添加好友成功
        const val BOSS_ADD_FRIEND_SUCCESS = "boss_add_friend_success"
        //B端geekDetail页面添加好友成功(因为geekDetail接口返回的securityId和外面传进来的securityId不同,所以只能在geekDetail页做一次中转)
        const val BOSS_CANDIDATE_DETAIL_ADD_FRIEND_SUCCESS = "boss_candidate_detail_add_friend_success"
        //B端收藏牛人状态改变
        const val BOSS_COLLECT_CANDIDATE_CHANGE = "boss_collect_candidate_change"
        //C编辑工作经验成功
        const val GEEK_EDIT_WORK_EXPERIENCE_SUCCESS = "geek_edit_work_experience_success"
        //C 端领英广告事件
        const val GEEK_LINKEDIN_ACTIVATION = "geek_linked_activation"
        //C 端待改进项更新事项
        const val GEEK_IMPROVEMENT_COUNT = "geek_improvement_count"
        //编辑技能
        const val GEEK_EDIT_SKILL_SUCCESS = "geek_edit_skill_success"
        //C编辑教育经历成功
        const val GEEK_EDIT_EDU_EXPERIENCE_SUCCESS = "geek_edit_edu_experience_success"
        //C编辑项目经验成功
        const val GEEK_EDIT_PROJECT_EXPERIENCE_SUCCESS = "geek_edit_project_experience_success"
        //C新手流程三项完善
        const val GEEK_BEGINNER_GUIDANCE_COMPLETE = "geek_beginner_guidance_complete"
        //C编辑语言成功
        const val GEEK_EDIT_LANGUAGE_SUCCESS = "geek_edit_language_success"
        //C编辑证书成功
        const val GEEK_EDIT_CERTIFICATION_SUCCESS = "geek_edit_certification_success"
        //联系人拉入黑名单
        const val CONTACT_BE_ADD_TO_BLACKLIST = "contact_be_add_to_blacklist"
        //删除联系人
        const val CONTACT_BE_DELETE = "contact_be_delete"
        //H5编辑职位描述成功
        const val H5_JOB_EDIT_DESCRIPTION_RESULT = "h5_job_edit_description_result"
        //移除C F1插入的卡片
        const val GEEK_REMOVE_F1_INSERTED_CARD = "geek_remove_f1_inserted_card"
        //bossUserInfo和geekUserInfo获取完成
        const val GET_USER_INFO_FINISH = "get_user_info_finish"
        //编辑个人姓名成功
        const val EDIT_USER_NAME_SUCCESS = "edit_user_name_success"
        //认证邮箱成功
        const val EMAIL_VERIFIED_SUCCESS = "email_verified_successfully"
        //C端公司详情页加入/退出人才池, 发event通知首页更新数据
        const val GEEK_TALENT_POOL_STATUS_CHANGE = "geek_talent_pool_status_change"
        //通知开关在设置页发生变更
        const val BOSS_NOTIFICATION_CHANGED = "boss_notification_changed"
        //高度匹配红点同步
        const val TOP_MATCHES_READ_DOT_SYNC= "top_matches_read_dot_sync"
        //高匹配标记不感兴趣
        const val NOT_INTERESTED_IN_CANDIDATE = "not_interested_in_candidate"
        //同步快捷回复
        const val SYNC_QUICK_REPLIES = "sync_quick_replies"
    }
}