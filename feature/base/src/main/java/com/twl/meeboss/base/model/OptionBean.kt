package com.twl.meeboss.base.model

import androidx.annotation.Keep

@Keep
data class OptionBean(val code: Long = 0L,
                      val name: String = "",
                      val highlights: List<Highlight>? = listOf(),
                      val required: Boolean = true,
                      var localExclusive: Boolean = false //是否是互斥的，默认不是
) : BaseEntity

@Keep
/**
 * 用于格式化提交数据bean, 避免提交冗余数据
 */
data class SimpleOptionBean(val parentCode: Long, val code:Long,val name: String):BaseEntity
