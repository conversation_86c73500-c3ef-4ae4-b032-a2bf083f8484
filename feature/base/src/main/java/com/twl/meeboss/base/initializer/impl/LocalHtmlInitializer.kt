package com.twl.meeboss.base.initializer.impl

import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.Utils
import com.hpbr.directhires.module.localhtml.InitConfig
import com.hpbr.directhires.module.localhtml.LocalHtmlService
import com.hpbr.directhires.module.localhtml.log.LLog
import com.hpbr.directhires.module.localhtml.request.CommonHeaderProvider
import com.twl.meeboss.base.initializer.IInitializer
import com.twl.meeboss.common.base.AppConfig
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.common.utils.ProcessHelper
import com.twl.meeboss.core.network.HttpCore
import com.twl.meeboss.core.network.HttpParamsProvider.httpAppVersionName
import com.twl.meeboss.core.network.config.HttpConfigManager


class LocalHtmlInitializer : IInitializer {
    companion object {
        private const val TAG = "LHtml:initializer"
    }

    override fun init(appConfig: AppConfig) {
        if (ProcessHelper.isMainProcess()) {
            LocalHtmlService.init(
                InitConfig
                    .Builder(Utils.getApp())
                    .addInterceptor(HttpCore.interceptors.firstOrNull())
                    .log(object : LLog.ILog {
                        override fun log(level: Int, tag: String, content: String) {
                            try {
                                when (level) {
                                    LLog.LEVEL_DEBUG -> XLog.debug(tag, content)
                                    LLog.LEVEL_ERROR -> XLog.error(tag, content)
                                    else -> XLog.info(tag, content)
                                }
                            } catch (e: Exception) {
                                XLog.error(TAG, "log error " + e.message)
                            }
                        }

                    })
                    .commonHeaderProvider(object :CommonHeaderProvider{
                        override fun provide(): Map<String, String>? {
                            val t2: String = UserProvider.getTicket()
                            return if (t2.isEmpty()) {
                                null
                            } else {
                                mapOf("t" to t2)
                            }
                        }
                    })
                    .build()
            )
            LocalHtmlService.syncConfigs("${HttpConfigManager.getNetEnvironment().baseHttpUrl}api/upgrade/h5/check?version=${AppUtils.getAppVersionCode().httpAppVersionName()}")
        }
    }
}