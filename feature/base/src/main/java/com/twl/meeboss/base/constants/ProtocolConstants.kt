package com.twl.meeboss.base.constants

class ProtocolConstants {
    companion object{
        //
        const val MEE_BOSS_ONELINK_DOMAIN = "meeboss.onelink.me"

        const val MEE_BOSS_URL_PREFIX = "meeboss"
        //首页tab
        const val TYPE_MAIN_TAB = "main_tab"
        //打开web页面
        const val TYPE_WEB = "web"
        //打开C开聊设置
        const val TYPE_C_GREETING_SETTING = "jobseekerGreetingSetting"
        //打开B开聊设置
        const val TYPE_B_GREETING_SETTING = "bossGreetingSetting"
        //打开C我收藏的职位
        const val ACTION_C_MY_JOBS = "myJobs"
        //打开B我收藏的候选人
        const val ACTION_B_MY_CANDIDATE = "myCandidate"
        //系统通知页面
        const val SYSTEM_NOTIFICATION = "systemNotification"
        //C端个人信息编辑
        const val GEEK_PERSON = "jobseekerPerson"
        //C端资料编辑
        const val GEEK_PROFILE = "jobseekerProfile"
        //B端编辑职位  meeboss://meeboss.app/openwith?type=editJob&jobId=abc
        const val BOSS_EDIT_JOB = "editJob"
        //B端公司详情
        const val BOSS_COMPANY_PAGE = "bossCompanyPage"
        //C新手引导流程
        const val GEEK_BEGINNER_GUIDE = "jobSeekerBeginnerGuide"
        //C新手引导-添加工作经验
        const val GEEK_BEGINNER_GUIDE_ADD_WORK_EXP = "jobSeekerBeginnerAddWorkExp"
        //C新手引导-添加教育经验
        const val GEEK_BEGINNER_GUIDE_ADD_EDU_EXP = "jobSeekerBeginnerAddEduExp"
        //C新手引导-添加姓名
        const val GEEK_BEGINNER_GUIDE_ADD_NAME = "jobSeekerBeginnerAddName"
        //C信息完善-添加技能
        const val GEEK_IMPROVEMENT_SKILL = "addSkills"
        //C信息完善-编辑工作经历
        const val GEEK_IMPROVE_WORK_EXP = "editWorkExp"
        //C信息完善-改进建议
        const val GEEK_IMPROVEMENT_SUGGESTION = "improvementSuggestions"
        //聊天详情
        const val CHAT_DETAIL = "chatView"
        //B端个人信息页面
        const val BOSS_PERSON_INFO = "bossPersonInfo"
        //C端职位详情页
        const val GEEK_JOB_DETAIL = "h5_jobDetail"
        // B端公司简介
        const val BOSS_COMPANY_PROFILE = "bossCompanyProfile"
        // C端公司简介
        const val GEEK_COMPANY_PROFILE = "geekCompanyProfile"
        // 根据 index 进入B端 tab 页面， 0 是 F1 1.10 后双端支持
        const val BOSS_TAB = "bossTab"
        // 根据 index 进入C端 tab 页面， 0 是 F1 1.10 后双端支持
        const val JOB_SEEKER_TAB = "jobseekerTab"
        // 新开webview
        const val WEB_VIEW = "webview"
    }
}