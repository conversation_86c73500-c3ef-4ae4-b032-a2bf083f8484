package com.twl.meeboss.geek.module.job.detail.components

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.model.JobTabBean
import com.twl.meeboss.export_share.preview.JobDetailPreviewParameterProvider

/**
 * @author: 冯智健
 * @date: 2024年07月05日 18:23
 * @description:
 */
@Preview
@Composable
fun JobDetailContent(
    @PreviewParameter(JobDetailPreviewParameterProvider::class)
    jobDetail: JobDetailResult,
    modifier: Modifier = Modifier.background(Color.White).padding(horizontal = 16.dp).padding(top = 22.dp),
    scrollState: ScrollState = rememberScrollState(),
    onPreviewRecruiterAvatar: (uid: String, avatar: String) -> Unit = { _, _ -> },
    onShowCompanyDetail: () -> Unit = {},
    onCloseTabClick: (JobTabBean) -> Unit = {},
) {
    Column(
        modifier = modifier
            .verticalScroll(scrollState)
    ) {
        jobDetail.jobDetailJobInfo?.let {
            JobDetailHeaderCard(it)
        }
        jobDetail.jobDetailBossInfo?.let {
            JobDetailBossCard(it) {
                onPreviewRecruiterAvatar(it.userId ?: "", it.avatar ?: "")
            }
        }
        jobDetail.jobDetailJobInfo?.benefits?.takeIf { it.isNotEmpty() } ?.let {
            JobDetailBenefitsCard(benefits = it)
        }
        jobDetail.jobDetailJobInfo?.jobDesc?.takeIf { it.isNotBlank() }?.let {
            JobDetailDescCard(it, jobDetail.jobDetailJobInfo?.jobDescStyle)
        }
        jobDetail.jobDetailJobInfo?.let {
            JobDetailMoreCard(it)
        }
        jobDetail.tab?.run {
            JobDetailBeginnerGuideCard(modifier = Modifier.padding(bottom = 28.dp), bean = this) {
                onCloseTabClick(it)
            }
        }
        jobDetail.jobDetailCompanyInfo?.let {
            JobDetailCompanyCard(it) {
                onShowCompanyDetail()
            }
        }
    }
}