package com.twl.meeboss.geek.module.preference.activity

import android.app.Activity
import android.content.Intent
import android.view.KeyEvent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.components.dialog.CommonDataType
import com.twl.meeboss.base.components.dialog.DialogHelper
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_FROM
import com.twl.meeboss.base.constants.BUNDLE_LONG
import com.twl.meeboss.base.constants.BUNDLE_OBJECT
import com.twl.meeboss.base.constants.BUNDLE_SALARY_UNIT
import com.twl.meeboss.base.constants.BUNDLE_SALARY_UNIT_DESC
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.alertContentChangesDialog
import com.twl.meeboss.base.ktx.stringResourceWithOption
import com.twl.meeboss.base.ktx.toMoneyString
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.COLOR_000000
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.module.preference.viewmodel.GeekJobPreferenceUiIntent
import com.twl.meeboss.geek.module.preference.viewmodel.GeekJobPreferenceUiState
import com.twl.meeboss.geek.module.preference.viewmodel.GeekJobPreferenceViewModel
import com.twl.meeboss.geek.module.preference.viewmodel.getVisaSponsorshipDesc
import com.twl.meeboss.geek.module.profile.components.GeekEditCommonItem
import dagger.hilt.android.AndroidEntryPoint

/**
 * C端期望管理页面
 */
@AndroidEntryPoint
@RouterPage(path = [GeekRouterPath.GEEK_JOB_PREFERENCE_PAGE])
class GeekJobPreferenceActivity : BaseMviActivity<GeekJobPreferenceViewModel>(){
    override val viewModel: GeekJobPreferenceViewModel by viewModels()


    companion object {
        const val REQ_CODE_JOB_TITLE = 10001
        const val REQ_CODE_JOB_LOCATION = 10002
        const val REQ_CODE_JOB_SALARY = 10003

        const val BUNDLE_EXTRA_SALARY_TYPE = "salary_type"
        const val BUNDLE_EXTRA_MIN_SALARY = "min_salary"
    }

    private val dialogHelper by lazy {
        DialogHelper(this)
    }
    private var from = ""

    override fun preInit(intent: Intent) {
        from = intent.getStringExtra(BUNDLE_FROM) ?: ""
        viewModel.setFrom(from)
        viewModel.saveSuccess.observe(this) {
            finish()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackClick()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    private fun onBackClick() {
        if (viewModel.uiStateFlow.value.canSave) {
            alertContentChangesDialog(
                onConfirm = {
                    finish()
                }
            )
        } else {
            finish()
        }
    }

    override fun initData() {
        viewModel.sendUiIntent(GeekJobPreferenceUiIntent.GetJobPreferenceData)
    }

    @Composable
    override fun ComposeContent() {
        val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()
        GeekJobPreferencePage(
            from = from,
            uiState = uiState,
            onClickBack = this::onBackClick,
            showMultiChoiceDialog = {
                showMultiChoiceDialog(it)
            },
            showSingleChoiceDialog = {
                showSingleChoiceDialog(it)
            },
            onClickSave = {
                viewModel.sendUiIntent(GeekJobPreferenceUiIntent.Save)
            },
            onDeleteReadyToWorkClick = {
                viewModel.sendUiIntent(GeekJobPreferenceUiIntent.DeleteReadyToWork)
            },
            onDeleteVisaSponsorship = {
                viewModel.sendUiIntent(GeekJobPreferenceUiIntent.DeleteVisaSponsorship)
            }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQ_CODE_JOB_TITLE && resultCode == RESULT_OK && data != null) {
            viewModel.sendUiIntent(GeekJobPreferenceUiIntent.OnJobTitleChange(data.getSerializableExtra(BUNDLE_OBJECT) as ArrayList<HighlightBean>))
        } else if (requestCode == REQ_CODE_JOB_LOCATION && resultCode == RESULT_OK && data != null) {
            viewModel.sendUiIntent(GeekJobPreferenceUiIntent.OnCityChange(data.getSerializableExtra(BUNDLE_OBJECT) as ArrayList<CityBean>))
        } else if (requestCode == REQ_CODE_JOB_SALARY && resultCode == RESULT_OK && data != null) {
            viewModel.sendUiIntent(GeekJobPreferenceUiIntent.OnSalaryChange(
                data.getLongExtra(BUNDLE_LONG,0L),
                data.getSerializableExtra(BUNDLE_OBJECT) as OptionBean,
                data.getLongExtra(BUNDLE_SALARY_UNIT,0L),
                data.getStringExtra(BUNDLE_SALARY_UNIT_DESC)
            ))
        }
    }

    private fun showMultiChoiceDialog(itemType: CommonDataType) {
        if (itemType!= CommonDataType.WORKPLACE_TYPE && itemType != CommonDataType.EMPLOYMENT_TYPE) {
            TLog.error(TAG,"showMultiChoiceDialog type not match:${itemType}")
            return
        }
        dialogHelper.showMultiChoiceDialog(
            type = itemType,
            defaultValue = viewModel.getSelectListByType(itemType).map { it.name },
            hasEnable = false,
            callback = {
                viewModel.sendUiIntent(GeekJobPreferenceUiIntent.SetSelectListByType(itemType, it))
            },
        )
    }

    private fun showSingleChoiceDialog(itemType: CommonDataType) {
        dialogHelper.showSingleChoiceDialog(
            type = itemType,
            defaultValue = viewModel.getSelectValueByType(itemType),
            callback = {
                viewModel.sendUiIntent(GeekJobPreferenceUiIntent.SetSingleOptionByType(itemType,it))
            },
        )
    }
}

@Preview
@Composable
fun PreviewJobPreferencePage() {
    GeekJobPreferencePage(from = "", GeekJobPreferenceUiState())
}

@Composable
fun GeekJobPreferencePage(
    from: String,
    uiState: GeekJobPreferenceUiState,
    modifier: Modifier = Modifier,
    onClickBack: () -> Unit = {},
    showMultiChoiceDialog: (itemType: CommonDataType) -> Unit = {},
    showSingleChoiceDialog: (itemType: CommonDataType) -> Unit = {},
    onClickSave: () -> Unit = {},
    onDeleteReadyToWorkClick: () -> Unit = {},
    onDeleteVisaSponsorship: () -> Unit = {}
) {
    val context = LocalContext.current
    XTheme {
        Column(
            modifier = modifier
                .background(Color.White)
                .fillMaxSize()
        ) {

            XTitleBar(onBackClick = {
                onClickBack()
            })

            Column(
                Modifier
                    .weight(1f)
                    .verticalScroll(
                        rememberScrollState()
                    )
            ) {

                Text(
                    text = stringResource(id = R.string.geek_job_preference),
                    modifier = modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp, top = 20.dp),
                    fontSize = 28.sp,
                    color = COLOR_000000,
                    textAlign = TextAlign.Start,
                )
                Text(
                    text = stringResource(id = R.string.geek_job_preference_page_desc),
                    modifier = modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp, end = 16.dp, top = 8.dp),
                    fontSize = 13.sp,
                    color = Black484848,
                    textAlign = TextAlign.Start,
                )

                //职位名称
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        GeekEditJobTitleActivity.intentForResult(
                            context as Activity,
                            GeekJobPreferenceActivity.REQ_CODE_JOB_TITLE,
                            uiState.jobTitles,
                            from = from
                        )
                    },
                    title = stringResource(id = R.string.job_seeker_job_preference_role),
                    content = uiState.jobTitles.joinToString(",") { it.name },
                    placeHolder = stringResource(id = R.string.job_seeker_job_preference_other_enter))

                //工作地点
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        GeekEditJobLocationActivity.intentForResult(
                            context as Activity,
                            GeekJobPreferenceActivity.REQ_CODE_JOB_LOCATION,
                            uiState.cities,
                            from = from
                        )
                    },
                    title = stringResource(id = R.string.job_location),
                    content = uiState.cities.joinToString(",") { it.name },
                    placeHolder = stringResource(id = R.string.job_enter_location))

                //期望薪资
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        GeekEditSalaryActivity.intentForResult(
                            context as Activity,
                            GeekJobPreferenceActivity.REQ_CODE_JOB_SALARY,
                            uiState.minSalary,
                            uiState.salaryType,
                            uiState.salaryUnit,
                            uiState.salaryUnitDesc,
                            from = from
                        )
                    },
                    title = stringResource(id = R.string.geek_job_preference_item_salary_expectations),
                    content = if (uiState.minSalary > 0) "${uiState.salaryUnitDesc}${uiState.minSalary.toMoneyString()}/${uiState.salaryTypeDesc}" else "",
                    placeHolder = stringResource(id = R.string.geek_job_preference_item_salary_expectations))

                //Visa Sponsorship
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        showSingleChoiceDialog(CommonDataType.GEEK_VISA_SPONSORSHIP)
                    },
                    title = stringResourceWithOption(id = R.string.job_seeker_job_prep_visa_sponsor),
                    content = uiState.visaSponsorship.getVisaSponsorshipDesc() ,
                    placeHolder = stringResource(id = R.string.job_seeker_job_prep_visa_sponsor_ask),
                    showDeleteIcon = true,
                    onDeleteClick = onDeleteVisaSponsorship)

                //locationType
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        showMultiChoiceDialog(CommonDataType.WORKPLACE_TYPE)
                    },
                    title = stringResourceWithOption(id = R.string.common_location_type),
                    content = uiState.locationTypes.joinToString(",") { it.name },
                    placeHolder = stringResource(id = R.string.geek_register_desc_location_type))

                //employmentType
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        showMultiChoiceDialog(CommonDataType.EMPLOYMENT_TYPE)
                    },
                    title = stringResourceWithOption(id = R.string.common_employment_type),
                    content = uiState.jobTypes.joinToString(",") { it.name },
                    placeHolder = stringResource(id = R.string.job_select_employment_type))

                //levelOfRole
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        showSingleChoiceDialog(CommonDataType.LEVEL_OF_ROLE)
                    },
                    title = stringResourceWithOption(id = R.string.geek_register_title_level_of_role),
                    content = uiState.expLevels.joinToString(",") { it.name },
                    placeHolder = stringResource(id = R.string.geek_register_desc_level_of_role))

                //ready to work
                GeekEditCommonItem(modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .noRippleClickable {
                        showSingleChoiceDialog(CommonDataType.READY_TO_WORK)
                    },
                    title = stringResourceWithOption(id = R.string.common_read_to_work),
                    content = uiState.readyToWorkDesc,
                    placeHolder = stringResource(id = R.string.geek_job_preference_hint_ready_to_work),
                    showDeleteIcon = true,
                    onDeleteClick = onDeleteReadyToWorkClick,
                )
            }

            XCommonButton(
                text = stringResource(id = R.string.common_button_save),
                modifier = Modifier.padding(16.dp),
                enabled = uiState.jobTitles.isNotEmpty() && uiState.cities.isNotEmpty() && uiState.minSalary > 0,
                onClick = onClickSave
            )
        }
    }
}