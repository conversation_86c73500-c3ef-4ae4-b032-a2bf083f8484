package com.twl.meeboss.geek.module.job.detail.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.export_share.model.JobDetailJobInfo
import com.twl.meeboss.export_share.preview.JobPreviewPreviewData
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.core.ui.component.text.XVariableSizeText
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.module.job.detail.VisaSponsorshipDescDialog
import com.twl.meeboss.geek.utils.GeekPointReporter

/**
 * @author: 冯智健
 * @date: 2024年07月05日 13:56
 * @description:
 */
@Composable
fun JobDetailHeaderCard(jobDetailJobInfo: JobDetailJobInfo) {
    XVariableSizeText(
        modifier = Modifier.padding(bottom = 16.dp),
        text = jobDetailJobInfo.jobTitle ?: "",
        fontSizePair = Pair(26.sp, 22.sp),
        overLine = 4,
        color = Black222222,
        fontWeight = FontWeight.SemiBold,
    )
    jobDetailJobInfo.jobType?.joinToString { it.name.toString() }?.takeIf { it.isNotBlank() }?.let {
        JobDetailHeadBar(
            drawableRes = R.drawable.ui_wait_gray_icon,
            text = it
        )
    }
    jobDetailJobInfo.locationType?.name?.takeIf { it.isNotBlank() }?.let {
        JobDetailHeadBar(
            drawableRes = R.drawable.ui_location_type_gray,
            text = it
        )
    }
    jobDetailJobInfo.address?.joinToString { it.name.toString() }?.takeIf { it.isNotBlank() }?.let {
        JobDetailHeadBar(
            drawableRes = R.drawable.ui_address_gray_icon,
            text = it
        )
    }
    jobDetailJobInfo.visaSponsored?.takeIf { it != 0 }?.let {
        val context = LocalContext.current
        Row() {
            JobDetailHeadBar(
                drawableRes = R.drawable.ui_icon_visa_sponsor,
                text = it.visaSponsorshipDesc()
            )
            Image(
                painter = painterResource(id = R.drawable.base_icon_title_tips),
                contentDescription = "",
                modifier = Modifier
                    .padding(start = 6.dp, top = 4.dp)
                    .noRippleClickable {
                        if (context is FragmentActivity) {
                            VisaSponsorshipDescDialog.newInstance(it).showSafely(context)
                        }
                        GeekPointReporter.jobDetailVisaClick(it)
                    }
            )
        }
    }
    jobDetailJobInfo.apply {
        Text(
            modifier = Modifier.padding(top = 12.dp, bottom = 28.dp),
            text = salaryShortDesc.notNull(),
            color = Secondary,
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
private fun Int.visaSponsorshipDesc() = when(this){
    1 -> stringResource(R.string.job_seeker_job_detail_visa_sponsored)
    2 -> stringResource(R.string.job_seeker_job_detail_visa_sponsored_no)
    3 -> stringResource(R.string.job_seeker_job_detail_visa_sponsored_likely)
    else -> ""
}

@Composable
private fun JobDetailHeadBar(@DrawableRes drawableRes: Int, text: String) {
    Row(
        modifier = Modifier.padding(bottom = 8.dp)
    ) {
        Image(
            modifier = Modifier
                .padding(end = 8.dp, top = 4.dp)
                .widthIn(min = 18.dp),
            painter = painterResource(id = drawableRes),
            contentDescription = ""
        )
        Text(
            text = text,
            color = Black484848,
            fontSize = 14.sp
        )
    }
}

@Preview
@Composable
private fun PreviewJobDetailHeaderCard() {
    Column(
        modifier = Modifier
            .background(Color.White)
            .padding(horizontal = 16.dp)
    ) {
        JobPreviewPreviewData.jobDetail.jobDetailJobInfo?.let {
            JobDetailHeaderCard(it)
        }
    }
}