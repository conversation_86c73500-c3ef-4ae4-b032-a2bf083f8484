package com.twl.meeboss.geek.module.main

import android.os.Bundle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.graphics.toArgb
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.fragment.BaseMviFragment
import com.twl.meeboss.base.function.FunctionsManager
import com.twl.meeboss.base.model.MainTabBean
import com.twl.meeboss.chat.export.ChatServiceRouter
import com.twl.meeboss.core.ui.theme.COLOR_354848
import com.twl.meeboss.core.ui.utils.observeLifecycleEvents
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.statusBarColor
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.BeginnerGuidanceType
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GuidanceType
import com.twl.meeboss.geek.foundation.moduleservice.GeekUserInfoService
import com.twl.meeboss.geek.model.bean.GeekTalentPoolChangeData
import com.twl.meeboss.geek.module.company.recommend.component.GeekCompaniesPage
import com.twl.meeboss.geek.module.company.recommend.manager.GeekCompaniesEventDispatcher
import com.twl.meeboss.geek.module.company.recommend.viewmodel.GeekCompaniesUiIntent
import com.twl.meeboss.geek.module.company.recommend.viewmodel.GeekCompaniesViewModel
import com.twl.meeboss.geek.module.job.interaction.GeekInteractionPage
import com.twl.meeboss.geek.module.job.recommend.GeekF1ViewModel
import com.twl.meeboss.geek.module.job.recommend.GeekJobPage
import com.twl.meeboss.geek.module.job.recommend.business.GeekF1EventDispatcher
import com.twl.meeboss.geek.module.main.functions.GeekMainResumeFunction
import com.twl.meeboss.geek.module.main.functions.UnReadCountFunction
import com.twl.meeboss.geek.module.me.home.GeekMePage
import com.twl.meeboss.geek.module.talentpool.manager.GeekTalentPoolManager
import com.twl.meeboss.geek.repos.GeekLinkedinRepository
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableStateFlow

@AndroidEntryPoint
class GeekMainFragment : BaseMviFragment<GeekMainViewModel>() {

    private val functionsManager by lazy {
        FunctionsManager(this)
    }
    companion object {

        private const val BUNDLE_TAB_INDEX = "tabIndex"

        fun newInstance(tabIndex: Int) = GeekMainFragment().apply {
            arguments = Bundle().apply {
                putInt(BUNDLE_TAB_INDEX, tabIndex)
            }
        }
    }
    //GeekF1相关
    private val geekF1ViewModel: GeekF1ViewModel by viewModels()
    private  val geekF1EventDispatcher:GeekF1EventDispatcher by lazy {
        GeekF1EventDispatcher(owner = this, vm = geekF1ViewModel)
    }
    private val linkedinEvent: MutableStateFlow<Int> = MutableStateFlow(0)
    private val completeEvent: MutableStateFlow<Boolean> = MutableStateFlow(false)

    //region v1.05.6 C端公司列表
    private val geekCompaniesViewModel: GeekCompaniesViewModel by viewModels()
    private  val geekCompaniesEventDispatcher: GeekCompaniesEventDispatcher by lazy {
        GeekCompaniesEventDispatcher(owner = this, vm = geekCompaniesViewModel)
    }
    private val geekTalentPoolManager:GeekTalentPoolManager by lazy {
        GeekTalentPoolManager(activity as FragmentActivity)
    }
    //endregion

    override val viewModel: GeekMainViewModel by viewModels()

    override fun preInit() {
        viewModel.currentTabIndex.value = arguments?.getInt(BUNDLE_TAB_INDEX, 0) ?: 0
        functionsManager.addFunction(GeekMainResumeFunction())
        functionsManager.addFunction(UnReadCountFunction())
    }

    override fun initData() {
        liveEventBusObserve(EventBusKey.CHANGE_MAIN_TAB) { index: Int ->
            viewModel.currentTabIndex.value = index
            changeStatusBarColor(index )
        }
        liveEventBusObserve<GeekTalentPoolChangeData>(EventBusKey.GEEK_TALENT_POOL_STATUS_CHANGE) { data->
            geekCompaniesViewModel.sendUiIntent(GeekCompaniesUiIntent.GeekTalentPoolChange(data))
        }
        liveEventBusObserve(EventBusKey.GEEK_LINKEDIN_ACTIVATION) { _: String ->
            linkedinEvent.value++
        }
        geekTalentPoolManager.joinTalentPoolNotComplete.observe(this) { notComplete->
            if (notComplete) {
                activity?.let {
                    GeekPageRouter.jumpToGeekChatGuidanceActivity(
                        context = it,
                        guidanceType = GuidanceType.BeginnerGuidance,
                        beginnerGuidanceType = BeginnerGuidanceType.TALENT_POOL,
                    )
                }
            }
        }
        AccountManager.getGeekUserInfo()?.observe(this) {
            completeEvent.value = it?.completeStatus == 1
        }
    }

    @Composable
    override fun ComposeContent() {
        val userInfo = GeekUserInfoService.geekUserInfo.observeAsState()
        val owner = LocalLifecycleOwner.current
        viewModel.observeLifecycleEvents(owner.lifecycle)
        LaunchedEffect(owner) {
            geekF1EventDispatcher.init()
        }
        val currentIndex by viewModel.currentTabIndex.collectAsStateWithLifecycle()
        val unreadCount by ChatServiceRouter.getUnreadCount().collectAsStateWithLifecycle(0)
        val uiState by geekF1ViewModel.uiStateFlow.collectAsStateWithLifecycle()
        val tabBeanList = listOf(
            MainTabBean(
                nameRes = R.string.common_jobs,
                icon = R.drawable.base_icon_home_normal,
                selectedIcon = R.drawable.base_icon_home_selected
            ),
            MainTabBean(
                nameRes = R.string.jobseeker_companies_tab,
                icon = R.drawable.base_icon_company_normal,
                selectedIcon = R.drawable.base_icon_company_selected,
            ),
            MainTabBean(
                nameRes = R.string.common_messages,
                unreadCount = unreadCount,
                icon = R.drawable.base_icon_chat_normal,
                selectedIcon = R.drawable.base_icon_chat_selected
            ),
            MainTabBean(
                nameRes = R.string.common_me,
                icon = R.drawable.base_icon_me_normal,
                selectedIcon = R.drawable.base_icon_me_selected,
                showRedDot = userInfo.value?.displayRedDot == 1
            )
        )
        viewModel.totalTabCount = tabBeanList.size
        GeekMainPage(currentIndex,
            tabs = tabBeanList,
            candidatesContent = {
                GeekJobPage(uiState = uiState, callback = geekF1EventDispatcher)

                val link by linkedinEvent.collectAsState()
                val complete by completeEvent.collectAsState()
                LaunchedEffect(link, complete) {
                    if (complete) {
                        val securityId = GeekLinkedinRepository.getActivation()
                        if (!securityId.isNullOrEmpty()) {
                            GeekJobMessageDialog.create(securityId).showSafely(requireActivity())
                            GeekLinkedinRepository.clearActivation()
                        }
                    }
                }
            },
            companiesContent = {
                GeekCompaniesPage(
                    uiState = geekCompaniesViewModel.uiStateFlow.collectAsStateWithLifecycle().value,
                    geekTalentPoolManager = geekTalentPoolManager,
                    callback = geekCompaniesEventDispatcher
                )
            },
            chatContent = {
                ChatServiceRouter.MessagesScreen(
                    tabs = listOf(R.string.common_messages, R.string.geek_interactions_head_profile_views),
                    pageContent = { index ->
                        when (index) {
                            0 -> { ChatServiceRouter.GeekConversationPage(userInfo.value?.completeStatus ==1) }
                            1 -> { GeekInteractionPage() }
                        }
                    }
                )
            }, meContent = {
                GeekMePage()
            }
        ) {
            viewModel.currentTabIndex.value = it
            changeStatusBarColor(it)
            GeekPointReporter.geekMainTabClick(viewModel.currentTabIndex.value)
        }
    }

    private fun changeStatusBarColor(index: Int){
        if (index == viewModel.totalTabCount - 1) {
            activity?.statusBarColor(COLOR_354848.toArgb())
        } else {
            activity?.statusBarColor(android.graphics.Color.WHITE)
        }
    }
}