package com.twl.meeboss.geek.model.bean

import androidx.annotation.Keep
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.geek.model.constants.GeekF1CardType
import com.twl.meeboss.base.model.job.JobItemResult

data class GeekF1PageList(
    val content: List<JobItemResult> = emptyList(),
    val tabs: List<GeekListExtendBean> = emptyList(),
    val hasMore: Boolean = false,
    val page: Int = 0,
    val pageSize: Int = 0,
    val banners: List<Banner> = emptyList()
) : BaseEntity {

    /**
     * 获取插入后的列表,将banners和tabs插入到content的列表中
     * banners一定展示在列表头部，tabs不一定，要先处理tabs再处理banners
     */
    fun getInsertedList(): List<GeekF1ListBean> {
        val targetList = content.map { GeekF1ListBean(cardType = GeekF1CardType.JOB, jobs = it) }

        // 无tabs和banners
        if (tabs.isEmpty() && banners.isEmpty()) {
            return targetList
        }

        val targetMutableList = targetList.toMutableList()

        // tabs
        if (tabs.isNotEmpty()) {
            var offset = 0
            tabs.forEach {
                val position = it.index + offset
                if (position >= 0 && position <= targetMutableList.size) {
                    it.toGeekF1ListBean()?.let { geekF1ListBean ->
                        targetMutableList.add(position, geekF1ListBean)
                        offset++
                    }
                }
            }
        }

        // banners
        if (banners.isNotEmpty()) {
            // 后续这里可能会做成轮播的形式，因此这里将整个banner数据列表塞到一个GeekF1ListBean中，方便解析
            targetMutableList.add(0, GeekF1ListBean(cardType = GeekF1CardType.BANNERS, banners = banners))
        }

        return targetMutableList
    }
}

@Keep
data class Banner(
    val imageUrl: String,
    val jumpUrl: String
) : BaseEntity

private fun GeekListExtendBean?.toGeekF1ListBean(): GeekF1ListBean? {
    if (this == null) {
        return null
    }

    return when (this.type) {
        GeekListExtendType.BEGINNER_GUIDE_FLOW,
        GeekListExtendType.BEGINNER_GUIDE_ADD_WORK_EXP,
        GeekListExtendType.BEGINNER_GUIDE_ADD_EDU_EXP,
        GeekListExtendType.BEGINNER_GUIDE_ADD_NAME,
        GeekListExtendType.VERIFY_USER_EMAIL,
        GeekListExtendType.JS_GUIDE_WORK_EXP,
        GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP,
        GeekListExtendType.JS_GUIDE_SKILL,
        GeekListExtendType.JS_GUIDE_IMPROVEMENT, -> {
            GeekF1ListBean(cardType = GeekF1CardType.BEGINNER_GUIDE_CARD, extend = this)
        }
        GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS -> {
            GeekF1ListBean(cardType = GeekF1CardType.TWO_BUTTON_CARD, extend = this)
        }

        else -> {
            null
        }
    }
}