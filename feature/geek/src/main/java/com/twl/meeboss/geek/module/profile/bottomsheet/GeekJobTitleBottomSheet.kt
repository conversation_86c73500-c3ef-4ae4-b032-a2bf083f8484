package com.twl.meeboss.geek.module.profile.bottomsheet

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.os.bundleOf
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.components.SuggestListItem
import com.twl.meeboss.base.foundation.viewmodel.BaseViewModel
import com.twl.meeboss.base.ktx.getHighlightString
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.addSearchKeyToLast
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.XTextField
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.api.GeekApi

class GeekJobTitleBottomSheet : CommonBottomDialogFragment() {
    private var onSelectCallback:((HighlightBean) -> Unit) ?= null

    private val defaultValue: HighlightBean? by lazy {
        arguments?.getSerializable("defaultValue") as? HighlightBean
    }

    companion object {
        fun newInstance(defaultValue: HighlightBean,onSelectCallback: (HighlightBean) -> Unit) = GeekJobTitleBottomSheet().apply {
            this.onSelectCallback = onSelectCallback
            arguments = bundleOf(
                "defaultValue" to defaultValue
            )
        }
    }

    @Composable
    override fun DialogContent() {
        val defaultValue = defaultValue ?: return dismissSafely()
        SuggestTitleContent(defaultValue = defaultValue, onClickClose = {
            dismissSafely()
        }, callback = {
            onSelectCallback?.let { it1 -> it1(it) }
            dismissSafely()
        })
    }

}

@Composable
internal fun SuggestTitleContent(viewModel: GeekEditJobTitleViewModel = viewModel(), defaultValue: HighlightBean = HighlightBean(), onClickClose:()->Unit = {}, callback: (item: HighlightBean) -> Unit = {}) {
    LaunchedEffect(key1 = viewModel) {
        viewModel.etInput.value = TextFieldValue(
            text = defaultValue.name,
            selection = TextRange(defaultValue.name.length)
        )
        viewModel.getSuggestJobTitle()
    }
    XTheme {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White, shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))) {
            Image(painter = painterResource(id = R.drawable.ui_dailog_close), contentDescription = "Close dialog",
                modifier = Modifier
                    .padding(16.dp, 20.dp, 0.dp, 0.dp)
                    .clickable {
                        onClickClose()
                    }
                    .size(24.dp))
            Text(
                modifier = Modifier.padding(16.dp, 18.dp),
                text = stringResource(id = R.string.common_job_title),
                style = TextStyle(
                    color = Black222222,
                    fontSize = 28.sp,
                    fontWeight = FontWeight.SemiBold
                ))
            Spacer(modifier = Modifier.height(2.dp))
            val input by viewModel.etInput.observeAsState(TextFieldValue(defaultValue.name, selection = TextRange(defaultValue.name.length)))
            XTextField(modifier = Modifier.padding(16.dp, 0.dp),
                value = input,
                innerTitle = R.string.common_enter_job_title,
                placeHolder = R.string.common_enter_job_title,
                textStyle = TextStyle(fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Black222222
                ),
                showKeyboard = true,
                onValueChange = {v->
                    v.subString(viewModel.maxLength).let {
                        viewModel.etInput.value = it
                        viewModel.getSuggestJobTitle()
                    }
                })
            val list by viewModel.list.observeAsState(listOf())
            val count = list?.size ?: 0
            LazyColumn {
                itemsIndexed(list!!) { index, item ->
                    SuggestListItem(item.name.getHighlightString(item.highlights), index != count - 1, isAdd = item.isLocalAdd) {
                        viewModel.getSuggestJobTitle()
                        callback(item)
                    }
                }
            }

        }
    }
}

@Preview
@Composable
internal fun SuggestTitlePagePreview() {
    val mViewModel: GeekEditJobTitleViewModel = viewModel<GeekEditJobTitleViewModel>().also {
        it.list.value = listOf(HighlightBean(1, "Product manager"),
            HighlightBean(2, "QA"),
            HighlightBean(2, "UX designer", isLocalAdd = true))
    }
    SuggestTitleContent(mViewModel) {

    }
}

internal class GeekEditJobTitleViewModel : BaseViewModel() {
    
    val maxLength = 100

    val etInput: MutableLiveData<TextFieldValue> = MutableLiveData()

    var list: MutableLiveData<List<HighlightBean>> = MutableLiveData()

    fun getSuggestJobTitle() {
        async {
            val input = etInput.value?.text ?: ""
            val api = getService(GeekApi::class.java)
            val result = api.jobSeekerSuggestJobTitle(etInput.value?.text ?: "")
            if (result.isSuccess) {
                list.postValue(result.getOrNull()?.list ?.addSearchKeyToLast(input) ?: listOf())
            } else {
                list.postValue(listOf())
            }
        }
    }

}
