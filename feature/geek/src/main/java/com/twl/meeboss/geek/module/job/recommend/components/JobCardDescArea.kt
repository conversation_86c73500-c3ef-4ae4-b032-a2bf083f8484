package com.twl.meeboss.geek.module.job.recommend.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.core.ui.component.layout.FlowLayout
import com.twl.meeboss.core.ui.component.text.XLabelText
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.COLOR_858585
import com.twl.meeboss.core.ui.theme.COLOR_AAAAAA
import com.twl.meeboss.base.model.job.JobItemResult
import com.twl.meeboss.core.ui.theme.COLOR_028847
import com.twl.meeboss.core.ui.theme.COLOR_E0F8EB
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.foundation.moduleservice.GeekUserInfoService
import com.twl.meeboss.geek.module.job.recommend.preview.JobPreviewData

/**
 * @author: 冯智健
 * @date: 2024年07月17日 16:33
 * @description:
 */
@Composable
fun JobCardDescArea(item: JobItemResult, isDisable: Boolean = false) {
    val labels = item.labels?.mapNotNull { it }?.filter { it.isNotBlank() } ?: emptyList()
    val showVisaSponsorship = item.visaSponsored == 1
    if (labels.isNotEmpty() || showVisaSponsorship){
        FlowLayout(
            modifier = Modifier.padding(top = 4.dp, bottom = 12.dp),
            maxLine = 1,
            horizontalSpacing = 8.dp
        ) {
            if (item.visaSponsored == 1) {
                val geekUserInfo by GeekUserInfoService.geekUserInfo.observeAsState(null)
                val needVisaSponsorship = geekUserInfo?.expectationVO?.visaSponsorship == 1
                XLabelText(
                    text = stringResource(R.string.job_seeker_job_detail_visa_sponsored),
                    fontWeight = FontWeight.Medium,
                    color = if (needVisaSponsorship) COLOR_028847 else Black484848,
                    backgroundColor = if (needVisaSponsorship) COLOR_E0F8EB else COLOR_F5F5F5
                )
            }
            labels.map { label ->
                XLabelText(
                    text = label,
                    fontWeight = FontWeight.Medium,
                    color = if (isDisable) COLOR_AAAAAA else Black484848
                )
            }
        }
    }
    item.jobDesc?.takeIf { it.isNotBlank() }?.let {
        Text(
            modifier = Modifier.padding(bottom = 12.dp),
            text = it,
            color = if (isDisable) COLOR_AAAAAA else COLOR_858585,
            fontSize = 13.sp,
            lineHeight = 18.sp,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
    }
    item.recommendReason?.apply {
        text?.takeIf { it.isNotBlank() }?.let {
            JobCardHighLightArea(Modifier.padding(bottom = 16.dp), highlight, it)
        }
    }
}

@Preview
@Composable
private fun PreviewJobCardDescArea() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(vertical = 20.dp, horizontal = 16.dp)
        ) {
            JobCardDescArea(JobPreviewData.jobItemResult)
        }
    }
}