package com.twl.meeboss.geek.module.register.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withLink
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintLayoutBaseScope.VerticalAnchor
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import androidx.core.view.WindowCompat
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.BUNDLE_LONG
import com.twl.meeboss.base.constants.BUNDLE_OBJECT
import com.twl.meeboss.base.constants.BUNDLE_SALARY_UNIT
import com.twl.meeboss.base.constants.BUNDLE_SALARY_UNIT_DESC
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.from.GeekFromData
import com.twl.meeboss.base.main.router.BaseServiceRouter
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.SalaryBean
import com.twl.meeboss.base.model.job.JobItemResult
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.Black888888
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.COLOR_BD222B
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.EditPageScene
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.export.GeekRouterPath
import com.twl.meeboss.geek.export.GuidanceType
import com.twl.meeboss.geek.module.guidance.activity.GeekBeginnerAddNameActivity
import com.twl.meeboss.geek.module.guidance.components.GeekMandatoryInfo
import com.twl.meeboss.geek.module.guidance.components.GeekMandatoryInfoForm
import com.twl.meeboss.geek.module.preference.activity.GeekEditJobLocationActivity
import com.twl.meeboss.geek.module.preference.activity.GeekEditJobTitleActivity
import com.twl.meeboss.geek.module.preference.activity.GeekEditSalaryActivity
import com.twl.meeboss.geek.module.register.components.GeekLinkedinRegisterTips
import com.twl.meeboss.geek.module.register.components.GeekRecommendJobCard
import com.twl.meeboss.geek.module.register.viewmodel.GeekRegisterLinkedinUiIntent
import com.twl.meeboss.geek.module.register.viewmodel.GeekRegisterLinkedinUiState
import com.twl.meeboss.geek.module.register.viewmodel.GeekRegisterLinkedinViewModel
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.AndroidEntryPoint

private const val TAG = "GeekRegisterLinkedinActivity"

private const val REQ_CODE_JOB_TITLE = 1001
private const val REQ_CODE_JOB_LOCATION = 1002
private const val REQ_CODE_JOB_SALARY = 1003
private const val REQ_CODE_PROFILE_NAME = 1004

// 常量定义
private object LinkedinPageConstants {
    val CARD_BORDER_SHAPE = RoundedCornerShape(size = 20.dp)
    val INPUT_BORDER_SHAPE = RoundedCornerShape(16.dp)
    val HORIZONTAL_PADDING = 16.dp
    val VERTICAL_PADDING = 20.dp
    val BORDER_WIDTH = 1.dp
}

/**
 * LinkedIn注册激活Activity
 *
 * 功能说明：
 * 1. 展示LinkedIn激活和信息确认的两个阶段页面
 * 2. 处理LinkedIn URL输入和验证
 * 3. 展示LinkedIn解析后的信息供用户确认和编辑
 * 4. 管理与各个编辑页面的跳转和数据回调
 *
 * 页面流程：
 * LinkedIn URL输入页面 -> LinkedIn信息解析 -> 信息确认编辑页面 -> 完成注册
 *
 * 支持的编辑功能：
 * - 职位偏好编辑
 * - 工作地点编辑
 * - 薪资期望编辑
 * - 个人姓名编辑
 * - 工作经验编辑
 * - 教育经历编辑
 */
@RouterPage(path = [GeekRouterPath.GEEK_LINKEDIN_ACTIVATE_PAGE])
@AndroidEntryPoint
class GeekRegisterLinkedinActivity : BaseMviActivity<GeekRegisterLinkedinViewModel>() {

    override val viewModel: GeekRegisterLinkedinViewModel by viewModels()

    override fun preInit(intent: Intent) {
        XLog.info(TAG, "preInit - starting LinkedIn activation activity")
        WindowCompat.setDecorFitsSystemWindows(window, false)

        val securityId = intent.getStringExtra(GeekPageRouter.BUNDLE_SECURITY_ID)
        XLog.info(TAG, "received securityId from intent: $securityId")
        if (!securityId.isNullOrEmpty()) {
            viewModel.update(securityId)
        }
        
        viewModel.postResult.observe(this) {
            XLog.info(TAG, "LinkedIn activation completed, navigating to main page")
            BaseServiceRouter.afterLogin(this)
            finish()
        }
    }

    override fun initData() {
        liveEventBusObserve(EventBusKey.GEEK_LINKEDIN_ACTIVATION) { id: String ->
            XLog.info(TAG, "received LinkedIn activation event: $id")
            viewModel.sendUiIntent(GeekRegisterLinkedinUiIntent.NewLinkedinId(id))
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        XLog.info(TAG, "onActivityResult - requestCode: $requestCode, resultCode: $resultCode")

        if (resultCode == RESULT_OK && data != null) {
            when (requestCode) {
                REQ_CODE_JOB_TITLE -> {
                    val jobTitles = data.getSerializableExtra(BUNDLE_OBJECT) as ArrayList<HighlightBean>
                    XLog.info(TAG, "job title edit completed - count: ${jobTitles.size}")
                    viewModel.sendUiIntent(
                        GeekRegisterLinkedinUiIntent.OnJobTitleChange(jobTitles)
                    )
                }

                REQ_CODE_JOB_LOCATION -> {
                    val cities = data.getSerializableExtra(BUNDLE_OBJECT) as ArrayList<CityBean>
                    XLog.info(TAG, "job location edit completed - count: ${cities.size}")
                    viewModel.sendUiIntent(
                        GeekRegisterLinkedinUiIntent.OnCityChange(cities)
                    )
                }

                REQ_CODE_JOB_SALARY -> {
                    val minSalary = data.getLongExtra(BUNDLE_LONG, 0L)
                    val salaryOption = data.getSerializableExtra(BUNDLE_OBJECT) as OptionBean
                    XLog.info(TAG, "salary edit completed - minSalary: $minSalary, type: ${salaryOption.name}")
                    viewModel.sendUiIntent(
                        GeekRegisterLinkedinUiIntent.OnSalaryChange(
                            minSalary,
                            salaryOption,
                            data.getLongExtra(BUNDLE_SALARY_UNIT, 0L),
                            data.getStringExtra(BUNDLE_SALARY_UNIT_DESC)
                        )
                    )
                }

                REQ_CODE_PROFILE_NAME -> {
                    val (firstName, lastName) = GeekBeginnerAddNameActivity.getResult(data)
                    XLog.info(TAG, "name edit completed - firstName: '$firstName', lastName: '$lastName'")
                    viewModel.sendUiIntent(
                        GeekRegisterLinkedinUiIntent.OnNameChange(
                            firstName = firstName ?: "",
                            lastName = lastName ?: ""
                        )
                    )
                }

                else -> {
                    XLog.error(TAG, "unknown request code: $requestCode")
                }
            }
        } else {
            XLog.info(TAG, "activity result cancelled or no data")
        }
    }

    /**
     * 处理返回按键事件
     * 当前Activity包含两个阶段，需拦截回退事件，在信息确认阶段时返回到LinkedIn输入阶段，否则执行默认返回逻辑
     */
    override fun onBackPressed() {
        val currentPhase = viewModel.phase
        XLog.info(TAG, "onBackPressed - current phase: $currentPhase")
        if (currentPhase > 0) {
            XLog.info(TAG, "returning to previous phase")
            viewModel.sendUiIntent(GeekRegisterLinkedinUiIntent.Back)
        } else {
            XLog.info(TAG, "exiting LinkedIn activation")
            super.onBackPressed()
        }
    }

    /**
     * 主要的Compose内容组件
     * 根据当前阶段展示不同的页面：LinkedIn激活页面或信息确认页面
     */
    @Composable
    override fun ComposeContent() {
        // 使用 systemUiController 控制系统 UI 样式
        val systemUiController = rememberSystemUiController()

        // 设置状态栏颜色透明
        SideEffect {
            systemUiController.setSystemBarsColor(
                color = Color.Transparent,
                darkIcons = true
            )
        }

        val loadState by viewModel.loadUiStateFlow.collectAsState()
        val uiState by viewModel.uiStateFlow.collectAsState()

        DisposableEffect(loadState.loadState) {
            when (loadState.loadState) {
                LoadState.Loading -> {
                    showLoadingDialog()
                }

                else -> {
                    dismissLoadingDialog()
                }
            }

            onDispose {
                dismissLoadingDialog()
            }
        }

        Box(modifier = Modifier.background(Color.White)) {
            Image(
                painter = painterResource(R.drawable.geek_bg_register_linkedin),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.fillMaxWidth()
            )

            AnimatedContent(
                targetState = uiState.phase,
                transitionSpec = {
                    if (targetState > initialState) {
                        slideInHorizontally { fullWidth -> fullWidth } togetherWith
                                slideOutHorizontally { fullWidth -> -fullWidth } + fadeOut()
                    } else if (targetState < initialState) {
                        slideInHorizontally { fullWidth -> -fullWidth } togetherWith
                                slideOutHorizontally { fullWidth -> fullWidth } + fadeOut()
                    } else {
                        EnterTransition.None togetherWith ExitTransition.None
                    }
                },
                modifier = Modifier
                    .fillMaxSize()
                    .windowInsetsPadding(WindowInsets.systemBars)
            ) {
                if (it == 0) {
                    LinkedinActivatePage(
                        loadState = loadState.loadState,
                        job = uiState.jobCard,
                        link = uiState.linkedin,
                        onSkipClicked = ::skipLinkedinActivate,
                        onShowTips = ::showTipsBottomSheet,
                        onClickActivate = ::startActivate,
                        onClickRetry = ::refresh
                    )
                } else {
                    LinkedinParsedPage(
                        uiState = uiState,
                        onBackPressed = ::onBackPressed,
                        onNextClick = ::submitData
                    )
                }
            }
        }
    }

    private fun startActivate(linkedin: String) {
        XLog.info(TAG, "startActivate - LinkedIn URL: $linkedin")
        viewModel.sendUiIntent(GeekRegisterLinkedinUiIntent.Activate(linkedin))
    }

    private fun skipLinkedinActivate() {
        XLog.info(TAG, "skipLinkedinActivate - user chose to skip")
        viewModel.sendUiIntent(GeekRegisterLinkedinUiIntent.Skip)
    }

    private fun showTipsBottomSheet() {
        XLog.info(TAG, "showTipsBottomSheet - showing LinkedIn tips")
        GeekLinkedinRegisterTips().showSafely(this)
    }

    private fun refresh() {
        XLog.info(TAG, "refresh - user requested to refresh job card")
        viewModel.sendUiIntent(GeekRegisterLinkedinUiIntent.Refresh)
    }

    private fun submitData() {
        XLog.info(TAG, "submitData - user submitted LinkedIn profile data")
        viewModel.sendUiIntent(GeekRegisterLinkedinUiIntent.Submit)
        GeekPointReporter.guidanceAllCompleted(4)
    }
}

/**
 * LinkedIn激活页面组件
 * 展示推荐职位和LinkedIn URL输入界面
 *
 * @param loadState 页面加载状态
 * @param job 推荐的职位信息
 * @param link 当前的LinkedIn URL
 * @param onSkipClicked 跳过按钮点击回调
 * @param onShowTips 显示提示信息回调
 * @param onClickActivate LinkedIn激活按钮点击回调
 * @param onClickRetry 重试按钮点击回调（当加载失败时）
 */
@Composable
private fun LinkedinActivatePage(
    loadState: LoadState,
    job: JobItemResult,
    link: String,
    onSkipClicked: () -> Unit,
    onShowTips: () -> Unit,
    onClickActivate: (String) -> Unit,
    onClickRetry: () -> Unit,
) {
    var linkedin by remember(link) { mutableStateOf(link) }

    Column {
        // 顶部跳过按钮
        LinkedinActivateTopBar(
            onSkipClicked = {
                onSkipClicked()
                GeekPointReporter.linkedinActivate(2, linkedin, job.jobId)
            }
        )

        // 主要内容
        LinkedinActivateContent(
            loadState = loadState,
            job = job,
            linkedin = linkedin,
            onLinkedinChange = { linkedin = it },
            onShowTips = onShowTips,
            onClickActivate = {
                onClickActivate(linkedin)
                GeekPointReporter.linkedinActivate(1, linkedin, job.jobId)
            },
        )
    }
}

@Composable
private fun LinkedinActivateTopBar(
    onSkipClicked: () -> Unit
) {
    Row(
        modifier = Modifier.padding(
            horizontal = LinkedinPageConstants.HORIZONTAL_PADDING,
            vertical = 10.dp
        ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.weight(1f))
        Text(
            text = stringResource(R.string.geek_register_skip),
            modifier = Modifier.noRippleClickable { onSkipClicked() }
        )
    }
}

@Composable
private fun LinkedinActivateContent(
    loadState: LoadState,
    job: JobItemResult,
    linkedin: String,
    onLinkedinChange: (String) -> Unit,
    onShowTips: () -> Unit,
    onClickActivate: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.verticalScroll(rememberScrollState())) {
        if (job.jobTitle.isNullOrEmpty()) {
            AnimatedVisibility(visible = loadState !is LoadState.Fail) {
                Box(modifier = Modifier
                    .fillMaxWidth()
                    .height(240.dp)
                    .padding(horizontal = 32.dp)
                    .padding(top = 32.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(12.dp)
                    )
                )
            }
        } else {
            // 推荐职位卡片
            GeekRecommendJobCard(job)
        }

        // 标题和提示
        LinkedinActivateTitle(onShowTips = onShowTips)

        // LinkedIn URL输入框
        LinkedinUrlInput(
            value = linkedin,
            onValueChange = onLinkedinChange,
        )

        // 激活按钮
        XCommonButton(
            text = stringResource(R.string.job_seeker_linkedin_activate_button),
            modifier = Modifier.padding(LinkedinPageConstants.HORIZONTAL_PADDING),
            enabled = linkedin.isNotBlank(),
            onClick = onClickActivate
        )
    }
}

@Composable
private fun LinkedinActivateTitle(
    onShowTips: () -> Unit
) {
    val title = stringResource(R.string.job_seeker_linkedin_activate_title)
    val titleText = remember {
        buildAnnotatedString {
            append(title)
            withLink(
                link = LinkAnnotation.Clickable(
                    tag = "?",
                    linkInteractionListener = { onShowTips() }
                )
            ) {
                appendInlineContent("?")
            }
        }
    }

    val inlineContent = remember {
        mapOf(
            "?" to InlineTextContent(
                placeholder = Placeholder(
                    width = 18.sp,
                    height = 18.sp,
                    placeholderVerticalAlign = PlaceholderVerticalAlign.TextCenter
                ),
                children = {
                    Image(
                        painter = painterResource(R.drawable.geek_icon_register_linkedin_tip),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .offset(x = 8.dp, y = 2.dp)
                    )
                }
            )
        )
    }

    Text(
        text = titleText,
        inlineContent = inlineContent,
        textAlign = TextAlign.Center,
        fontSize = 28.sp,
        fontWeight = FontWeight.SemiBold,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 48.dp)
            .padding(horizontal = 34.dp)
    )
}

@Composable
private fun LinkedinUrlInput(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        textStyle = TextStyle(fontSize = 16.sp, color = COLOR_222222),
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Uri,
            imeAction = ImeAction.Done
        ),
        modifier = modifier
            .padding(24.dp)
            .fillMaxWidth()
            .background(
                color = Color.White,
                shape = LinkedinPageConstants.INPUT_BORDER_SHAPE
            )
            .border(
                width = 2.dp,
                color = COLOR_DDDDDD,
                shape = LinkedinPageConstants.INPUT_BORDER_SHAPE
            )
    ) { innerTextField ->
        Box(modifier = Modifier.padding(LinkedinPageConstants.HORIZONTAL_PADDING)) {
            if (value.isEmpty()) {
                Text(
                    text = stringResource(R.string.job_seeker_linkedin_activate_placeholder),
                    fontSize = 16.sp,
                    color = Black888888
                )
            }
            innerTextField()
        }
    }
}

/**
 * LinkedIn信息确认页面组件
 * 展示解析后的LinkedIn信息供用户确认和编辑
 *
 * @param uiState 当前的UI状态，包含所有用户信息
 * @param onBackPressed 返回按钮点击回调
 * @param onNextClick 下一步按钮点击回调
 */
@Composable
private fun LinkedinParsedPage(
    uiState: GeekRegisterLinkedinUiState,
    onBackPressed: () -> Unit,
    onNextClick: () -> Unit,
) {
    val rowItems = rememberJobPreferenceItems(uiState)

    Column {
        // 返回按钮
        LinkedinParsedTopBar(onBackPressed = onBackPressed)

        // 可滚动内容
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
        ) {
            GuidanceHeader(uiState.allSet)
            JobPreferenceSection(rowItems)
            ProfileSection(uiState)
        }

        // 提交按钮
        XCommonButton(
            text = stringResource(R.string.job_seeker_linkedin_activate_result_button),
            enabled = uiState.allSet,
            onClick = onNextClick,
            modifier = Modifier.padding(LinkedinPageConstants.HORIZONTAL_PADDING)
        )
    }
}

@Composable
private fun LinkedinParsedTopBar(
    onBackPressed: () -> Unit
) {
    Icon(
        painter = painterResource(R.drawable.ui_back_arrow),
        contentDescription = null,
        modifier = Modifier
            .padding(
                horizontal = LinkedinPageConstants.HORIZONTAL_PADDING,
                vertical = 10.dp
            )
            .noRippleClickable { onBackPressed() }
    )
}

/**
 * 记忆化创建职位偏好编辑项配置
 */
@Composable
private fun rememberJobPreferenceItems(uiState: GeekRegisterLinkedinUiState): List<RowItem> {
    val context = LocalContext.current

    return remember(uiState.jobTitles, uiState.cities, uiState.salary) {
        createJobPreferenceItems(
            context = context,
            jobTitles = uiState.jobTitles,
            cities = uiState.cities,
            salary = uiState.salary,
        )
    }
}

/**
 * 创建职位偏好编辑项配置
 */
private fun createJobPreferenceItems(
    context: Context,
    jobTitles: List<HighlightBean>,
    cities: List<CityBean>,
    salary: SalaryBean,
): List<RowItem> {
    return listOf(
        // 职位偏好
        RowItem(
            label = context.getString(R.string.job_seeker_linkedin_activate_resule_jp_role),
            value = jobTitles.joinToString(separator = "; ") { it.name },
            isEmpty = jobTitles.isEmpty(),
            onEditClick = { isEmpty ->
                GeekEditJobTitleActivity.intentForResult(
                    context as Activity,
                    REQ_CODE_JOB_TITLE,
                    jobTitles,
                    from = GeekFromData.GEEK_LINKEDIN
                )
                GeekPointReporter.jobPreferenceClick(1, if (isEmpty) 1 else 2)
            }
        ),
        // 地点偏好
        RowItem(
            label = context.getString(R.string.job_seeker_linkedin_activate_resule_jp_location),
            value = cities.joinToString(separator = "; ") { it.name },
            isEmpty = cities.isEmpty(),
            onEditClick = { isEmpty ->
                GeekEditJobLocationActivity.intentForResult(
                    context as Activity,
                    REQ_CODE_JOB_LOCATION,
                    cities,
                    from = GeekFromData.GEEK_LINKEDIN
                )
                GeekPointReporter.jobPreferenceClick(2, if (isEmpty) 1 else 2)
            }
        ),
        // 薪资偏好
        RowItem(
            label = context.getString(R.string.job_seeker_linkedin_activate_resule_jp_salary),
            value = "${salary.salaryUnitDesc} ${salary.minSalary} ${salary.salaryTypeDesc}",
            isEmpty = salary.minSalary <= 0,
            onEditClick = { isEmpty ->
                GeekEditSalaryActivity.intentForResult(
                    context as Activity,
                    REQ_CODE_JOB_SALARY,
                    salary.minSalary,
                    salary.salaryType,
                    salary.salaryUnit,
                    salary.salaryUnitDesc,
                    from = GeekFromData.GEEK_LINKEDIN
                )
                GeekPointReporter.jobPreferenceClick(3, if (isEmpty) 1 else 2)
            }
        )
    )
}

/**
 * 页面头部组件
 * 根据信息完成状态显示不同的图标、标题和提示文本
 *
 * @param allSet 是否所有必填信息都已完成
 */
@Composable
private fun GuidanceHeader(allSet: Boolean) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
            .padding(top = 8.dp, bottom = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val (icon, title, hint) = if (allSet) {
            Triple(
                R.drawable.geek_icon_register_linkedin_allset,
                R.string.job_seeker_linkedin_activate_result_title1,
                R.string.job_seeker_linkedin_activate_result_hint2
            )
        } else {
            Triple(
                R.drawable.geek_icon_register_linkedin_incomplete,
                R.string.job_seeker_linkedin_activate_result_title2,
                R.string.job_seeker_linkedin_activate_result_hint1
            )
        }
        Image(
            painter = painterResource(icon),
            contentDescription = null
        )

        Text(
            text = stringResource(title),
            fontSize = 28.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(vertical = 8.dp)
        )

        Text(
            text = stringResource(hint),
            fontSize = 13.sp,
            modifier = Modifier.padding(bottom = 8.dp)
        )
    }
}

/**
 * 个人信息编辑区域组件
 * 展示个人基本信息、工作经验、教育经历的编辑表单
 *
 * @param uiState 包含个人信息的UI状态
 */
@Composable
private fun ProfileSection(uiState: GeekMandatoryInfo) {
    Column {
        // 标题和描述
        Text(
            text = stringResource(R.string.job_seeker_linkedin_activate_result_type2),
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier
                .padding(horizontal = LinkedinPageConstants.HORIZONTAL_PADDING)
                .padding(top = 8.dp)
        )
        Text(
            text = stringResource(R.string.job_seeker_linkedin_activate_result_jp_hint2),
            fontSize = 13.sp,
            modifier = Modifier
                .padding(horizontal = LinkedinPageConstants.HORIZONTAL_PADDING)
                .padding(top = 4.dp)
        )

        // 表单内容
        ProfileForm(uiState = uiState)
    }
}

@Composable
private fun ProfileForm(uiState: GeekMandatoryInfo) {
    val context = LocalContext.current

    GeekMandatoryInfoForm(
        uiState = uiState,
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = LinkedinPageConstants.HORIZONTAL_PADDING,
                vertical = LinkedinPageConstants.VERTICAL_PADDING
            )
            .border(
                width = LinkedinPageConstants.BORDER_WIDTH,
                color = COLOR_DDDDDD,
                shape = LinkedinPageConstants.CARD_BORDER_SHAPE
            )
            .background(
                color = Color.White,
                shape = LinkedinPageConstants.CARD_BORDER_SHAPE
            ),
        itemStyle = Modifier,
        showDivider = true,
        onAddNameClick = { isEmpty ->
            handleNameEdit(context, uiState.firstName, uiState.lastName)
            GeekPointReporter.guidancePageClick(1, if (isEmpty) 1 else 2, 4)
        },
        onAddWorkExpClick = { isEmpty ->
            handleWorkExpEdit(context, isEmpty)
            GeekPointReporter.guidancePageClick(2, if (isEmpty) 1 else 2, 4)
        },
        onAddEduExpClick = { isEmpty ->
            handleEduExpEdit(context, isEmpty)
            GeekPointReporter.guidancePageClick(3, if (isEmpty) 1 else 2, 4)
        }
    )
}

private fun handleNameEdit(
    context: Context,
    firstName: String?,
    lastName: String?,
) {
    XLog.info(TAG, "handleNameEdit, current name: '$firstName $lastName'")
    GeekBeginnerAddNameActivity.intentForResult(
        context as Activity,
        noSideEffect = true,
        firstname = firstName,
        lastname = lastName,
        guidanceType = GuidanceType.ActivateLinkedin,
        requestCode = REQ_CODE_PROFILE_NAME
    )
    XLog.info(TAG, "navigated to name edit page")
}

private fun handleWorkExpEdit(context: Context, isEmpty: Boolean) {
    XLog.info(TAG, "handleWorkExpEdit - isEmpty: $isEmpty")
    if (isEmpty) {
        // 添加新的工作经验
        XLog.info(TAG, "navigating to add new work experience page")
        GeekPageRouter.jumpToGeekBeginnerAddWorkExpActivity(
            context = context,
            scene = EditPageScene.SINGLE_PAGE,
            guidanceType = GuidanceType.ActivateLinkedin,
            local = true
        )
    } else {
        // 编辑现有工作经验列表
        XLog.info(TAG, "navigating to work experience list page")
        GeekPageRouter.jumpToGeekChatGuidanceWorkExpListActivity(
            context = context,
            guidanceType = GuidanceType.ActivateLinkedin,
            local = true
        )
    }
}

private fun handleEduExpEdit(context: Context, isEmpty: Boolean) {
    XLog.info(TAG, "handleEduExpEdit - isEmpty: $isEmpty")
    if (isEmpty) {
        // 添加新的教育经历
        XLog.info(TAG, "navigating to add new education experience page")
        GeekPageRouter.jumpToGeekBeginnerAddEduExpActivity(
            context = context,
            scene = EditPageScene.SINGLE_PAGE,
            guidanceType = GuidanceType.ActivateLinkedin,
            local = true
        )
    } else {
        // 编辑现有教育经历列表
        XLog.info(TAG, "navigating to education experience list page")
        GeekPageRouter.jumpToGeekChatGuidanceEduExpListActivity(
            context = context,
            guidanceType = GuidanceType.ActivateLinkedin,
            local = true
        )
    }
}

/**
 * 职位偏好设置区域组件
 * 展示职位、地点、薪资等偏好设置的标题和表单
 *
 * @param rowItems 包含各个编辑项配置的列表
 */
@Composable
private fun JobPreferenceSection(
    rowItems: List<RowItem>,
) {
    Column {
        // 标题和描述
        Text(
            text = stringResource(R.string.job_seeker_linkedin_activate_result_type1),
            fontSize = 18.sp,
            fontWeight = FontWeight.SemiBold,
            modifier = Modifier.padding(horizontal = LinkedinPageConstants.HORIZONTAL_PADDING)
        )
        Text(
            text = stringResource(R.string.job_seeker_linkedin_activate_result_jp_hint1),
            fontSize = 13.sp,
            modifier = Modifier
                .padding(horizontal = LinkedinPageConstants.HORIZONTAL_PADDING)
                .padding(top = 4.dp)
        )

        // 输入表单
        JobPreferenceInputArea(
            rowItems = rowItems,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = LinkedinPageConstants.HORIZONTAL_PADDING,
                    vertical = LinkedinPageConstants.VERTICAL_PADDING
                )
                .border(
                    width = LinkedinPageConstants.BORDER_WIDTH,
                    color = COLOR_DDDDDD,
                    shape = LinkedinPageConstants.CARD_BORDER_SHAPE
                )
                .background(
                    color = Color.White,
                    shape = LinkedinPageConstants.CARD_BORDER_SHAPE
                )
        )
    }
}

/**
 * 职位偏好输入表单组件
 * 使用ConstraintLayout布局展示各个编辑项，实现列对齐
 * 每一行包含：标签、当前值、编辑按钮
 *
 * @param rowItems 编辑项配置列表
 */
@Composable
private fun JobPreferenceInputArea(
    rowItems: List<RowItem>,
    modifier: Modifier = Modifier,
) {
    // 样式常量
    val labelStyle = remember {
        TextStyle(
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = COLOR_222222,
        )
    }
    val editStyle = remember {
        TextStyle(
            fontSize = 14.sp,
            color = COLOR_222222,
            textDecoration = TextDecoration.Underline
        )
    }

    ConstraintLayout(modifier = modifier) {
        // 创建引用
        val size = rowItems.size
        val refs = remember(size) {
            JobPreferenceRefs(
                labelRefs = List(size) { createRef() },
                valueRefs = List(size) { createRef() },
                editRefs = List(size) { createRef() },
                dividerRefs = List(size - 1) { createRef() }
            )
        }

        // 创建barriers用于列对齐
        val labelsBarrier = createEndBarrier(*refs.labelRefs.toTypedArray())
        val editsBarrier = createStartBarrier(*refs.editRefs.toTypedArray())

        // 渲染每一行
        rowItems.forEachIndexed { index, rowItem ->
            JobPreferenceRow(
                rowItem = rowItem,
                index = index,
                isLastRow = index == size - 1,
                refs = refs,
                labelsBarrier = labelsBarrier,
                editsBarrier = editsBarrier,
                labelStyle = labelStyle,
                editStyle = editStyle
            )
        }
    }
}

/**
 * 单行偏好设置组件
 */
@Composable
private fun ConstraintLayoutScope.JobPreferenceRow(
    rowItem: RowItem,
    index: Int,
    isLastRow: Boolean,
    refs: JobPreferenceRefs,
    labelsBarrier: VerticalAnchor,
    editsBarrier: VerticalAnchor,
    labelStyle: TextStyle,
    editStyle: TextStyle
) {
    // 标签
    Text(
        text = rowItem.label,
        style = labelStyle,
        modifier = Modifier.constrainAs(refs.labelRefs[index]) {
            top.linkTo(
                anchor = if (index == 0) parent.top else refs.dividerRefs[index - 1].bottom,
                margin = LinkedinPageConstants.HORIZONTAL_PADDING
            )
            start.linkTo(
                anchor = parent.start,
                margin = LinkedinPageConstants.HORIZONTAL_PADDING
            )
            if (isLastRow) {
                bottom.linkTo(
                    anchor = parent.bottom,
                    margin = LinkedinPageConstants.HORIZONTAL_PADDING
                )
            }
        }
    )

    // 值
    Text(
        text = if (rowItem.isEmpty) stringResource(R.string.common_not_started) else rowItem.value,
        fontSize = 15.sp,
        color = if (rowItem.isEmpty) COLOR_BD222B else COLOR_888888,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        modifier = Modifier.constrainAs(refs.valueRefs[index]) {
            top.linkTo(anchor = refs.labelRefs[index].top)
            bottom.linkTo(anchor = refs.labelRefs[index].bottom)
            start.linkTo(anchor = labelsBarrier, margin = LinkedinPageConstants.HORIZONTAL_PADDING)
            end.linkTo(anchor = editsBarrier, margin = LinkedinPageConstants.HORIZONTAL_PADDING)
            width = Dimension.fillToConstraints
        }
    )

    // 编辑按钮
    Text(
        text = stringResource(id = if (rowItem.isEmpty) R.string.common_add else R.string.common_button_edit),
        style = editStyle,
        modifier = Modifier
            .constrainAs(refs.editRefs[index]) {
                top.linkTo(anchor = refs.labelRefs[index].top)
                bottom.linkTo(anchor = refs.labelRefs[index].bottom)
                end.linkTo(anchor = parent.end, margin = LinkedinPageConstants.HORIZONTAL_PADDING)
            }
            .noRippleClickable {
                rowItem.onEditClick(rowItem.isEmpty)
            }
    )

    // 分隔线(除了最后一行)
    if (!isLastRow) {
        HorizontalDivider(
            color = COLOR_DDDDDD,
            thickness = LinkedinPageConstants.BORDER_WIDTH,
            modifier = Modifier.constrainAs(refs.dividerRefs[index]) {
                top.linkTo(anchor = refs.labelRefs[index].bottom, margin = LinkedinPageConstants.HORIZONTAL_PADDING)
                start.linkTo(anchor = parent.start)
                end.linkTo(anchor = parent.end)
                width = Dimension.fillToConstraints
            }
        )
    }
}

/**
 * 引用对象管理类
 */
private data class JobPreferenceRefs(
    val labelRefs: List<ConstrainedLayoutReference>,
    val valueRefs: List<ConstrainedLayoutReference>,
    val editRefs: List<ConstrainedLayoutReference>,
    val dividerRefs: List<ConstrainedLayoutReference>
)

/**
 * 编辑行数据结构
 *
 * @property label 标签文本
 * @property value 当前值文本
 * @property isEmpty 是否为空（影响显示样式和按钮文本）
 * @property onEditClick 编辑按钮点击回调，参数表示是否为空
 */
private data class RowItem(
    val label: String,
    val value: String,
    val isEmpty: Boolean,
    val onEditClick: (Boolean) -> Unit
)

@Preview(showBackground = true)
@Composable
private fun PreviewActivatePage() {
    LinkedinActivatePage(
        loadState = LoadState.Success,
        job = JobItemResult(jobTitle = "Android Expert"),
        link = "https://www.linkedin.com/in",
        onSkipClicked = {},
        onShowTips = {},
        onClickActivate = {},
        onClickRetry = {}
    )
}

@Preview(showBackground = true)
@Composable
private fun PreviewParsedHeader() {
    Column {
        GuidanceHeader(false)

        GuidanceHeader(true)
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewParsedPage() {
    LinkedinParsedPage(
        GeekRegisterLinkedinUiState(
            jobTitles = listOf(
                HighlightBean(name = "Java Developer"),
                HighlightBean(name = "Python Developer"),
                HighlightBean(name = "C++ Engineer"),
                HighlightBean(name = "Database Administrator"),
            )
        ),
        onBackPressed = {},
        onNextClick = {}
    )
}
