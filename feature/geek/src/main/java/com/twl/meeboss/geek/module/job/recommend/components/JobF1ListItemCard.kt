package com.twl.meeboss.geek.module.job.recommend.components

import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.twl.meeboss.geek.export.GeekPageRouter
import com.twl.meeboss.geek.model.bean.GeekF1ListBean
import com.twl.meeboss.geek.model.constants.GeekF1CardType
import com.twl.meeboss.geek.module.job.recommend.business.GeekF1Callback

@Composable
fun JobF1ListItemCard(modifier: Modifier = Modifier, bean: GeekF1ListBean, callback: GeekF1Callback, ) {
    val context = LocalContext.current
    Column(modifier = modifier) {
        when (bean.cardType) {
            GeekF1CardType.JOB -> {
                bean.jobs?.let {
                    JobListJobCard(
                        item = it,
                        onCloseClick = {
                        },
                        onItemClick = {
                            it.securityId?.let { securityId ->
                                GeekPageRouter.jumpToGeekJobDetailActivity(context, securityId)
                            }
                        }
                    )
                }

            }

            GeekF1CardType.BEGINNER_GUIDE_CARD -> {
                GeekBeginnerGuideCard(bean = bean, onCloseClick = callback::onClickCardClose)
            }

            GeekF1CardType.TWO_BUTTON_CARD -> {
                GeekF1TwoButtonCard(bean = bean, onLeftClick = callback::onTwoButtonLeftClick, onRightClick = callback::onTwoButtonRightClick, onCloseClick = callback::onClickCardClose)
            }

            GeekF1CardType.BANNERS -> {
                bean.banners?.takeIf { it.isNotEmpty() } ?.let {
                    JobF1ListBanners(banners = it)
                }
            }
        }

    }
}
