package com.twl.meeboss.geek.module.job.recommend.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.plugins.ImagePlugin
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.ui.ktx.onVisible
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.model.bean.Banner

private const val TAG = "JobF1ListBanners"

/**
 * banner加载状态占位图插件
 */
private class BannerLoadingPlaceholderPlugin(
    @DrawableRes private val placeholderRes: Int
) : ImagePlugin.LoadingStatePlugin {

    @Composable
    override fun compose(
        modifier: Modifier,
        imageOptions: ImageOptions,
        executor: @Composable (IntSize) -> Unit
    ): ImagePlugin = apply {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = placeholderRes),
                contentDescription = null,
                // 确保图标以原始尺寸绘制
                contentScale = ContentScale.None
            )
        }
    }
}

/**
 * banner失败状态占位图插件
 */
private class BannerFailurePlaceholderPlugin(
    @DrawableRes private val placeholderRes: Int
) : ImagePlugin.FailureStatePlugin {

    @Composable
    override fun compose(
        modifier: Modifier,
        imageOptions: ImageOptions,
        reason: Throwable?
    ): ImagePlugin = apply {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = placeholderRes),
                contentDescription = null,
                // 确保图标以原始尺寸绘制
                contentScale = ContentScale.None
            )
        }
    }
}

@Composable
fun JobF1ListBanners(
    modifier: Modifier = Modifier,
    banners: List<Banner>
) {
    // 目前场景只会配置一个banner
    val banner = banners[0]
    Column {
        GlideImage(
            imageModel = {
                banner.imageUrl
            },
            previewPlaceholder = painterResource(id = R.drawable.job_f1_list_banner_place_holder),
            component = rememberImageComponent {
                +BannerLoadingPlaceholderPlugin(placeholderRes = R.drawable.job_f1_list_banner_place_holder)
                +BannerFailurePlaceholderPlugin(placeholderRes = R.drawable.job_f1_list_banner_place_holder)
            },
            imageOptions = ImageOptions(
                // 主图片的 ContentScale
                contentScale = ContentScale.Crop,
                contentDescription = "banner"
            ),
            modifier = modifier
                .fillMaxWidth()
                .aspectRatio(2.93f)
                .clip(RoundedCornerShape(12.dp))
                .background(BlackEBEBEB)
                .noRippleClickable {
                    XLog.info(TAG, "click banner, jumpUrl: ${banner.jumpUrl}")
                    ProtocolHelper.parseProtocol(banner.jumpUrl)
                }.onVisible {
                    // “应用内活动入口展示时”上报埋点
                    PointHelper.reportPoint("eventpage-entrance-show")
                }
        )

        HorizontalDivider(color = Color.Transparent, thickness = 12.dp)
    }
}