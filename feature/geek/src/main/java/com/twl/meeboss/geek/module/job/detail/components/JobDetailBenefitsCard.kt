package com.twl.meeboss.geek.module.job.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.core.ui.component.layout.FlowLayout
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_E5E5EA
import com.twl.meeboss.geek.R

@Composable
fun JobDetailBenefitsCard(benefits: List<OptionBean>) {
    HorizontalDivider(color = BlackEBEBEB)
    Text(text = stringResource(id = R.string.job_pay_benefits), modifier = Modifier.padding(top = 28.dp, bottom = 16.dp),
        fontSize = 20.sp, fontWeight = FontWeight.SemiBold,
        color = Black222222
    )

    FlowLayout(
        modifier = Modifier.padding(bottom = 28.dp),
        horizontalSpacing = 8.dp,
        verticalSpacing = 8.dp,
        maxLine = Int.MAX_VALUE
    ) {
        benefits.forEach {
            Box(
                modifier = Modifier
                    .background(
                        Color.Transparent,
                        RoundedCornerShape(8.dp)
                    )
                    .border(
                        1.dp,
                        COLOR_E5E5EA,
                        RoundedCornerShape(8.dp)
                    )
                    .padding(12.dp)
            ) {
                Text(
                    text = it.name,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium,
                    fontSize = 13.sp,
                    color = Black484848
                )
            }
        }
    }
}