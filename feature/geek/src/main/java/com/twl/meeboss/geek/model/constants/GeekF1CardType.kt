package com.twl.meeboss.geek.model.constants

import androidx.annotation.IntDef

@Target(AnnotationTarget.TYPE, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.SOURCE)
@IntDef(flag = true, value = [
    GeekF1CardType.JOB,
    GeekF1CardType.BEGINNER_GUIDE_CARD,
    GeekF1CardType.BANNERS,
    GeekF1CardType.TWO_BUTTON_CARD
])
annotation class GeekF1CardType {
    companion object {
        // 职位卡片
        const val JOB = 0
        // 新手指引卡片
        const val BEGINNER_GUIDE_CARD = 1
        // banners（展示在头部，目前只配置一个，后续可能会配置成多个，形成轮播样式）
        const val BANNERS = 2
        // 两个操作按钮的卡片：visa卡片
        const val TWO_BUTTON_CARD = 3
    }
}