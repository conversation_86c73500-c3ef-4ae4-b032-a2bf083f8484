package com.twl.meeboss.geek.module.register.viewmodel

import javax.inject.Inject
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.SalaryBean
import com.twl.meeboss.base.model.job.JobItemResult
import com.twl.meeboss.base.model.profile.EducationExperienceBean
import com.twl.meeboss.base.model.profile.WorkExperienceBean
import com.twl.meeboss.base.mudule.ModuleManager
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.foundation.moduleservice.GeekUserInfoService
import com.twl.meeboss.geek.module.guidance.components.GeekMandatoryInfo
import com.twl.meeboss.geek.module.me.quickcomplete.model.GeekLinkedinAnalyzeResult
import com.twl.meeboss.geek.repos.GeekJobRepository
import com.twl.meeboss.geek.repos.GeekLinkedinRepository
import com.twl.meeboss.geek.repos.GeekRepository
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map

/**
 * LinkedIn注册激活功能的ViewModel
 *
 * 主要功能：
 * 1. LinkedIn URL解析和验证
 * 2. 自动解析LinkedIn信息并填充用户资料（工作经验、教育经历、基本信息等）
 * 3. 管理注册流程的两个阶段：URL输入阶段(phase=0) 和 信息确认阶段(phase=1)
 * 4. 与后端API交互，完成用户信息更新
 *
 * 业务流程：
 * 用户输入LinkedIn URL -> 解析LinkedIn信息 -> 用户确认/编辑信息 -> 提交完成注册
 */
@HiltViewModel
class GeekRegisterLinkedinViewModel @Inject constructor(
    private val remote: GeekRepository,
    private val repos: GeekLinkedinRepository,
    private val jobRepo: GeekJobRepository,
) : BaseMviViewModel<GeekRegisterLinkedinUiState, GeekRegisterLinkedinUiIntent>() {

    /**
     * 获取当前注册流程阶段
     * @return 0-LinkedIn URL输入阶段, 1-信息确认编辑阶段
     */
    val phase: Int
        get() = uiStateFlow.value.phase

    val postResult: MutableLiveData<Boolean> = MutableLiveData()

    private var analyzeJob: Job? = null

    private val securityIdFlow: MutableStateFlow<String> = MutableStateFlow("")

    fun update(securityId: String) {
        XLog.info(TAG, "update securityId: $securityId, current phase: $phase")
        securityIdFlow.value = securityId
    }

    /**
     * 初始化UI状态
     * 根据是否已解析过LinkedIn信息来决定启动时显示URL输入还是信息确认
     */
    override fun initUiState(): GeekRegisterLinkedinUiState {
        val isParsed = repos.isParsed()
        val initialPhase = if (isParsed) 1 else 0
        XLog.info(TAG, "initUiState - isParsed: $isParsed, initialPhase: $initialPhase")
        return GeekRegisterLinkedinUiState(phase = initialPhase)
    }

    /**
     * 监听阶段变化和ID变化，自动响应推荐职位卡片的数据更新请求
     */
    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        XLog.info(TAG, "onCreate - start monitoring phase and securityId changes")
        launcherOnIO {
            uiStateFlow
                .filter { it.jobCard.jobTitle.isNullOrBlank() }
                .map { it.phase }
                .combine(securityIdFlow) { ph, id -> ph to id }
                .distinctUntilChanged()
                .collect { (ph, id) ->
                    XLog.info(TAG, "phase/securityId changed - phase: $ph, securityId: $id")
                    if (ph == 0 && id.isNotBlank()) {
                        XLog.info(TAG, "starting initJobCard for securityId: $id")
                        initJobCard(id).join()
                    }
                }
        }
    }

    /**
     * ViewModel启动时加载已保存的用户数据，及时响应其它页面的数据填写内容
     * 从本地存储加载LinkedIn URL、职位偏好、薪资、个人信息、工作经验、教育经历等
     */
    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)
        XLog.info(TAG, "onStart - loading saved user data from local storage")
        launcherOnIO {
            val jobs = repos.getJobs()
            val cities = repos.getCities()
            val salary = repos.getSalary()
            val (firstname, lastname) = repos.getName()
            val edu = repos.eduExpList()
            val noExp = repos.noWorkExp()
            val (works, workCount) = if (noExp) {
                listOf(WorkExperienceBean(localNoExperience = true)) to 0
            } else {
                val works = repos.workExpList()
                works to works.size
            }

            XLog.info(TAG, "loaded user data - jobs: ${jobs.size}, cities: ${cities.size}, " +
                    "salary: ${salary.minSalary}, name: '$firstname $lastname', " +
                    "edu: ${edu.size}, workCount: $workCount, noExp: $noExp")

            sendUiState {
                copy(
                    linkedin = linkedin,
                    jobTitles = jobs,
                    cities = cities,
                    salary = salary,
                    completeBaseInfo = firstname.isNotEmpty() && lastname.isNotEmpty(),
                    completeWorkExp = noExp || workCount > 0,
                    completeEduExp = edu.isNotEmpty(),
                    firstName = firstname,
                    lastName = lastname,
                    noWorkExp = noExp,
                    works = works,
                    workCount = workCount,
                    educations = edu,
                    eduCount = edu.size
                ).checkAllSet().also { newState ->
                    XLog.info(TAG, "updated UI state - allSet: ${newState.allSet}, " +
                            "completeBaseInfo: ${newState.completeBaseInfo}, " +
                            "completeWorkExp: ${newState.completeWorkExp}, " +
                            "completeEduExp: ${newState.completeEduExp}")
                    if (newState.phase == 1) {
                        XLog.info(TAG, "LinkedIn info complete - unset count: ${newState.countUnset()}")
                        // 埋点统计：记录完成状态
                        GeekPointReporter.guidancePageShow(
                            type = if (newState.allSet) 3 else 2,
                            count = newState.countUnset(),
                            source = 4
                        )
                    }
                }
            }
        }
    }

    override fun handleIntent(intent: IUiIntent) {
        XLog.info(TAG, "handleIntent: ${intent::class.simpleName}")
        when (intent) {
            // 返回到LinkedIn URL输入阶段
            GeekRegisterLinkedinUiIntent.Back -> {
                XLog.info(TAG, "user clicked back - switching from phase $phase to phase 0")
                launcherOnIO { repos.setParsed(false) }
                sendUiState { copy(phase = 0) }
            }

            // 跳过LinkedIn激活流程
            GeekRegisterLinkedinUiIntent.Skip -> {
                XLog.info(TAG, "user skipped LinkedIn activation")
                GeekLinkedinRepository.saveUserSkipped()
                GeekLinkedinRepository.clearActivation()
                XLog.info(TAG, "cleared activation data and marked as skipped")
                postResult.postValue(true)
            }

            // 刷新推荐职位信息
            GeekRegisterLinkedinUiIntent.Refresh -> {
                val securityId = securityIdFlow.value
                XLog.info(TAG, "user clicked refresh - refreshing job card for securityId: $securityId")
                initJobCard(securityId)
            }

            // 接收新的LinkedIn安全ID
            is GeekRegisterLinkedinUiIntent.NewLinkedinId -> {
                XLog.info(TAG, "received new LinkedIn securityId: ${intent.securityId}, current phase: $phase")
                if (phase == 0) {
                    update(intent.securityId)
                } else {
                    XLog.info(TAG, "ignored new securityId because current phase is $phase")
                }
            }

            // 激活LinkedIn解析流程
            is GeekRegisterLinkedinUiIntent.Activate -> {
                XLog.info(TAG, "user activated LinkedIn parsing - URL: ${intent.linkedin}")
                analyzeJob?.cancel()
                analyzeJob = requestLinkedinProfile(intent.linkedin)
                sendUiState { copy(linkedin = intent.linkedin).checkAllSet() }
            }

            // 更新职位偏好
            is GeekRegisterLinkedinUiIntent.OnJobTitleChange -> {
                XLog.info(TAG, "user updated job titles - count: ${intent.jobTitles.size}")
                launcherOnIO { repos.saveJobs(intent.jobTitles) }
                sendUiState { copy(jobTitles = intent.jobTitles).checkAllSet() }
            }

            // 更新城市偏好
            is GeekRegisterLinkedinUiIntent.OnCityChange -> {
                XLog.info(TAG, "user updated cities - count: ${intent.locations.size}")
                launcherOnIO { repos.saveCities(intent.locations) }
                sendUiState { copy(cities = intent.locations).checkAllSet() }
            }

            // 更新薪资期望
            is GeekRegisterLinkedinUiIntent.OnSalaryChange -> {
                XLog.info(TAG, "user updated salary - minSalary: ${intent.minSalary}, " +
                        "type: ${intent.salaryOptionBean.name}, unit: ${intent.salaryUnitDesc}")
                val salary = SalaryBean(
                    minSalary = intent.minSalary,
                    salaryType = intent.salaryOptionBean.code,
                    salaryTypeDesc = intent.salaryOptionBean.name,
                    salaryUnit = intent.salaryUnit,
                    salaryUnitDesc = intent.salaryUnitDesc ?: ""
                )
                launcherOnIO {
                    repos.saveSalary(salary)
                    XLog.info(TAG, "saved salary to local storage")
                }
                sendUiState {
                    copy(salary = salary).checkAllSet()
                }
            }

            // 更新姓名信息
            is GeekRegisterLinkedinUiIntent.OnNameChange -> {
                val firstName = intent.firstName
                val lastName = intent.lastName
                XLog.info(TAG, "user updated name - firstName: '$firstName', lastName: '$lastName'")
                launcherOnIO {
                    repos.updateName(firstName, lastName)
                    XLog.info(TAG, "saved name to local storage")
                }
                sendUiState {
                    copy(
                        completeBaseInfo = firstName.isNotEmpty() && lastName.isNotEmpty(),
                        firstName = firstName,
                        lastName = lastName
                    ).checkAllSet()
                }
            }

            // 提交用户信息，完成注册流程
            is GeekRegisterLinkedinUiIntent.Submit -> {
                requestSubmitData()
            }

            else -> {
                XLog.error(TAG, "unhandled intent: ${intent::class.simpleName}")
            }
        }
    }

    private fun initJobCard(securityId: String): Job {
        XLog.info(TAG, "initJobCard - starting job detail request for securityId: $securityId")
        return requestData(
            enableLoadState = true,
            request = {
                jobRepo.getExploreJobDetail(securityId)
            },
            success = { it: JobDetailResult? ->
                if (it != null) {
                    XLog.info(TAG, "job detail loaded successfully - jobTitle: '${it.jobDetailJobInfo?.jobTitle}', " +
                            "company: '${it.jobDetailCompanyInfo?.name}', " +
                            "linkedinUrl: '${it.linkedinProfileUrl}'")

                    val jobItemResult = JobItemResult(
                        securityId = it.securityId,
                        jobId = it.jobDetailJobInfo?.jobId,
                        jobTitle = it.jobDetailJobInfo?.jobTitle,
                        companyName = it.jobDetailCompanyInfo?.name,
                        jobTypes = it.jobDetailJobInfo?.jobType,
                        avatar = it.jobDetailBossInfo?.avatar,
                        name = it.jobDetailBossInfo?.name,
                        bossPosition = it.jobDetailBossInfo?.bossPosition,
                        companyIndustry = it.jobDetailCompanyInfo?.industryName,
                        locationType = it.jobDetailJobInfo?.locationType,
                        jobLocations = it.jobDetailJobInfo?.address,
                        salaryDesc = it.jobDetailJobInfo?.salaryDesc,
                        jobDesc = it.jobDetailJobInfo?.jobDesc,
                        friended = it.friended,
                        jobStatus = it.jobDetailJobInfo?.jobStatus ?: 0
                    )

                    sendUiState {
                        copy(jobCard = jobItemResult, linkedin = it.linkedinProfileUrl ?: "")
                    }
                } else {
                    XLog.error(TAG, "job detail is null for securityId: $securityId")
                }
            },
            fail = {
                XLog.error(TAG, it, "failed to load job detail for securityId: $securityId - ${it.message}")
                T.ss(it.message)
            }
        )
    }

    /**
     * 请求LinkedIn信息解析
     * 这是LinkedIn解析的核心方法，包含以下步骤：
     * 1. 发起LinkedIn解析请求
     * 2. 轮询获取解析结果
     * 3. 将解析结果填充到用户资料中
     * 4. 更新UI状态到信息确认阶段
     *
     * @param linkedin LinkedIn个人资料URL
     * @return 解析任务的Job对象，可用于取消操作
     */
    private fun requestLinkedinProfile(linkedin: String): Job {
        XLog.info(TAG, "requestLinkedinProfile - starting LinkedIn parsing for URL: $linkedin")
        showLoadingDialog()
        return requestData(
            request = {
                analyzeLinkedin(securityIdFlow.value, linkedin).also {
                    dismissLoadingDialog()
                }
            },
            success = { it: GeekLinkedinAnalyzeResult? ->
                if (it != null && it.finished) {
                    XLog.info(TAG, "LinkedIn analysis completed successfully - " +
                            "jobTitles: ${it.jobTitles?.size}, address: ${it.address?.size}, " +
                            "minSalary: ${it.minSalary}, hasProfile: ${it.updatePreview != null}")

                    // 过滤和处理解析结果
                    val jobs = it.jobTitles?.filter { it.name.isNotBlank() }
                    val address = it.address?.filter { it.name.isNotBlank() }
                    val profile = it.updatePreview
                    val baseInfo = profile?.baseInfo?.takeIf { !it.firstName.isNullOrEmpty() && !it.lastName.isNullOrEmpty() }

                    XLog.info(TAG, "filtered analysis results - jobs: ${jobs?.size}, " +
                            "address: ${address?.size}, hasBaseInfo: ${baseInfo != null}")

                    // 将 LinkedIn 分析结果填充到本地存储和UI状态中
                    launcherOnIO {
                        val workExps = profile?.workExpList?.filter { !it.jobTitle.isNullOrBlank() }
                        val eduExps = profile?.eduExpList

                        XLog.info(TAG, "processing profile data - workExps: ${workExps?.size}, " +
                                "eduExps: ${eduExps?.size}")

                        // 标记为已解析，进入信息确认阶段
                        repos.setParsed(true)
                        XLog.info(TAG, "marked as parsed, switching to phase 1")

                        // 保存各类解析出的信息到本地存储
                        jobs?.let { jobTitles ->
                            repos.saveJobs(jobTitles)
                            XLog.info(TAG, "saved ${jobTitles.size} job titles")
                        }

                        address?.let { addressList ->
                            // 将地址信息转换为城市对象
                            val cities = addressList.map { highlight ->
                                CityBean(
                                    code = highlight.code,
                                    name = highlight.name
                                )
                            }
                            repos.saveCities(cities)
                            XLog.info(TAG, "saved ${cities.size} cities")
                        }

                        // 保存薪资期望
                        if (it.minSalary > 0) {
                            val salary = SalaryBean(
                                minSalary = it.minSalary,
                                salaryType = it.salaryType,
                                salaryTypeDesc = it.salaryTypeDesc,
                                salaryUnit = it.salaryUnit,
                                salaryUnitDesc = it.salaryUnitDesc
                            )
                            repos.saveSalary(salary)
                            XLog.info(TAG, "saved salary: ${it.minSalary} ${it.salaryUnitDesc} ${it.salaryTypeDesc}")
                        }

                        // 保存基本个人信息
                        baseInfo?.let { baseInfo ->
                            val firstName = baseInfo.firstName
                            val lastName = baseInfo.lastName
                            if (!firstName.isNullOrEmpty() && !lastName.isNullOrEmpty()) {
                                repos.updateName(firstName, lastName)
                                XLog.info(TAG, "saved name: '$firstName $lastName'")
                            }
                        }

                        // 保存工作经验
                        workExps?.let {
                            repos.saveWorkExps(workExps)
                            XLog.info(TAG, "saved ${workExps.size} work experiences")
                        }

                        // 保存教育经历
                        eduExps?.let {
                            repos.saveEduExps(eduExps)
                            XLog.info(TAG, "saved ${eduExps.size} education experiences")
                        }

                        // 重新加载数据以更新UI
                        val works = repos.workExpList()
                        val educations = repos.eduExpList()

                        XLog.info(TAG, "reloaded data from storage - works: ${works.size}, educations: ${educations.size}")

                        // 更新UI状态到信息确认阶段
                        sendUiState {
                            // 计算各部分信息的完成状态
                            val hasValidName = !baseInfo?.firstName.isNullOrEmpty() && !baseInfo?.lastName.isNullOrEmpty()
                            val hasWorkExp = works.isNotEmpty()
                            val hasEduExp = educations.isNotEmpty()

                            XLog.info(TAG, "updating UI state - hasValidName: $hasValidName, " +
                                    "hasWorkExp: $hasWorkExp, hasEduExp: $hasEduExp")

                            copy(
                                phase = 1, // 进入信息确认阶段
                                jobTitles = jobs ?: jobTitles,
                                cities = address?.map { highlight ->
                                    CityBean(
                                        code = highlight.code,
                                        name = highlight.name
                                    )
                                } ?: cities,
                                salary = if (it.minSalary > 0) {
                                    SalaryBean(
                                        minSalary = it.minSalary,
                                        salaryType = it.salaryType,
                                        salaryTypeDesc = it.salaryTypeDesc,
                                        salaryUnit = it.salaryUnit,
                                        salaryUnitDesc = it.salaryUnitDesc
                                    )
                                } else salary,
                                // 更新基本信息
                                firstName = baseInfo?.firstName ?: firstName,
                                lastName = baseInfo?.lastName ?: lastName,
                                completeBaseInfo = hasValidName,
                                // 更新工作经验
                                works = works,
                                workCount = works.size,
                                completeWorkExp = hasWorkExp || completeWorkExp,
                                noWorkExp = if (hasWorkExp) false else noWorkExp,
                                // 更新教育经历
                                educations = educations,
                                eduCount = educations.size,
                                completeEduExp = hasEduExp || completeEduExp
                            ).checkAllSet().also { newState ->
                                XLog.info(TAG, "LinkedIn analysis complete - switched to phase 1, " +
                                        "allSet: ${newState.allSet}, unset count: ${newState.countUnset()}")
                                // 埋点统计：记录解析结果和完成状态
                                GeekPointReporter.guidancePageShow(
                                    type = if (newState.allSet) 3 else 2,
                                    count = newState.countUnset(),
                                    source = 4
                                )
                            }
                        }
                    }
                } else {
                    // 解析超时或失败
                    XLog.error(TAG, "LinkedIn analysis failed or timed out - finished: ${it?.finished}")
                    T.ss(R.string.job_seeker_analysis_timeout.toResourceString())
                }
            },
            fail = {
                XLog.error(TAG, it, "LinkedIn analysis request failed - ${it.message}")
                T.ss(it.message)
            }
        )
    }

    /**
     * LinkedIn信息解析的底层API调用
     * 采用轮询机制获取解析结果：
     * 1. 首先发起解析请求，获取resultKey
     * 2. 使用resultKey轮询获取解析进度和结果
     * 3. 最多轮询 [QUERY_TIMES] 次，每次间隔1秒
     *
     * @param securityId LinkedIn相关的安全标识
     * @param linkedin LinkedIn个人资料URL
     * @return 包含解析结果的Result对象
     */
    private suspend fun analyzeLinkedin(securityId: String, linkedin: String): Result<GeekLinkedinAnalyzeResult> {
        XLog.info(TAG, "analyzeLinkedin - starting analysis request for securityId: $securityId")

        val analyze = remote.geekResumeAnalyze(
            analyzeScene = "",
            resumeId = "",
            linkedInUrl = linkedin
        )
        val resultKey: String? = analyze.getOrNull()?.resultKey

        if (resultKey == null) {
            XLog.error(TAG, "failed to get resultKey from initial analysis request")
            return analyze.map { GeekLinkedinAnalyzeResult() }
        }

        XLog.info(TAG, "got resultKey: $resultKey, starting polling for results")

        var polling: Boolean
        var times = 0
        var result: Result<GeekLinkedinAnalyzeResult>
        do {
            times++
            XLog.debug(TAG, "polling attempt #$times for resultKey: $resultKey")
            result = remote.geekLinkedinAnalyzeResult(securityId, resultKey)
            polling = result.getOrNull()?.finished != true

            if (polling) {
                XLog.debug(TAG, "polling #$times not finished yet, waiting 1s")
                delay(1000)
            } else {
                XLog.info(TAG, "polling completed after $times attempts")
            }
        } while (polling && times < QUERY_TIMES)

        if (times >= QUERY_TIMES) {
            XLog.error(TAG, "polling timed out after $QUERY_TIMES attempts")
        }

        return result
    }

    private fun requestSubmitData() {
        val state = uiStateFlow.value
        XLog.info(
            TAG, "user submitted profile - allSet: ${state.allSet}, " +
                    "jobTitles: ${state.jobTitles.size}, cities: ${state.cities.size}, " +
                    "works: ${state.works.size}, educations: ${state.educations.size}"
        )

        requestData(
            enableLoadState = true,
            request = {
                XLog.info(TAG, "starting API call - geekUpdateProfileByLinkedin")
                remote.geekUpdateProfileByLinkedin(
                    firstName = state.firstName,
                    lastName = state.lastName,
                    jobTitles = state.jobTitles,
                    cities = state.cities,
                    salary = state.salary,
                    works = state.works.map { it.copy(id = null) },
                    educations = state.educations.map { it.copy(id = null) }
                )
            },
            success = {
                XLog.info(TAG, "API call succeeded - profile updated successfully")
                GeekUserInfoService.setComplete()
                ModuleManager.updateUserInfo()
                AccountManager.setIdentity(0)
                AccountManager.setFirstCompleteStatus(1)
                repos.clearParsedData()
                XLog.info(TAG, "completed user setup and cleared local data")
                postResult.postValue(true)
            },
            fail = {
                XLog.error(TAG, it, "API call failed - ${it.message}")
                T.ss(it.message)
            }
        )
    }

    companion object {
        private const val QUERY_TIMES = 50
    }
}

/**
 * LinkedIn注册激活功能的UI状态数据类
 *
 * @property phase 当前阶段：0-LinkedIn URL输入阶段，1-信息确认编辑阶段
 * @property jobCard 推荐的职位卡片信息
 * @property linkedin LinkedIn个人资料URL
 * @property allSet 是否所有必填信息都已完成
 * @property jobTitles 职位偏好列表
 * @property cities 城市偏好列表
 * @property salary 薪资期望信息
 * @property completeWorkExp 工作经验是否完成
 * @property completeEduExp 教育经历是否完成
 * @property completeBaseInfo 基本信息是否完成
 * @property noWorkExp 是否无工作经验
 * @property firstName 名
 * @property lastName 姓
 * @property works 工作经验列表
 * @property workCount 工作经验数量
 * @property educations 教育经历列表
 * @property eduCount 教育经历数量
 */
data class GeekRegisterLinkedinUiState(
    val phase: Int = 0,
    val jobCard: JobItemResult = JobItemResult(),
    val linkedin: String = "",
    val allSet: Boolean = false,
    val jobTitles: List<HighlightBean> = listOf(),
    val cities: List<CityBean> = listOf(),
    val salary: SalaryBean = SalaryBean(),
    override val completeWorkExp: Boolean = false,
    override val completeEduExp: Boolean = false,
    override val completeBaseInfo: Boolean = false,
    override val noWorkExp: Boolean = false,
    override val firstName: String = "",
    override val lastName: String = "",
    override val works: List<WorkExperienceBean> = emptyList(),
    override val workCount: Int = 0,
    override val educations: List<EducationExperienceBean> = emptyList(),
    override val eduCount: Int = 0
) : GeekMandatoryInfo, IUiState {

    /**
     * 检查并更新所有必填信息的完成状态
     * 必填信息包括：职位偏好、城市偏好、薪资期望、基本信息、工作经验、教育经历
     * @return 更新后的UI状态对象
     */
    fun checkAllSet(): GeekRegisterLinkedinUiState {
        return copy(
            allSet = jobTitles.isNotEmpty()
                    && cities.isNotEmpty()
                    && salary.minSalary > 0
                    && completeBaseInfo
                    && completeWorkExp
                    && completeEduExp
        )
    }

    /**
     * 统计未完成的必填信息数量
     */
    fun countUnset(): Int {
        return listOf(
            jobTitles.isEmpty(),
            cities.isEmpty(),
            salary.minSalary <= 0,
            !completeBaseInfo,
            !completeWorkExp,
            !completeEduExp
        ).count { it }
    }
}

sealed interface GeekRegisterLinkedinUiIntent : IUiIntent {
    /** 返回上一阶段 */
    data object Back : GeekRegisterLinkedinUiIntent

    /** 跳过LinkedIn激活流程 */
    data object Skip : GeekRegisterLinkedinUiIntent

    /** 刷新推荐职位信息 */
    data object Refresh : GeekRegisterLinkedinUiIntent

    /** 提交用户信息完成注册 */
    data object Submit : GeekRegisterLinkedinUiIntent

    /**
     * 接收新的LinkedIn广告ID
     */
    data class NewLinkedinId(
        val securityId: String
    ) : GeekRegisterLinkedinUiIntent

    /**
     * 激活LinkedIn解析流程
     * @property linkedin LinkedIn个人资料URL
     */
    data class Activate(
        val linkedin: String
    ) : GeekRegisterLinkedinUiIntent

    /**
     * 更新职位偏好
     */
    data class OnJobTitleChange(
        val jobTitles: List<HighlightBean>
    ) : GeekRegisterLinkedinUiIntent

    /**
     * 更新城市偏好
     */
    data class OnCityChange(
        val locations: List<CityBean>
    ) : GeekRegisterLinkedinUiIntent

    /**
     * 更新薪资期望
     * @property minSalary 最低薪资
     * @property salaryOptionBean 薪资类型选项
     * @property salaryUnit 薪资单位
     * @property salaryUnitDesc 薪资单位描述
     */
    data class OnSalaryChange(
        val minSalary: Long,
        val salaryOptionBean: OptionBean,
        val salaryUnit: Long,
        val salaryUnitDesc: String?
    ) : GeekRegisterLinkedinUiIntent

    /**
     * 更新姓名信息
     */
    data class OnNameChange(
        val firstName: String,
        val lastName: String,
    ) : GeekRegisterLinkedinUiIntent
}
