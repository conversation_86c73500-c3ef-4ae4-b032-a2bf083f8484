package com.twl.meeboss.geek.model.bean

import androidx.compose.runtime.Stable
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.geek.model.constants.GeekF1CardType
import com.twl.meeboss.base.model.job.JobItemResult
import java.util.UUID

@Stable
data class GeekF1ListBean(
    @GeekF1CardType val cardType: Int,
    val jobs: JobItemResult? = null,
    val extend: GeekListExtendBean? = null,
    val banners: List<Banner>? = null
) :BaseEntity{
    fun getUniqueKey(): String {
        return when (cardType) {
            GeekF1CardType.JOB -> jobs?.securityId ?: ""
            GeekF1CardType.BEGINNER_GUIDE_CARD -> UUID.randomUUID().toString()
            else -> UUID.randomUUID().toString()
        }
    }
}