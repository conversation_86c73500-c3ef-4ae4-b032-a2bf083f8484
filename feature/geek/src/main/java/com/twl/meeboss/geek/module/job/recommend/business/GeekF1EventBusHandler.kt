package com.twl.meeboss.geek.module.job.recommend.business

import androidx.lifecycle.LifecycleOwner
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.geek.export.GeekEventBusKey
import com.twl.meeboss.geek.module.job.recommend.GeekF1ViewModel
import com.twl.meeboss.geek.module.job.recommend.GeekJobUiIntent

class GeekF1EventBusHandler(private val owner: LifecycleOwner,
                            private val viewModel: GeekF1ViewModel) {

    fun init(){
        //保存求职意向成功
        owner.liveEventBusObserve(GeekEventBusKey.SAVE_JOB_PREFERENCE_INFECT_F1) { _: Boolean ->
            viewModel.sendUiIntent(GeekJobUiIntent.RefreshPage(true))
        }
        //编辑工作经验成功
        owner.liveEventBusObserve(EventBusKey.GEEK_EDIT_WORK_EXPERIENCE_SUCCESS){_:String?->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteBeginnerWorkExpCard)
        }
        //编辑教育经验成功
        owner.liveEventBusObserve(EventBusKey.GEEK_EDIT_EDU_EXPERIENCE_SUCCESS){_:String?->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteBeginnerEduExpCard)
        }
        //编辑姓名成功
        owner.liveEventBusObserve(EventBusKey.EDIT_USER_NAME_SUCCESS){_:String?->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteBeginnerNameCard)
        }
        owner.liveEventBusObserve(EventBusKey.EMAIL_VERIFIED_SUCCESS){_:String?->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteEmailVerifiedCard)
        }
        owner.liveEventBusObserve(EventBusKey.GEEK_BEGINNER_GUIDANCE_COMPLETE){_:Boolean->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteAllNotFillCard)
        }
        owner.liveEventBusObserve(EventBusKey.GEEK_EDIT_SKILL_SUCCESS) { _: String? ->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteSkillCard)
        }
        //签证赞助状态完善成功
        owner.liveEventBusObserve(GeekEventBusKey.VISA_SPONSORSHIP_COMPLETED) { _: Boolean ->
            viewModel.sendUiIntent(GeekJobUiIntent.DeleteVisaSponsorshipCard)
        }
        owner.liveEventBusObserve(EventBusKey.GEEK_IMPROVEMENT_COUNT) { count: Int ->
            viewModel.sendUiIntent(GeekJobUiIntent.ChangeImproveCount(count))
        }
    }
}