package com.twl.meeboss.geek.module.job.recommend.components

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.runtime.LaunchedEffect
import com.twl.meeboss.components.R
import com.twl.meeboss.components.cards.UserTwoButtonCard
import com.twl.meeboss.geek.model.bean.GeekF1ListBean
import com.twl.meeboss.geek.model.bean.GeekListExtendBean
import com.twl.meeboss.geek.model.bean.GeekListExtendType
import com.twl.meeboss.geek.utils.GeekPointReporter

/**
 * @author: musa on 2025/06/09
 * @e-mail: <EMAIL>
 * @desc: GeekF1列表两种操作的卡片
 */

@Composable
fun GeekF1TwoButtonCard(
    bean: GeekF1ListBean,
    onLeftClick: (GeekF1ListBean) -> Unit,
    onRightClick: (GeekF1ListBean) -> Unit,
    onCloseClick: (GeekF1ListBean) -> Unit
) {
    val (title, leftButtonText, rightButtonText, iconRes) = bean.extend?.getTwoButtonBean() ?: return

    // 使用LaunchedEffect确保埋点只上报一次
    if (bean.extend.type == GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS) {
        LaunchedEffect(bean.extend.type) {
            GeekPointReporter.sponsorCardShow()
        }
    }

    UserTwoButtonCard(
        modifier = Modifier.padding(bottom = 12.dp),
        title = title,
        leftButtonText = leftButtonText,
        rightButtonText = rightButtonText,
        iconResId = iconRes,
        onLeftButtonClick = { onLeftClick(bean) },
        onRightButtonClick = { onRightClick(bean) },
        onCloseClick = {onCloseClick(bean)}
    )
}

private data class TwoButtonCardBean(
    val title : String,
    val leftButtonText: String,
    val rightButtonText: String,
    @DrawableRes val icon: Int
)

@Composable
private fun GeekListExtendBean.getTwoButtonBean(): TwoButtonCardBean? {
    return when (type){
        GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS -> TwoButtonCardBean(
            title = stringResource(R.string.job_seeker_job_prep_visa_sponsor_ask),
            leftButtonText = stringResource(R.string.job_seeker_visa_sponsor_answer_yes),
            rightButtonText = stringResource(R.string.job_seeker_visa_sponsor_answer_no),
            icon = R.drawable.ui_icon_visa,
        )

        else -> null
    }
}
 