package com.twl.meeboss.geek.utils

import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.profile.GeekProfileBean
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.geek.export.ResumeAnalysisType
import com.twl.meeboss.geek.foundation.moduleservice.GeekUserInfoService
import com.twl.meeboss.geek.model.bean.GeekListExtendType
import com.twl.meeboss.geek.model.bean.JobCategory

object GeekPointReporter {

    /**
     * 引导完善卡片展示
     * @param source 展示来源：1，f1推荐列表；2，JD详情页；3，个人中心；
     */
    fun completeGuideShow(cardType: Int, source: Int) {
        //引导内容：1，完整引导；2，工作经历；3，教育经历；4，姓名
        val type = when (cardType) {
            GeekListExtendType.BEGINNER_GUIDE_ADD_WORK_EXP -> 2
            GeekListExtendType.BEGINNER_GUIDE_ADD_EDU_EXP -> 3
            GeekListExtendType.BEGINNER_GUIDE_ADD_NAME -> 4
            GeekListExtendType.VERIFY_USER_EMAIL -> 5
            GeekListExtendType.JS_GUIDE_NAME -> 6
            GeekListExtendType.JS_GUIDE_LOCATION -> 7
            GeekListExtendType.JS_GUIDE_AVATAR -> 8
            GeekListExtendType.JS_GUIDE_WORK_EXP -> 9
            GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP -> 10
            GeekListExtendType.JS_GUIDE_SKILL -> 11
            GeekListExtendType.JS_GUIDE_IMPROVEMENT -> 12
            else -> 1
        }
        PointHelper.reportPoint("complete-guide-show") {
            addP(type.toString())
            addP2(source.toString())
        }
    }


    /**
     * 引导完善卡片点击
     * @param source 展示来源：1，f1推荐列表；2，JD详情页;3，chat空列表；4，viewed me空列表；5，个人中心；
     * @param clickAction 1，点击complete去完善；2，点击叉号关闭
     */
    fun completeGuideClick(cardType: Int, source: Int, clickAction: Int = 0) {
        //引导内容：1，完整引导；2，工作经历；3，教育经历；4，姓名
        val type = when (cardType) {
            GeekListExtendType.BEGINNER_GUIDE_FLOW -> 1
            GeekListExtendType.BEGINNER_GUIDE_ADD_WORK_EXP -> 2
            GeekListExtendType.BEGINNER_GUIDE_ADD_EDU_EXP -> 3
            GeekListExtendType.BEGINNER_GUIDE_ADD_NAME -> 4
            GeekListExtendType.VERIFY_USER_EMAIL -> 5
            GeekListExtendType.JS_GUIDE_NAME -> 6
            GeekListExtendType.JS_GUIDE_LOCATION -> 7
            GeekListExtendType.JS_GUIDE_AVATAR -> 8
            GeekListExtendType.JS_GUIDE_WORK_EXP -> 9
            GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP -> 10
            GeekListExtendType.JS_GUIDE_SKILL -> 11
            GeekListExtendType.JS_GUIDE_IMPROVEMENT -> 12
            else -> 0
        }
        PointHelper.reportPoint("complete-guide-click") {
            if (type > 0) {
                addP(type.toString())
            }
            addP2(source.toString())
            if (clickAction > 0) {
                addP3(clickAction.toString())
            }
        }
    }


    /**
     * 求职者LinkedIn快速完善流程的第一个页面，点击相应按钮
     *
     * [action] 1，Activate；2，Skip
     */
    fun linkedinActivate(action: Int, linkedin: String?, jobId: String?) {
        PointHelper.reportPoint("firstcomplete-activation-click") {
            addP(action.toString())
            if (!linkedin.isNullOrEmpty()) {
                addP2(linkedin)
            }
            if (!jobId.isNullOrEmpty()) {
                addEncryptP3(jobId)
            }
        }
    }


    /**
     * 求职者在my jobs页面点击不同子tab
     * @param tab 1，saved；2，in progress；3，archived
     */
    fun myJobSubTabClick(tab: Int) {
        PointHelper.reportPoint("myjobs-subtab-click") {
            addP(tab.toString())
        }
    }

    /**
     * 求职者在my jobs页面操作卡片
     * @param action 1，unsave；2，remove；3，archive
     */
    fun myJobsSet(action: Int, jobId: String) {
        PointHelper.reportPoint("myjobs-set") {
            addP(action.toString())
            addEncryptP2(jobId)
        }
    }

    /**
     * C用户点击主页底部Tab
     * @param index 0，F1；2，F2；3，F3
     */
    fun geekMainTabClick(index: Int) {
        when (index) {
            0 -> {}
            1 -> {
                PointHelper.reportPoint("f4tab-companies-view")
            }
            2 -> {
                PointHelper.reportPoint("f2tab-click")
            }
            3 -> {
                PointHelper.reportPoint("f3tab-click")
            }
        }
    }


    /**
     * @param button 点击按钮：1，next；2，back；3，叉号关闭
     */
    fun chatApplyWorkExpContinue1() {
        PointHelper.reportPoint("moreinfo-workexp-continue") {
            addP("4")
            addP2("2")
        }
    }


    fun chatApplyEduExpContinue1() {
        PointHelper.reportPoint("moreinfo-educationexp-continue") {
            addP("4")
            addP2("2")
        }
    }


    fun chatApplyNameContinue1() {
        PointHelper.reportPoint("moreinfo-name-continue") {
            addP("4")
            addP2("2")
        }
    }


    /**
     * 求职者点击f3个人中心浮动卡片
     */
    fun f3tabCardClick(type: Int) {
        PointHelper.reportPoint("f3tab-card-click") {
            addP(if (type == 1) type.toString() else "2")
        }
    }

    /**
     * 求职者点击f3个人中心主菜单
     */
    fun f3tabMenuClick(type: Int, expId: String? = null) {
        PointHelper.reportPoint("f3tab-menu-click") {
            addP(type.toString())
            if (expId != null) {
                addEncryptP2(expId)
            }
        }
    }

    /**
     * 求职者在个人中心profile页面点击进入personal information
     */
    fun f3tabProfileInfo() {
        PointHelper.reportPoint("f3tab-profile-info")
    }

    /**
     * 求职者在个人中心profile页面点击加号➕按钮
     * @param type 点击目录：1，work experience；2，education；3，project experience；4，language；5，skills；6，certification
     */
    fun f3tabProfileAdd(type: Int) {
        PointHelper.reportPoint("f3tab-profile-add") {
            addP(type.toString())
        }
    }

    /**
     * 求职者在个人中心profile页面点击加号➕按钮
     * @param type 1，work experience；2，education；3，project experience；4，language；5，skills；6，certification；7，phone number or location
     * @param status 增加保存情况：1，create；2，edit
     */
    fun f3tabProfileSave(type: Int, status: Int) {
        PointHelper.reportPoint("f3tab-profile-save") {
            addP(type.toString())
            addP2(status.toString())
        }
    }

    /**
     * 求职者在resume attachment页面点击upload按钮
     */
    fun cvAttachUploadClick() {
        PointHelper.reportPoint("cvattach-upload-click")
    }

    /**
     * 求职者在f1tab页面点击加号进入Job preference期望修改页面
     * @param expId 当前求职期望
     */
    fun f1tabJobPreferClick(expId: String) {
        PointHelper.reportPoint("f1tab-jobprefer-click") {
            addEncryptP2(expId)
        }
    }

    /**
     * 求职者在Job preference期望修改页面，点击save按钮
     * @param isEdit 是否编辑
     * @param expId 旧期望ID
     * @param newExpId 新期望ID
     */
    fun jobPreferSave(isEdit: Boolean, expId: String, newExpId: String) {
        PointHelper.reportPoint("jobprefer-save") {
            addP(if (isEdit) "1" else "2")
            addEncryptP2(expId)
            addEncryptP3(newExpId)
        }
    }

    /**
     * 求职者从不同入口进入简历解析页面
     * @param type 入口来源：1，开聊引导；2，f1列表卡片引导；3，个人中心Profile页面
     */
    fun quickCompleteStart(type: Int) {
        PointHelper.reportPoint("quickcomplete-start") {
            addP(type.toString())
        }
    }

    /**
     * 求职者在简历解析页面选择解析方式
     * @param type path：1，select resume；2，provide LinkedIn url
     */
    fun quickCompletePathContinue(type: Int) {
        PointHelper.reportPoint("quickcomplete-path-continue") {
            addP(type.toString())
        }
    }

    /**
     * 求职者在简历解析结果页面点击弹窗按钮
     * @param type 1，Upload to profile；2，Preview
     */
    fun quickCompleteResultClick(type: Int) {
        PointHelper.reportPoint("quickcomplete-result-click") {
            addP(type.toString())
        }
    }

    /**
     * 求职者选择简历解析方式之后，记录解析结果
     * @param type path：1，select resume；2，provide LinkedIn url
     */
    fun quickCompleteResultShow(type: Int, num: Int) {
        PointHelper.reportPoint("quickcomplete-result-show") {
            addP(type.toString())
            addP2(num.toString())
        }
    }

    /**
     *求职者首善流程选择title页面每次点击一级类目按钮时上报
     *
     * [source] 来源：1，首善流程；2，LinkedIn快速完善流程；3；F1修改期望页；4，F3修改期望页
     */
    fun firstCompleteTitleClick(primaryJobTitle: String, source: Int) {
        PointHelper.reportPoint("firstcomplete-title-first") {
            addP(primaryJobTitle)
            addP4(source.toString())
        }
    }

    /**
     * 求职者首善流程选择title页面每次点击二级类目按钮时上报
     *
     * [source] 来源：1，首善流程；2，LinkedIn快速完善流程；3；F1修改期望页；4，F3修改期望页
     */
    fun firstCompleteSubTitleClick(primaryJobTitle: String, subJobTitle: String, isSelected: Boolean, source: Int) {
        PointHelper.reportPoint("firstcomplete-title-second") {
            addP(primaryJobTitle)
            addP2(subJobTitle)
            addP3(if (isSelected) "1" else "2")
            addP4(source.toString())
        }
    }

    /**
     * 求职者首善流程选择title页面每次点击save按钮时上报
     */
    fun firstCompleteTitleSave(primaryJobTitle: String, subJobTitles: List<String>, source: Int) {
        PointHelper.reportPoint("firstcomplete-title-save"){
            addP(primaryJobTitle)
            addP2(subJobTitles.toJson())
            addP4(source.toString())
        }
    }

    /**
     * 求职者在期望工作地点页面点击Save
     *
     * [source] 来源：1，LinkedIn快速完善流程；2，F1修改期望页；3，F3修改期望页
     */
    fun preferenceLocationSave(location: List<String>, source: Int) {
        PointHelper.reportPoint("preference-location-save") {
            addP(location.toJson())
            addP4(source.toString())
        }
    }

    /**
     * 求职者在期望薪资页面点击Save
     *
     * [salaryType] 1，per year；2，per month；3，per hour
     * [source] 来源：1，LinkedIn快速完善流程；2，F1修改期望页；3，F3修改期望页
     */
    fun preferenceSalarySave(salaryType: Int, minSalary: Int, source: Int) {
        PointHelper.reportPoint("preference-salary-save") {
            addP(salaryType.toString())
            addP2(minSalary.toString())
            addP4(source.toString())
        }
    }

    /**
     * 求职者首善流程选择job title后，点击next按钮
     * @param title 记录选择的job title
     */
    fun firstCompleteTitleNext(list: List<JobCategory>) {
        PointHelper.reportPoint("firstcomplete-title-next") {
            addP(list.map { items -> items.name }.toJson())
        }
    }

    /**
     * 求职者首善流程选择location后，点击next按钮
     * @param location 记录选择的location
     */
    fun firstCompleteLocationNext(list: List<CityBean>) {
        PointHelper.reportPoint("firstcomplete-location-next") {
            addP(list.map { items -> items.code }.toJson())
        }
    }

    /**
     * 求职者首善流程选择salary后，点击next按钮
     * @param salaryType salary type：1，per year；2，per month；3，per hour
     * @param minSalary 记录填写的minimum salary
     */
    fun firstCompleteSalaryNext(salaryType: Long, minSalary: String) {
        PointHelper.reportPoint("firstcomplete-salary-next") {
            addP(salaryType.toString())
            addP2(minSalary)
        }
    }

    /**
     * 求职者首善流程附加信息页面，点击相应按钮
     * @param type 点击按钮：1，next；2，skip
     * @param locationType 记录勾选的location type
     * @param employmentType 记录勾选的employment type
     */
    fun firstCompleteAddtionalNext(type: Int, locationType: String, employmentType: String) {
        PointHelper.reportPoint("firstcomplete-addtional-next") {
            addP(type.toString())
            addP2(locationType)
            addP3(employmentType)
        }
    }

    /**
     * 求职者首善流程选择行业信息页面，点击相应按钮
     * @param type 点击按钮：1，Let's start；2，skip
     * @param industry 记录勾选的industry标签
     */
    fun firstCompleteIndustryNext(type: Int, industry: String) {
        PointHelper.reportPoint("firstcomplete-industry-next") {
            addP(type.toString())
            addP2(industry)
        }
    }

    //用户成功上传并保存头像
    fun photoUpload() {
        PointHelper.reportPoint("photo-upload")
    }

    /**
     * quick complete页面展示时上报
     * @param resumeAnalysisType 来源：1，开聊；2，引导完善卡片；3，Profile
     */
    fun quickCompleteShow(resumeAnalysisType: @ResumeAnalysisType String) {
        PointHelper.reportPoint("basicinfo-quickcomplete-show") {
            addP3(
                when (resumeAnalysisType) {
                    ResumeAnalysisType.FIRST_CHAT -> "1"
                    ResumeAnalysisType.GEEK_PROFILE_GUIDE -> "2"
                    ResumeAnalysisType.PROFILE_UPDATE -> "3"
                    ResumeAnalysisType.GEEK_FIRST_COMPLETE -> "4"
                    else -> ""
                }
            )
        }
    }

    /**
     * quick complete页面选择三种方式
     * @param clickType:方式：1，上传简历；2，Linked In url；3，manually
     * @param resumeAnalysisType 来源：1，开聊；2，引导完善卡片；3，Profile
     */
    fun quickCompletePath(clickType: Int, resumeAnalysisType: @ResumeAnalysisType String) {
        PointHelper.reportPoint("basicinfo-quickcomplete-path") {
            addP(clickType.toString())
            addP3(
                when (resumeAnalysisType) {
                    ResumeAnalysisType.FIRST_CHAT -> "1"
                    ResumeAnalysisType.GEEK_PROFILE_GUIDE -> "2"
                    ResumeAnalysisType.PROFILE_UPDATE -> "3"
                    ResumeAnalysisType.GEEK_FIRST_COMPLETE -> "4"
                    else -> ""
                }
            )
        }
    }

    /**
     * 附件简历预览页面展示时上报
     */
    fun quickCompleteFileResumeShow(resumeAnalysisType: @ResumeAnalysisType String) {
        PointHelper.reportPoint("basicinfo-cvpreview") {
            addP3(
                when (resumeAnalysisType) {
                    ResumeAnalysisType.FIRST_CHAT -> "1"
                    ResumeAnalysisType.GEEK_PROFILE_GUIDE -> "2"
                    ResumeAnalysisType.PROFILE_UPDATE -> "3"
                    ResumeAnalysisType.GEEK_FIRST_COMPLETE -> "4"
                    else -> ""
                }
            )
        }
    }

    /**
     * 附件简历预览页面点击update按钮 / Linkedin url输入页面点击update按钮
     */
    fun quickCompleteResumeUpdate(
        isFileResume: Boolean,
        resumeAnalysisType: @ResumeAnalysisType String
    ) {
        PointHelper.reportPoint(if (isFileResume) "basicinfo-cvpreview-update" else "basicinfo-linkedin-update") {
            addP3(
                when (resumeAnalysisType) {
                    ResumeAnalysisType.FIRST_CHAT -> "1"
                    ResumeAnalysisType.GEEK_PROFILE_GUIDE -> "2"
                    ResumeAnalysisType.PROFILE_UPDATE -> "3"
                    ResumeAnalysisType.GEEK_FIRST_COMPLETE -> "4"
                    else -> ""
                }
            )
        }
    }

    /**
     * 进入开聊信息确认页面
     */
    fun guidancePageShow(type: Int, count: Int, source: Int) {
        PointHelper.reportPoint("basicinfo-require") {
            addP(type.toString())
            addP2(count.toString())
            addP3(source.toString())
        }

    }

    /**
     * 快速完善的期望和Profile信息预览页点击add/edit按钮
     *
     * [position] 点击字段位置：1，Role；2，Location；3，Salary
     * [buttonType] 点击按钮：1，add；2，edit
     */
    fun jobPreferenceClick(position: Int, buttonType: Int) {
        PointHelper.reportPoint("firstcomplete-preference-click") {
            addP(position.toString())
            addP2(buttonType.toString())
        }
    }

    /**
     * 点击开聊信息确认页面按钮
     * @param position 点击字段位置：1，name；2，work experience；3，education
     * @param buttonType 点击按钮：点击按钮：1，add；2，edit
     * @param source 来源：1，开聊；2，引导完善卡片；3，完善流程；4，LinkedIn快速完善流程
     */
    fun guidancePageClick(position: Int, buttonType: Int, source: Int) {
        PointHelper.reportPoint("basicinfo-require-click") {
            addP(position.toString())
            addP2(buttonType.toString())
            addP3(source.toString())
        }
    }

    fun guidanceQuickComplete(source: Int) {
        PointHelper.reportPoint("basicinfo-quickcomplete-click") {
            addP3(source.toString())
        }
    }
    fun guidanceAllCompleted(source: Int) {
        PointHelper.reportPoint("basicinfo-allset-message") {
            addP3(source.toString())
        }
    }

    /**
     * 进入各个字段添加信息页面时上报
     * @param position 点击字段位置：1，name；2，work experience；3，education
     * @param source 来源：1，开聊；2，引导完善卡片；3，完善流程；4，LinkedIn快速完善流程
     */
    fun guidanceEditPageShow(position: Int, source: String) {
        PointHelper.reportPoint("basicinfo-require-edit") {
            addP(position.toString())
            addP3(source)
        }
    }

    /**
     * 进入各个字段添加信息页面时上报
     * @param position 点击字段位置：1，name；2，work experience；3，education
     * @param buttonType 点击按钮：1，save；2，save and add more
     * @param source 来源：1，开聊；2，引导完善卡片；3，完善流程；4，LinkedIn快速完善流程
     */
    fun guidanceEditPageClick(position: Int, buttonType: Int, source: String) {
        PointHelper.reportPoint("basicinfo-require-button-click") {
            addP(position.toString())
            addP2(buttonType.toString())
            addP3(source)
        }
    }

    /**
     * 进入各个字段添加信息页面时上报
     * @param position 点击字段位置：1，name；2，work experience；3，education
     * @param source 来源：1，开聊；2，引导完善卡片；3，完善流程
     */
    fun guidanceEditFlowPageShow(position: Int, source: String) {
        PointHelper.reportPoint("basicinfo-manually-show") {
            addP(position.toString())
            addP3(source)
        }
    }

    /**
     * 各个字段添加信息页面点击
     * @param position 点击字段位置：1，name；2，work experience；3，education
     * @param buttonType 点击按钮：1，next；2，back
     * @param source 来源：1，开聊；2，引导完善卡片；3，完善流程
     */
    fun guidanceEditFlowPageButtonClick(position: Int,buttonType: Int, source: String) {
        PointHelper.reportPoint("basicinfo-manually-button-click") {
            addP(position.toString())
            addP2(buttonType.toString())
            addP3(source)
        }
    }

    /**
     * @param fillSource 填写来源：0其他， 1简历解析 ，2领英， 3 手动填写
     * @param source 来源：1，开聊；2，引导完善卡片；3，完善流程
     */
    fun guidancePageAllSetShow(fillSource:String,source:String){
        PointHelper.reportPoint("basicinfo-allset-show"){
            addP(fillSource)
            addP3(source)
        }
    }

    /**
     * @param clickType 来源：点击效果：1，落地公司主页；2，落地职位详情页
     *
     */
    fun geekSuggestCompanyCardClick(onlineJobCount:Int,companyId:String,clickType:Int) {
        PointHelper.reportPoint("detail-company"){
            addEncryptP(GeekUserInfoService.geekUserInfo.value?.expectId?:"")
            addP2(onlineJobCount.toString())
            addEncryptP3(companyId)
            addP4(clickType.toString())
        }
    }

    /**
     * 用户在公司主页点击"Notify Recruiter"按钮
     */
    fun geekNotifyRecruiter(companyId:String) {
        PointHelper.reportPoint("company-notify-recruiter") {
            addEncryptP(companyId)
            addP2("1") //登录态：0，未登录；1，已登录
        }
    }

    /**
     * 用户在公司主页点击子Tab按钮
     */
    fun geekCompanyClick(companyId:String, type:Int, jobCount:Int?) {
        PointHelper.reportPoint("company-page-click") {
            addEncryptP(companyId)
            addP2(type.toString())  //子Tab：1，About；2，Jobs
            jobCount?.let {
                addP3(jobCount.toString())  //点击Jobs Tab时记录当前在线职位数，无职位传0
            }
        }
    }

    fun geekCompanyWebsiteVisit() {
        PointHelper.reportPoint("company-website-visit")
    }

    fun geekCopyJobLink(jobId:String, url:String) {
        PointHelper.reportPoint("copy-link-click") {
            addEncryptP(jobId)
            addP2(url)
        }
    }

    fun geekJdPageClick(jobId:String, type:String) {
        PointHelper.reportPoint("jd-page-click") {
            addEncryptP(jobId)
            addP2(type) //按钮：1，Message；2，收藏；3，举报
            addP3("1") //点击时的登录态：0，未登录；1，已登录
        }
    }

    /**
     * @param type:1，boss头像；2，牛人头像；3，公司logo；
     * @param source: 场景来源：1，职位详情页；2，简历详情页；3，聊天窗；4，职位模板详情页；5，公司详情
     */
    fun geekAvatarClick(type:Int, source:Int, userId:String, companyId: String) {
        PointHelper.reportPoint("avatar-click") {
            addP(type.toString())
            addP2(source.toString())
            addEncryptP3(userId)
            addEncryptP4(companyId)
        }
    }

    fun geekImprovementGuideShow(improveCount: Int) {
        PointHelper.reportPoint("improvement-guide-show") {
            addP2(improveCount.toString())
        }
    }

    fun geekImprovementGuideClick(improveCount: Int) {
        PointHelper.reportPoint("improvement-guide-click") {
            addP("1")
            addP2(improveCount.toString())
        }
    }

    fun geekImprovementItemClick(type: Int) {
        val strategy = when (type) {
            GeekListExtendType.JS_GUIDE_NAME -> 1
            GeekListExtendType.JS_GUIDE_LOCATION -> 2
            GeekListExtendType.JS_GUIDE_AVATAR -> 3
            GeekListExtendType.JS_GUIDE_WORK_EXP -> 4
            GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP -> 5
            GeekListExtendType.JS_GUIDE_SKILL -> 6
            GeekListExtendType.JS_GUIDE_IMPROVEMENT -> 7
            else -> 0
        }
        if (strategy > 0) {
            PointHelper.reportPoint("improvement-page-click") {
                addP(strategy.toString())
            }
        }
    }

    fun geekImprovementThirdpartyUpload() {
        PointHelper.reportPoint("improvement-page-update")
    }

    fun geekProfilePreviewClick() {
        PointHelper.reportPoint("profile-preview")
    }

    /**
     * [tab] 点击的Tab：1，Profile；2，List Card
     */
    fun geekPreviewProfileTabClick(tab: Int) {
        PointHelper.reportPoint("profile-preview-switch") {
            addP(tab.toString())
        }
    }

    /**
     * @param method 解析方式：1，resume；2，Linkedin
     * @param profile 解析出的内容：1
     */
    fun quickCompleteResultPreview(method: Int, source: String, profile: GeekProfileBean, linkedinUrl: String){
        PointHelper.reportPoint("quickcomplete-result-preview") {
            addP(method.toString())
            addP2(buildPreviewSource(source))
            addP3(profile.buildParseResultList())
            if (linkedinUrl.isNotEmpty()) {
                addP4(linkedinUrl)
            }
        }
    }

    private fun GeekProfileBean.buildParseResultList():String{
        val list = mutableListOf<Int>()
        if(!this.baseInfo?.firstName.isNullOrBlank() ||this.baseInfo?.lastName.isNullOrBlank()){
            list.add(1)
        }
        if(!this.workExpList.isNullOrEmpty()){
            list.add(2)
        }
        if(!this.eduExpList.isNullOrEmpty()){
            list.add(3)
        }
        return list.joinToString(",")
    }

    private fun buildPreviewSource(source:String):String{
        return when(source){
            ResumeAnalysisType.FIRST_CHAT-> "1"
            ResumeAnalysisType.GEEK_PROFILE_GUIDE->"2"
            else->""
        }
    }


    /**
     * @param method 解析方式：1，resume；2，Linkedin
     * @param source
     * @param profile 解析出的内容
     */
    fun quickCompleteResultUpdate(method:Int,source:String,profile: GeekProfileBean){
        PointHelper.reportPoint("quickcomplete-result-update") {
            addP(method.toString())
            addP2(buildPreviewSource(source))
            addP3(profile.buildParseResultList())
        }
    }

    /**
     * C端加入人才池
     */
    fun joinTalentPoolClick(hasComplete:Boolean, companyId: String) {
        PointHelper.reportPoint("join-talentpool-click") {
            addP(if(UserProvider.isLogin()) "1" else "0")
            addP2(if(hasComplete) "1" else "0")
            addEncryptP3(companyId)
        }
    }

    /**
     * 用户成功加入Talent Pool时上报
     * @param source:操作来源：1，company列表；2，公司主页；3，已加入列表
     */
    fun joinTalentPoolSuccess(source:String, companyId: String) {
        PointHelper.reportPoint("join-talentpool"){
            addP(source)
            addEncryptP3(companyId)
        }
    }
    /**
     * 用户成功离开Talent Pool时上报
     * @param source:操作来源：1，company列表；2，公司主页；3，已加入列表
     */
    fun exitTalentPoolSuccess(source:String, companyId: String) {
        PointHelper.reportPoint("leave-talentpool"){
            addP(source)
            addEncryptP3(companyId)
        }
    }

    /**
     * 用户成功添加或修改保存留言时上报
     * @param type:1，新增留言；2，修改保存
     */
    fun commentTalentPool(type:String, comment:String, companyId: String) {
        PointHelper.reportPoint("comment-talentpool"){
            addP(type)
            addP2(comment)
            addEncryptP3(companyId)
        }
    }

    /**
     * 招聘者点击F3个人中心的Settings按钮时上报
     */
    fun f3TabSettings() {
        PointHelper.reportPoint("f3tab-settings")
    }

    /**
     * Sponsorship卡片展示时上报
     */
    fun sponsorCardShow() {
        PointHelper.reportPoint("sponsor-card-show")
    }

    /**
     * 点击Sponsorship卡片的按钮时上报
     * @param buttonType 按钮：1, Yes; 2, No; 3, 叉号
     */
    fun sponsorCardClick(buttonType: Int) {
        PointHelper.reportPoint("sponsor-card-click") {
            addP(buttonType.toString())
        }
    }

    /**
     * 点击职位详情页Visa Sponsor的小问号时上报
     * @param sponsorType 展示的Sponsor标签：1, Visa sponsored; 2, No visa sponsorship; 3, Visa sponsored likely
     */
    fun jobDetailVisaClick(sponsorType: Int) {
        PointHelper.reportPoint("jobdetail-visa-click") {
            addP2(sponsorType.toString())
        }
    }
}