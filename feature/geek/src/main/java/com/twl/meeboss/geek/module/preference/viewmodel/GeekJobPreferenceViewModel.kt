package com.twl.meeboss.geek.module.preference.viewmodel


import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.components.dialog.CommonDataType
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.eventbus.sendBooleanLiveEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.from.GeekFromData
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.SimpleOptionBean
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.core.network.handleDefaultError
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.GeekEventBusKey
import com.twl.meeboss.geek.repos.GeekRepository
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class GeekJobPreferenceViewModel @Inject constructor(
    private val repos: GeekRepository
) : BaseMviViewModel<GeekJobPreferenceUiState, GeekJobPreferenceUiIntent>() {
    val saveSuccess:MutableLiveData<Boolean> = MutableLiveData()
    private var expId: String = ""
    private var from: String = ""
    private var initialJobTitles: List<HighlightBean> = emptyList()
    private var initialCities: List<CityBean> = emptyList()
    private var initialVisaSponsorship: Int = 0

    fun setFrom(from: String){
        this.from = from
    }
    override fun initUiState(): GeekJobPreferenceUiState = GeekJobPreferenceUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is GeekJobPreferenceUiIntent.GetJobPreferenceData ->{
                getJobPreferenceData()
            }
            is GeekJobPreferenceUiIntent.OnJobTitleChange -> {
                onJobTitleChange(intent.data)
            }
            is GeekJobPreferenceUiIntent.OnCityChange -> {
                onCityChange(intent.data)
            }
            is GeekJobPreferenceUiIntent.OnSalaryChange -> {
                onSalaryChange(intent.minSalary, intent.salaryOptionBean, intent.salaryUnit, intent.salaryUnitDesc)
            }
            is GeekJobPreferenceUiIntent.SetSingleOptionByType -> {
                setSingleOptionByType(intent.type, intent.readyToWork)
            }
            is GeekJobPreferenceUiIntent.OnReadyToWorkClear -> {
                onReadyToWorkClear()
            }
            is GeekJobPreferenceUiIntent.SetSelectListByType -> {
                setSelectListByType(intent.type, intent.selectValue)
            }
            is GeekJobPreferenceUiIntent.Save -> {
                save()
            }
            is GeekJobPreferenceUiIntent.DeleteReadyToWork -> {
                onReadyToWorkClear()
            }

            is GeekJobPreferenceUiIntent.DeleteVisaSponsorship -> {
                sendUiState {
                    copy(visaSponsorship = 0)
                }
            }
            else->{

            }

        }
    }

    private fun getJobPreferenceData(){
        requestData(
            enableLoadState = true,
            request = {
                repos.getExpectation()
            },
            success = {
                it?.let { data ->
                    initialJobTitles = data.jobTitles
                    initialCities = data.cities
                    initialVisaSponsorship = data.visaSponsorship
                    sendUiState {
                        copy(
                            jobTitles = data.jobTitles,
                            cities = data.cities,
                            minSalary = data.minSalary,
                            salaryType = data.salaryType,
                            salaryTypeDesc = data.salaryTypeDesc,
                            salaryUnit = data.salaryUnit,
                            salaryUnitDesc = data.salaryUnitDesc,
                            locationTypes = data.locationTypes,
                            jobTypes = data.jobTypes,
                            expLevels = data.expLevels,
                            readyToWork = data.readyToWork,
                            readyToWorkDesc = data.readyToWorkDesc,
                            visaSponsorship = data.visaSponsorship
                        )
                    }
                    expId = data.id
                    if(from == GeekFromData.GEEK_F1){
                        GeekPointReporter.f1tabJobPreferClick(expId)
                    } else if(from == GeekFromData.GEEK_MY){
                        GeekPointReporter.f3tabMenuClick(2, expId)
                    }
                }
                      
            },
            fail = {
                it.handleDefaultError()
            }
        )
    }

    fun save() {
        val uiState = uiStateFlow.value
        showLoadingDialog(true)
        requestData(
            enableLoadState = false,
            request = {
                repos.updateExpectation(
                    jobTitles = (uiState.jobTitles.map {
                        SimpleOptionBean(
                            it.parentCode,
                            it.code,
                            it.name
                        )
                    }).toJson(),
                    cityCodes = (uiState.cities.map { it.code }).toJson(),
                    minSalary = uiState.minSalary.toString(),
                    salaryType = uiState.salaryType.toString(),
                    locationTypes = (uiState.locationTypes.map { it.code }).toJson(),
                    jobTypes = (uiState.jobTypes.map { it.code }).toJson(),
                    expLevels = (uiState.expLevels.map { it.code }).toJson(),
                    salaryUnit = uiState.salaryUnit.toString().takeIf { it.isNotBlank() }?: LocalManager.getSalaryUnitParamValue().toString(),
                    readyToWork = uiState.readyToWork?.toString(),
                    visaSponsorship = uiState.visaSponsorship.toString()
                )
            },
            success = {
                dismissLoadingDialog()
                if (initialJobTitles != uiState.jobTitles || initialCities != uiState.cities) {
                    sendBooleanLiveEvent(GeekEventBusKey.SAVE_JOB_PREFERENCE_INFECT_F1, true)
                }
                if (initialVisaSponsorship == 0 && (uiState.visaSponsorship == 1 || uiState.visaSponsorship == 2)) {
                    sendBooleanLiveEvent(GeekEventBusKey.VISA_SPONSORSHIP_COMPLETED, true)
                }
                val newExpId = it?.id ?: ""
                launcherOnMain { saveSuccess.value = true }
                GeekPointReporter.jobPreferSave(uiState.canSave, expId, newExpId)
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )
    }

    private fun onJobTitleChange(data: ArrayList<HighlightBean>) {
        sendUiState {
            copy(
                jobTitles = data,
                canSave = true
            )
        }
    }

    private fun onCityChange(data: ArrayList<CityBean>) {
        sendUiState {
            copy(
                cities = data,
                canSave = true
            )
        }
    }

    private fun onSalaryChange(
        minSalary:Long,
        salaryOptionBean: OptionBean,
        salaryUnit:Long,
        salaryUnitDesc:String?,
    ) {
        sendUiState { 
            copy(
                minSalary = minSalary,
                salaryType = salaryOptionBean.code,
                salaryTypeDesc = salaryOptionBean.name,
                salaryUnit = salaryUnit,
                salaryUnitDesc = salaryUnitDesc?:"",
                canSave = true,
            )
        }
    }

    private fun setSingleOptionByType(type: CommonDataType, optionBean:OptionBean) {
        when(type){
            CommonDataType.LEVEL_OF_ROLE->{
                sendUiState {
                    copy(
                        expLevels = arrayListOf(optionBean),
                        canSave = true
                    )
                }
            }
            CommonDataType.READY_TO_WORK->{
                sendUiState {
                    copy(
                        readyToWork = optionBean.code,
                        readyToWorkDesc = optionBean.name,
                        canSave = true
                    )
                }
            }
            CommonDataType.GEEK_VISA_SPONSORSHIP -> {
                sendUiState {
                    copy(
                        visaSponsorship = optionBean.code.toInt(),
                        canSave = true
                    )
                }
            }
            else->{

            }
        }

    }

    private fun onReadyToWorkClear() {
        sendUiState { 
            copy(
                readyToWork = 0L,
                readyToWorkDesc = "",
                canSave = true
            )
        }
    }

    private fun setSelectListByType(type: CommonDataType, selectValue: List<OptionBean>) {
        //region 判断是否修改,修改过允许按钮点击
        val uiState = uiStateFlow.value
        var tempCanSave = uiState.canSave
        val comparisonList = when(type) {
            CommonDataType.WORKPLACE_TYPE -> uiState.locationTypes
            CommonDataType.EMPLOYMENT_TYPE -> uiState.jobTypes
            CommonDataType.WORK_EXPERIENCE -> uiState.expLevels
            else -> listOf()
        }
        var originMatchCount = 0
        comparisonList.forEach outside@{ originItem ->
            selectValue.forEach inside@{ selectItem ->
                if (originItem.code == selectItem.code) {
                    originMatchCount++
                    return@inside
                }
            }
        }
        if (originMatchCount != selectValue.size) {
            tempCanSave = true
        }
        //endregion

        when (type) {
            CommonDataType.WORKPLACE_TYPE -> {
                sendUiState { 
                    copy(
                        locationTypes = selectValue,
                        canSave = tempCanSave
                    )
                }
            }

            CommonDataType.EMPLOYMENT_TYPE -> {
                sendUiState {
                    copy(
                        jobTypes = selectValue,
                        canSave = tempCanSave
                    )
                }
            }

            CommonDataType.WORK_EXPERIENCE -> {
                sendUiState {
                    copy(
                        expLevels = selectValue,
                        canSave = tempCanSave
                    )
                }
            }

            else -> {

            }
        }
    }

    fun getSelectListByType(type: CommonDataType): List<OptionBean> {
        val uiState = uiStateFlow.value
        return when (type) {
            CommonDataType.WORKPLACE_TYPE -> {
                uiState.locationTypes
            }
            CommonDataType.EMPLOYMENT_TYPE -> {
                uiState.jobTypes
            }
            else -> {
                emptyList()
            }
        }
    }

    fun getSelectValueByType(type: CommonDataType): String {
        return when (type) {
            CommonDataType.READY_TO_WORK -> uiStateFlow.value.readyToWorkDesc
            CommonDataType.LEVEL_OF_ROLE -> uiStateFlow.value.expLevels.lastOrNull()?.name ?: ""
            CommonDataType.GEEK_VISA_SPONSORSHIP -> uiStateFlow.value.visaSponsorship.getVisaSponsorshipDesc()
            else -> ""
        }
    }
}

data class GeekJobPreferenceUiState(
    val jobTitles: List<HighlightBean> = listOf(),
    val cities: List<CityBean> = listOf(),
    val minSalary: Long = 0L,
    val salaryType: Long = 0L,
    val salaryTypeDesc: String = "",
    val salaryUnit: Long = 0L,
    val salaryUnitDesc: String = "",
    val readyToWork: Long? = null,
    val readyToWorkDesc: String = "",
    val locationTypes: List<OptionBean> = listOf(),
    val visaSponsorship: Int = 0,
    val jobTypes: List<OptionBean> = listOf(),
    val expLevels: List<OptionBean> = listOf(),
    val canSave: Boolean = false,
) : IUiState

sealed class GeekJobPreferenceUiIntent : IUiIntent {
    data object GetJobPreferenceData : GeekJobPreferenceUiIntent()

    data class OnJobTitleChange(val data: ArrayList<HighlightBean>) : GeekJobPreferenceUiIntent()
    data class OnCityChange(val data: ArrayList<CityBean>) : GeekJobPreferenceUiIntent()
    data class OnSalaryChange(
        val minSalary: Long,
        val salaryOptionBean: OptionBean,
        val salaryUnit: Long,
        val salaryUnitDesc: String?
    ) : GeekJobPreferenceUiIntent()

    data class SetSingleOptionByType(val type: CommonDataType, val readyToWork: OptionBean) : GeekJobPreferenceUiIntent()
    data object OnReadyToWorkClear : GeekJobPreferenceUiIntent()
    data class SetSelectListByType(val type: CommonDataType, val selectValue: List<OptionBean>) : GeekJobPreferenceUiIntent()
    data object Save : GeekJobPreferenceUiIntent()
    data object DeleteReadyToWork : GeekJobPreferenceUiIntent()
    data object DeleteVisaSponsorship: GeekJobPreferenceUiIntent()
}

fun Int.getVisaSponsorshipDesc() = when(this){
    1 -> R.string.job_seeker_job_prep_visa_sponsor_1.toResourceString()
    2 -> R.string.job_seeker_job_prep_visa_sponsor_2.toResourceString()
    else -> ""
}