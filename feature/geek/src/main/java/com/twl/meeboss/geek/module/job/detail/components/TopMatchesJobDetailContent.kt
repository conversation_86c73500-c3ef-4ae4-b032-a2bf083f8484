package com.twl.meeboss.geek.module.job.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.twl.meeboss.chat.export.component.HighlyMatchReasonCard
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.preview.TopMatchesPreviewParameterProvider
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedDetailModel
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.export.facade.JobDetailClickInteract

/**
 * @author: musa on 2025/05/12
 * @e-mail: <EMAIL>
 * @desc: 高度匹配职位详情
 */

@Preview
@Composable
fun TopMatchesJobDetailContent(
    @PreviewParameter(TopMatchesPreviewParameterProvider::class)
    topMatchesJobDetail: Pair<TopHighlyMatchedDetailModel, JobDetailResult>,
    modifier: Modifier = Modifier
        .background(Color.White)
        .padding(horizontal = 16.dp)
        .padding(top = 22.dp),
    onShowCompanyDetail: () -> Unit = {},
    jobDetailClickInteract: JobDetailClickInteract? = null,
) {
    val reason = topMatchesJobDetail.first.reason
    val jobDetail = topMatchesJobDetail.second
    Column(
        modifier = modifier
    ) {
        jobDetail.jobDetailJobInfo?.let {
            JobDetailHeaderCard(it)
        }
        jobDetail.jobDetailBossInfo?.let {
            JobDetailBossCard(it) {}
        }

        reason.takeIf { it.isNotEmpty() }?.let {
            HighlyMatchReasonCard(
                title = stringResource(R.string.jobseeker_top_matches_instruction_ai_title),
                reason = it,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            ) {
                jobDetailClickInteract?.onClickHighlyMatchedReason()
            }
        }

        jobDetail.jobDetailJobInfo?.jobDesc?.takeIf { it.isNotBlank() }?.let {
            JobDetailDescCard(it, jobDetail.jobDetailJobInfo?.jobDescStyle)
        }
        jobDetail.jobDetailJobInfo?.let {
            JobDetailMoreCard(it)
        }
        jobDetail.tab?.run {
            JobDetailBeginnerGuideCard(modifier = Modifier.padding(bottom = 28.dp), bean = this) {
                jobDetailClickInteract?.onCloseTabClick(it)
            }
        }
        jobDetail.jobDetailCompanyInfo?.let {
            JobDetailCompanyCard(it) {
                onShowCompanyDetail()
            }
        }
    }
}