package com.twl.meeboss.geek.module.job.recommend

import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toastErrorIfPresent
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPage
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.geek.R
import com.twl.meeboss.geek.model.bean.GeekF1ListBean
import com.twl.meeboss.geek.model.bean.GeekListExtendBean
import com.twl.meeboss.geek.model.bean.GeekListExtendType
import com.twl.meeboss.geek.model.constants.GeekF1CardType
import com.twl.meeboss.geek.repos.GeekJobRepository
import com.twl.meeboss.geek.repos.GeekRepository
import com.twl.meeboss.geek.utils.GeekPointReporter
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年07月11日 11:50
 * @description:
 */
@HiltViewModel
class GeekF1ViewModel @Inject constructor(
    private val repos: GeekJobRepository,
    private val geekRepos: GeekRepository
) : BaseMviViewModel<GeekJobUiState, GeekJobUiIntent>() {

    private var pageIndex = getDefaultPage()

    init {
        getJobList(true)
    }

    override fun initUiState() = GeekJobUiState(
        listState = XRefreshListState.getDefault<GeekF1ListBean>().copy(refreshText = R.string.common_position_updated.toResourceString(),
            loadFinishText = R.string.boss_candidates_list_load_finish_tips.toResourceString())
    )

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is GeekJobUiIntent.RefreshPage -> {
                getJobList(intent.refresh)
            }

            is GeekJobUiIntent.CloseCard->{
                onCloseCard(intent.bean)
            }

            is GeekJobUiIntent.ChangeImproveCount -> {
                if (intent.count < 2) {
                    removeGeekBeginnerGuideCard(GeekListExtendType.JS_GUIDE_IMPROVEMENT)
                }
            }

            is GeekJobUiIntent.DeleteBeginnerWorkExpCard -> {
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_FLOW)
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_ADD_WORK_EXP)
                removeGeekBeginnerGuideCard(GeekListExtendType.JS_GUIDE_WORK_EXP)
                removeGeekBeginnerGuideCard(GeekListExtendType.JS_GUIDE_IMPROVE_WORK_EXP)
            }

            is GeekJobUiIntent.DeleteBeginnerEduExpCard -> {
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_FLOW)
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_ADD_EDU_EXP)
            }

            is GeekJobUiIntent.DeleteBeginnerNameCard -> {
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_FLOW)
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_ADD_NAME)
                removeGeekBeginnerGuideCard(GeekListExtendType.JS_GUIDE_NAME)
            }
            is GeekJobUiIntent.DeleteEmailVerifiedCard -> {
                removeGeekBeginnerGuideCard(GeekListExtendType.VERIFY_USER_EMAIL)
            }
            is GeekJobUiIntent.DeleteSkillCard -> {
                removeGeekBeginnerGuideCard(GeekListExtendType.JS_GUIDE_SKILL)
            }
            is GeekJobUiIntent.DeleteAllNotFillCard -> {
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_FLOW)
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_ADD_WORK_EXP)
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_ADD_EDU_EXP)
                removeGeekBeginnerGuideCard(GeekListExtendType.BEGINNER_GUIDE_ADD_NAME)
            }
            is GeekJobUiIntent.Retry -> {
                sendUiState { copy(listState = listState.loading()) }
                getJobList(true)
            }

            is GeekJobUiIntent.TwoButtonLeftClick -> {
                intent.bean.extend?.run {
                    when (type) {
                        GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS -> {
                            setVisaSponsorshipStatus(intent.bean, 1)
                            GeekPointReporter.sponsorCardClick(1)
                        }
                        else -> {}
                    }
                }
            }

            is GeekJobUiIntent.TwoButtonRightClick -> {
                intent.bean.extend?.run {
                    when (type) {
                        GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS -> {
                            setVisaSponsorshipStatus(intent.bean, 2)
                            GeekPointReporter.sponsorCardClick(2)
                        }
                        else -> {}
                    }
                }
            }

            is GeekJobUiIntent.DeleteVisaSponsorshipCard -> {
                removeCard(GeekF1CardType.TWO_BUTTON_CARD, GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS)
            }
        }
    }

    private fun setVisaSponsorshipStatus(bean: GeekF1ListBean, buttonType: Int) {
        launcherOnIO {
            bean.extend?.run {
                if (setSettingButtonClick(this, buttonType)) {
                    sendUiState {
                        val list = listState.list.toMutableList()
                        list.remove(bean)
                        copy(listState = listState.copy(list = list))
                    }
                    T.ss(R.string.job_seeker_visa_sponsor_update_hint)
                }
            }
        }
    }

    private fun onCloseCard(bean: GeekF1ListBean) {
        when (bean.cardType) {
            GeekF1CardType.BEGINNER_GUIDE_CARD, GeekF1CardType.TWO_BUTTON_CARD -> {
                bean.extend?.run {
                    onDeleteBeginnerWorkExpCard(this, bean)
                }
            }

            else -> {

            }
        }
        if (bean.extend?.type == GeekListExtendType.JS_GUIDE_VISA_SPONSORSHIP_STATUS) {
            GeekPointReporter.sponsorCardClick(3)
        }
    }

    private fun removeGeekBeginnerGuideCard(@GeekListExtendType type:Int){
        val list = uiStateFlow.value.listState.list.toMutableList()
        list.removeAll {
            it.cardType == GeekF1CardType.BEGINNER_GUIDE_CARD && it.extend?.type == type
        }
        sendUiState {
            copy(listState = listState.copy(list = list))
        }
    }

    private fun removeCard(@GeekF1CardType cardType: Int, @GeekListExtendType type:Int){
        val list = uiStateFlow.value.listState.list.toMutableList()
        list.removeAll {
            it.cardType == cardType && it.extend?.type == type
        }
        sendUiState {
            copy(listState = listState.copy(list = list))
        }
    }

    //删除并上报
    private fun onDeleteBeginnerWorkExpCard(extend: GeekListExtendBean, f1ListBean: GeekF1ListBean) {
        requestData(
            request = {
                geekRepos.geekSettingCloseTab(extend.scene, extend.type)
            },
            success = {
                it?.let { _ ->
                    sendUiState {
                        val list = listState.list.toMutableList()
                        list.remove(f1ListBean)
                        copy(listState = listState.copy(list = list))
                    }
                }
            },
            fail = {
                XLog.error(TAG, it.message)
            }
        )

    }

    private fun getJobList(isRefresh: Boolean) {
        if (isRefresh) {
            pageIndex = getDefaultPage()
        }
        requestData(
            enableLoadState = false,
            request = {
                repos.getRecommendJobList(pageIndex, getDefaultPageSize())
            },
            success = { result ->
                result?.run {
                    if (isRefresh) {
                        sendUiState { copy(listState = listState.refreshSuccess(result.getInsertedList(), result.hasMore)) }
                    } else {
                        sendUiState { copy(listState = listState.loadMoreSuccess(result.getInsertedList(), result.hasMore)) }
                    }
                    pageIndex++
                }
            },
            fail = {
                if (isRefresh) {
                    sendUiState { copy(listState = listState.refreshFail()) }
                } else {
                    sendUiState { copy(listState = listState.loadMoreFail()) }
                }
                XLog.error(TAG, it.message)
            }
        )
    }

    private suspend fun setSettingButtonClick(extend: GeekListExtendBean, buttonType: Int): Boolean {
        val result = geekRepos.setSettingButtonClick(extend.scene, extend.type, buttonType)
        result.toastErrorIfPresent()
        return result.isSuccess
    }
}

data class GeekJobUiState(
    val listState: XRefreshListState<GeekF1ListBean>,
) : IUiState

sealed class GeekJobUiIntent : IUiIntent {
    data class RefreshPage(val refresh: Boolean) : GeekJobUiIntent()
    data class CloseCard(val bean: GeekF1ListBean) : GeekJobUiIntent()
    data class ChangeImproveCount(val count: Int): GeekJobUiIntent()
    data class TwoButtonLeftClick(val bean: GeekF1ListBean) : GeekJobUiIntent()
    data class TwoButtonRightClick(val bean: GeekF1ListBean) : GeekJobUiIntent()

    data object DeleteAllNotFillCard : GeekJobUiIntent()
    data object DeleteSkillCard : GeekJobUiIntent()
    data object DeleteBeginnerWorkExpCard : GeekJobUiIntent()
    data object DeleteBeginnerEduExpCard : GeekJobUiIntent()
    data object DeleteBeginnerNameCard : GeekJobUiIntent()
    data object DeleteEmailVerifiedCard : GeekJobUiIntent()
    data object DeleteVisaSponsorshipCard : GeekJobUiIntent()
    data object Retry : GeekJobUiIntent()
}