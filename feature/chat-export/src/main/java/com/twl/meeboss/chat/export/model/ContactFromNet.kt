package com.twl.meeboss.chat.export.model

import androidx.compose.runtime.mutableStateOf
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.chat.export.constant.FriendStatus
import java.util.UUID

data class ContactFromNet(
    val blockUrl:String = "",
    val greetingSettings:Boolean = false,
    val securityId: String? = "",
    val friendId: String = "",
    val friendIdentity: Int = UserConstants.NONE_IDENTITY,
    val baseInfo: ContactFromNetBaseInfo? = null,
    val jobseekerInfo: ContactFromNetGeekInfo? = null,
    val bossInfo: ContactFromNetBossInfo? = null,
    val systemInfo: ContactFormSystemInfo? = null,
    val jobInfo: ContactFormNetJobInfo? = null,
    val userStatus: Int = 0,
    val userStatusPrompt: String? = "",
    @FriendStatus
    val friendStatus: Int = 0,
    val topMatched: Boolean = false,
    val favored: Boolean = false,
    val blacklist:Boolean = false,
    val visible: Boolean = true,
    val friendSource:Int = 0,
    val unreadCount: Int = 0,
    val updateTime: Long = 0L,
    val friendLastReadMsgId: Long = 0,
    val lastDeletedMsgId: Long = 0,
    val jobId:String? = "",
    val expectId:String? = ""
) : BaseEntity {
    @Transient
    var isShow = mutableStateOf(true)//联系人是否显示
    fun getUniqueKey(): String {
        return UUID.randomUUID().toString()
    }
}

data class ContactFromNetBaseInfo(
    val firstName: String? = "",
    val lastName: String? = "",
    val avatar: String? = "",
    val tinyAvatar: String? = ""
) : BaseEntity

data class ContactFromNetGeekInfo(
    val schoolName: String? = "",
    val companyName: String? = "",
    val jobTitle: String? = "",
    val highestEduLevelDesc: String? = "", //对方C独有
    val locationDesc: String? = "",
) : BaseEntity

data class ContactFromNetBossInfo(
    val companyName: String? = "",
    val bossPosition: String? = ""
) : BaseEntity

data class ContactFormNetJobInfo(
    val jobTitle: String? = "",
    val salaryDesc: String? = ""
) : BaseEntity

data class ContactFormSystemInfo(
    val displayName: String? = "",
    val teamName: String? = ""
) : BaseEntity