package com.twl.meeboss.chat.export.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.chat.export.constant.FriendStatus
import com.twl.meeboss.chat.export.constant.GeneralMsgType
import com.twl.meeboss.chat.export.constant.LocalMessageType
import com.twl.meeboss.chat.export.constant.MessageStatus
import com.twl.meeboss.chat.export.constant.UserStatus

@Entity(tableName = "Conversation", indices = [Index(value = ["friendId"], name = "conversation_friendId")])
data class Conversation(
    @PrimaryKey
    val friendId: String = "",
    val securityId: String? = "",
    val friendIdentity: Int = UserConstants.NONE_IDENTITY,
    val firstName: String? = "",
    val lastName: String? = "",
    val avatar: String? = "",
    val tinyAvatar: String? = "",
    val companyName: String? = "",
    val bossPosition: String? = "", //对方B独有
    val jobTitle: String? = "",
    val salaryDesc: String? = "", //对方B独有
    val schoolName: String? = "",
    val highestEduLevelDesc: String? = "", //对方C独有
    val locationDesc: String? = "",
    val userStatus: Int = 0,//0正常 1封禁 3已注销（注销=删除）
    val userStatusPrompt: String? = "",
    @FriendStatus
    val friendStatus: Int = 0,//1 对方未回复 ,2 new greeting  ,3 双聊
    val starred: Boolean = false,
    val friendSource: Int = 0,//1 主动  2 被动
    val topMatched: Boolean = false,
    val visible: Boolean = true,
    val blacklist: Boolean = true,
    val lastDeletedMsgId: Long = 0,
    val friendLastReadMsgId: Long = 0,
    val lastMid: Long = 0,
    val lastCMid: Long = 0,
    val lastMessageContent: String = "",
    @GeneralMsgType
    val lastMessageBodyType: Int = 0,
    @LocalMessageType
    val lastMessageLocalType: Int = 0,
    val lastMessageStatus: Int = MessageStatus.SEND,
    val lastMessageIsMine: Boolean = false, //最后一条是否我的消息
    val draft: String = "",
    val updateTime: Long = 0L,
    val unreadCount: Int = 0,
    val jobId:String? = "",
    val expectId:String? = ""
) : BaseEntity{

    fun isUserDeleted() = userStatus == UserStatus.DELETE

    fun isSystemUser() = friendIdentity == UserConstants.SYSTEM_IDENTITY
}