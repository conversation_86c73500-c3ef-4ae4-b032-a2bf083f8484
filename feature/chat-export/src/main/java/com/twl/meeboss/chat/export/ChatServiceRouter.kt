package com.twl.meeboss.chat.export

import androidx.activity.ComponentActivity
import androidx.compose.runtime.Composable
import androidx.lifecycle.MutableLiveData
import com.sankuai.waimai.router.Router
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.export.api.IChatClient
import com.twl.meeboss.chat.export.model.AddFriendResult
import com.twl.meeboss.chat.export.model.StartChatResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * @author: 冯智健
 * @date: 2024年07月12日 10:14
 * @description:
 */
object ChatServiceRouter : IChatService {

    override fun startChat(context: ComponentActivity, friendId: String, friendIdentity:Int, source: ChatSource, callback: (StartChatResult) -> Unit) {
        Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.startChat(context, friendId, friendIdentity, source, callback)
    }

    override fun checkStartChat(context: ComponentActivity, securityId: String,
                                friendId: String,
                                friendIdentity: Int,
                                isFriend: Boolean,
                                source: ChatSource,
                                addFriendSource: Int,
                                callback: (StartChatResult) -> Unit) {
        Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.checkStartChat(context, securityId, friendId, friendIdentity, isFriend, source, addFriendSource, callback)
    }

    override fun addFriendAndStartChat(context: ComponentActivity, securityId: String, source: ChatSource, addFriendSource: Int, callback: (StartChatResult) -> Unit) {
        Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.addFriendAndStartChat(context, securityId, source, addFriendSource, callback)
    }

    override fun getChatClient(): IChatClient? {
        return Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.getChatClient()
    }

    override suspend fun checkAndAddFriendOnly(context: ComponentActivity, securityId: String, friendId: String, isFriend: Boolean, source: ChatSource, addFriendSource: Int): AddFriendResult {
        return Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.checkAndAddFriendOnly(context, securityId, friendId, isFriend, source, addFriendSource) ?: AddFriendResult()
    }

    override suspend fun addFriendOnly(context: ComponentActivity, securityId: String, source: ChatSource, showLoading: Boolean, addFriendSource: Int): AddFriendResult {
        return Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.addFriendOnly(context, securityId, source, showLoading, addFriendSource)  ?: AddFriendResult()
    }

    @Composable
    override fun MessagesScreen(tabs: List<Int>, pageContent: @Composable (Int) -> Unit) {
        Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.MessagesScreen(tabs, pageContent)
    }

    @Composable
    override fun BossConversationPage() {
        Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.BossConversationPage()
    }

    @Composable
    override fun GeekConversationPage(isGeekComplete: Boolean) {
        Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.GeekConversationPage(isGeekComplete)
    }

    override fun getUnreadCount(): Flow<Int> {
       return Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.getUnreadCount()?: flowOf(0)
    }

    override fun getJobsWithConversation(): Flow<Set<String>> {
        return Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.getJobsWithConversation()?: flowOf(emptySet())
    }

    override fun getSystemNotificationCount(): MutableLiveData<Int> {
        return Router.getService(IChatService::class.java, ChatRouterPath.CHAT_SERVICE)
            ?.getSystemNotificationCount()?: MutableLiveData()
    }
}