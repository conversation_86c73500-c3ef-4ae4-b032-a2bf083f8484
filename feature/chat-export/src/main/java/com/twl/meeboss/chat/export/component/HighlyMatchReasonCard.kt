package com.twl.meeboss.chat.export.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import com.twl.meeboss.chat.export.R
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.theme.COLOR_888888
import com.twl.meeboss.core.ui.theme.COLOR_FFF9C9
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.preview.TopMatchesPreviewParameterProvider
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedDetailModel

/**
 * @author: musa on 2025/05/12
 * @e-mail: <EMAIL>
 * @desc: 高度匹配原因
 */
@Composable
fun HighlyMatchReasonCard(
    title: String,
    reason: String,
    modifier: Modifier = Modifier,
    onTipClick: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .background(COLOR_FFF9C9, RoundedCornerShape(12.dp))
            .padding(horizontal = 12.dp, vertical = 14.dp)
            .fillMaxWidth()
    ) {
        Row(verticalAlignment = Alignment.CenterVertically){
            Image(
                painter = painterResource(R.drawable.chat_export_ic_highly_matched),
                contentDescription = "geek_ic_higly_matched",
                modifier = Modifier.size(28.dp)
            )

            Text(
                text = title,
                color = COLOR_222222,
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(horizontal = 4.dp)
            )
        }

        Text(
            text = reason,
            color = COLOR_484848,
            fontSize = 13.sp,
            lineHeight = 1.3.em,
            modifier = Modifier.padding(top = 8.dp)
        )

        Spacer(modifier = Modifier.padding(top = 9.dp))
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.noRippleClickable {
                onTipClick()
            }
        ) {
            Image(
                painter = painterResource(R.drawable.chat_export_talent_pool_more_guide),
                colorFilter = ColorFilter.tint(COLOR_888888),
                contentDescription = "geek_ic_higly_matched_arrow",
                modifier = Modifier.size(16.dp)
            )

            Text(
                text = stringResource(R.string.top_matches_instruction_powered_by_ai),
                color = COLOR_888888,
                fontSize = 12.sp,
                modifier = Modifier.padding(start = 6.dp)
            )
        }
    }
}

@Preview
@Composable
fun PreviewHighlyMatchReasonCard(
    @PreviewParameter(TopMatchesPreviewParameterProvider::class)
    topMatchesJobDetail: Pair<TopHighlyMatchedDetailModel, JobDetailResult>,
) {
    HighlyMatchReasonCard(title = stringResource(R.string.jobseeker_top_matches_instruction_ai_title), reason = topMatchesJobDetail.first.reason)
}