package com.twl.meeboss.chat.export

import androidx.activity.ComponentActivity
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.export.api.IChatClient
import com.twl.meeboss.chat.export.model.AddFriendResult
import com.twl.meeboss.chat.export.model.StartChatResult
import kotlinx.coroutines.flow.Flow

interface IChatService {
    /**
     * 开启聊天，用户会话列表
     */
    fun startChat(context: ComponentActivity, friendId: String, friendIdentity:Int, source: ChatSource, callback: (StartChatResult) -> Unit = {})

    /**
     * 开聊检查
     */
    fun checkStartChat(context: ComponentActivity,
                       securityId: String,
                       friendId: String,
                       friendIdentity: Int,
                       isFriend: Boolean,
                       source: ChatSource,
                       addFriendSource: Int = 0,
                       callback: (StartChatResult) -> Unit = {})

    /**
     * 检查并开启聊天,用于完善个人信息以后
     */
    fun addFriendAndStartChat(
        context: ComponentActivity,
        securityId: String,
        source: ChatSource,
        addFriendSource: Int,
        callback: (StartChatResult) -> Unit = {}
    )

    fun getChatClient(): IChatClient?

    /**
     * 仅仅开聊，不打开会话
     */
    suspend fun checkAndAddFriendOnly(
        context: ComponentActivity,
        securityId: String,
        friendId: String,
        isFriend: Boolean,
        source: ChatSource,
        addFriendSource: Int = 0,
    ):AddFriendResult

    suspend fun addFriendOnly(
        context: ComponentActivity,
        securityId: String,
        source: ChatSource,
        showLoading: Boolean = true,
        addFriendSource: Int = 0,
    ): AddFriendResult


    @Composable
    fun MessagesScreen(
        @StringRes tabs: List<Int>,
        pageContent: @Composable (Int) -> Unit,
    )

    /**
     * B 端会话列表页面
     */
    @Composable
    fun BossConversationPage()

    /**
     * C 端会话列表页面
     */
    @Composable
    fun GeekConversationPage(isGeekComplete: Boolean)

    fun getUnreadCount(): Flow<Int>

    fun getJobsWithConversation(): Flow<Set<String>>

    fun getSystemNotificationCount():MutableLiveData<Int>
}