package com.twl.meeboss.export_share.model


import androidx.annotation.Keep
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.common.CommonTypeBean

@Keep
data class CandidateResumeResult(
    val securityId: String? = null,
    val friended: Boolean? = null,
    val friendId: String = "",
    val favorite: Boolean? = null,
    val baseInfo: CandidateResumeBaseInfo? = null,
    val locationDesc: String? = null,
    val highestEduLevelDesc: String? = null,
    val expLevelDesc: String? = null,
    val companyName: String? = null,
    val jobTitle: String? = null,
    val jobId: String? = null,
    val topMatchReason: String? = null,
    val workExpList: List<CandidateResumeWorkExp?>? = null,
    val eduExpList: List<CandidateResumeEduExp?>? = null,
    val projectExpList: List<CandidateResumeProjectExp?>? = null,
    val languages: List<CandidateResumeLanguage?>? = null,
    val certificates: List<CandidateResumeCertificate?>? = null,
    val skills: List<CommonTypeBean?>? = null,
    val interested: Int = 0,
    val comment: String = ""
): BaseEntity{

    fun hasJoinedCompany():Boolean{
        return interested == DefaultValueConstants.GEEK_HAS_JOIN_TALENT_POOL_CODE
    }
}

@Keep
data class CandidateResumeBaseInfo(
    val userId:String? = null,
    val avatar: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val city: CandidateResumeCity? = CandidateResumeCity()
): BaseEntity

@Keep
data class CandidateResumeWorkExp(
    val id: String? = null,
    val jobCategoryCode: Long? = null,
    val jobTitle: String? = null,
    val companyId: String? = null,
    val companyName: String? = null,
    val companyLogo: String? = null,
    val startYear: Int? = null,
    val startMonth: Int? = null,
    val endYear: Int? = null,
    val endMonth: Int? = null,
    val description: String? = null,
    val descriptionHtml: String? = null,
    val descriptionMd: String? = null,
): BaseEntity

@Keep
data class CandidateResumeEduExp(
    val id: String? = null,
    val schoolCode: Long? = null,
    val schoolName: String? = null,
    val schoolLogo: String? = null,
    val eduLevel: Int? = null,
    val eduLevelDesc: String? = null,
    val majorCode: Long? = null,
    val majorName: String? = null,
    val startYear: Int? = null,
    val startMonth: Int? = null,
    val endYear: Int? = null,
    val endMonth: Int? = null,
    val description: String? = null,
    val descriptionHtml: String? = null,
    val descriptionMd: String? = null,
): BaseEntity

@Keep
data class CandidateResumeProjectExp(
    val id: String? = null,
    val projectName: String? = null,
    val jobCategoryCode: Long? = null,
    val jobTitle: String? = null,
    val startYear: Int? = null,
    val startMonth: Int? = null,
    val endYear: Int? = null,
    val endMonth: Int? = null,
    val description: String? = null,
    val descriptionHtml: String? = null,
    val descriptionMd: String? = null,
    val performance: String? = null,
    val performanceHtml: String? = null,
    val performanceMd: String? = null,
): BaseEntity

@Keep
data class CandidateResumeCertificate(
    val id: String? = null,
    val code: Long? = null,
    val name: String? = null,
    val expirationYear: Int? = null,
    val expirationMonth: Int? = null
): BaseEntity

@Keep
data class CandidateResumeCity(
    val code: Long? = null,
    val name: String? = null,
    val city: String? = null,
    val state: String? = null,
    val shortState: String? = null,
    val country: String? = null,
    val shortCountry: String? = null
): BaseEntity

@Keep
data class CandidateResumeLanguage(
    val id: String? = null,
    val code: Long? = null,
    val name: String? = null,
    val fluencyCode: Long? = null,
    val fluencyName: String? = null
): BaseEntity