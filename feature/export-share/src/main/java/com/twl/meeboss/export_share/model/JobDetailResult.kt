package com.twl.meeboss.export_share.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.common.CommonTypeBean
import com.twl.meeboss.base.model.geek.TabArgument

/**
 * @author: 冯智健
 * @date: 2024年07月05日 10:08
 * @description:
 */
@Keep
data class JobDetailResult(
    @Json(name = "securityId")
    val securityId: String = "",
    @Json(name = "friended")
    val friended: Boolean = false,
    @Json(name = "bizInfo")
    val jobDetailBizInfo: JobDetailBizInfo? = null,
    @Json(name = "jobInfo")
    val jobDetailJobInfo: JobDetailJobInfo? = null,
    @<PERSON>son(name = "jobBossInfo")
    val jobDetailBossInfo: JobDetailBossInfo? = null,
    @Json(name = "jobCompanyInfo")
    val jobDetailCompanyInfo: JobDetailCompanyInfo? = null,
    @Json(name = "tab")
    val tab: JobTabBean? = null,
    @Json(name = "linkedinProfileUrl")
    val linkedinProfileUrl: String? = null,
) : BaseEntity

@Keep
data class JobDetailBizInfo(
    val favoriteJob: Int? = null
) : BaseEntity

@Keep
data class JobDetailJobInfo(
    val jobId: String? = null,
    val securityId: String? = null, // v1.05.6添加，C端公司推荐列表的职位跳转jd用
    val jobTitle: String? = null,
    val jobCode: String? = null,
    val jobType: List<CommonTypeBean>? = null,
    val locationType: CommonTypeBean? = null,
    val salaryType: CommonTypeBean? = null,
    val salaryShortType: CommonTypeBean? = null,
    val salaryUnit: CommonTypeBean? = null,
    val minSalary: Long? = null,
    val maxSalary: Long? = null,
    val salaryDesc: String? = "",
    val salaryShortDesc: String? = "",
    val eduLevel: CommonTypeBean? = null,
    val expLevel: List<CommonTypeBean>? = null,
    val skills: List<CommonTypeBean>? = null,
    val jobDesc: String? = null,
    val jobDescStyle: String? = null, // 20240904 新增  职位描述，html样式
    val jobDescMd: String? = null, // 20240904 新增  职位描述，markdown样式
    val address: List<CommonTypeBean>? = null,
    val addTime: Long? = null,
    val updateTime: Long? = null,
    val jobStatus: Int? = null,
    val auditStatus: Int? = null,
    val languages: List<IndexBean> = listOf(),
    val shareLink: String? = null,
    val benefits: List<OptionBean> = listOf(), // 福利
    val visaSponsored: Int? = 0, //0:未设置 1:提供签证担保 2:不提供 3:可能提供
) : BaseEntity

@Keep
data class JobDetailCompanyInfo(
    val comId: String? = null,
    val name: String? = null,
    val shortName: String? = null,
    val logo: String? = null,
    val description: String? = null,
    val industryName: String? = null,
    val sizeType: CommonTypeBean? = null,
) : BaseEntity

@Keep
data class JobDetailBossInfo(
    val userId: String? = "",
    val avatar: String? = null,
    val name: String? = null,
    val bossPosition: String? = null,
    val bossIntroduce: String? = null,
) : BaseEntity

@Keep
data class JobTabBean(
    val type: Int = 0,
    val scene: Int = 0,
    val url: String = "",
    val index: Int = 0,
    val closeable: Boolean = false,
    val arguments: TabArgument? = null,
) : BaseEntity