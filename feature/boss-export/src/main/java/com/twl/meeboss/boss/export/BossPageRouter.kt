package com.twl.meeboss.boss.export

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.constants.BOSS_EDIT_JOB_DESCRIPTION
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN_1
import com.twl.meeboss.base.constants.BUNDLE_FROM
import com.twl.meeboss.base.constants.BUNDLE_INT
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION_HAS_GENERATED
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION_IS_APPLIED
import com.twl.meeboss.base.constants.BUNDLE_JOB_TITLE
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.manager.UrlListManager
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.boss.export.model.CandidatePageFrom
import com.twl.meeboss.boss.export.model.PostResultBean
import com.twl.meeboss.common.router.RouterHelper
import com.twl.meeboss.common.utils.URLUtils
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.utils.toResourceString

/**
 * @author: 冯智健
 * @date: 2024年07月05日 10:14
 * @description:
 */
object BossPageRouter {

    const val BUNDLE_JOB_STATUS = "jobStatus"
    const val BUNDLE_JOB_DENIED_REASON = "jobDeniedReason"
    const val BUNDLE_JOB_ID = "jobId"
    const val BUNDLE_POST_RESULT = "postResult"
    const val BUNDLE_EDIT_JOB_FROM = "editJobFrom"
    const val BUNDLE_EDIT_TITLE_ENABLE = "editTitleEnable"
    const val BUNDLE_SECURITY_ID = "securityId"
    const val BUNDLE_LINE_MARKER_TYPE = "lineMarkerType"
    const val BUNDLE_BOSS_REGISTER_HAS_BACK = "bossRegisterHasBack"
    const val BUNDLE_BOSS_REGISTER_HAS_MORE = "bossRegisterHasMore"
    const val BUNDLE_BOSS_BASE_INFO = "bossBaseInfo"
    const val BUNDLE_TAB_INDEX = "tabIndex"
    const val FROM_BOSS_F1_EMPTY = "FROM_BOSS_F1_EMPTY"
    const val EDIT_JOB_FROM_WEB_VIEW = "webview"
    const val TAG = "BossPageRouter"

    fun jumpToBossPostJobActivity(context: Context) {
        RouterHelper.startPage(context, BossRouterPath.BOSS_POST_JOB_PAGE)
    }

    fun singleTaskToBossPostJobActivity(context: Context,bundle: Bundle) {
        RouterHelper.startPage(
            context = context,
            path = BossRouterPath.BOSS_POST_JOB_PAGE,
            data = bundle,
            flag = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
    }

    fun jumpToBossEditJobActivity(context: Context, jobId: String = "", from: String = "") {
        RouterHelper.startPage(context, BossRouterPath.BOSS_EDIT_JOB_PAGE, Bundle().apply {
            putString(BUNDLE_JOB_ID, jobId)
            putString(BUNDLE_EDIT_JOB_FROM, from)
        })
    }

    fun jumpToBossEditJobPayActivity(context: Context) {
        RouterHelper.startPage(context, BossRouterPath.BOSS_EDIT_JOB_PAY_PAGE)
    }

    fun jumpToBossJobPreviewActivity(
        jobId: String,
        from: String? = null
    ) {
        UrlListManager.getUrlList {
            val jobDetailH5Url = it?.jobDetailH5Url?.takeIf { url -> url.isNotBlank() } ?: run {
                TLog.error(TAG, "jumpToBossJobPreviewActivity, jobDetailH5Url is blank")
                return@getUrlList
            }
            TLog.info(TAG, "jumpToBossJobPreviewActivity, jobDetailH5Url: $jobDetailH5Url")
            val source = if (from == FROM_BOSS_F1_EMPTY) 1 else 2
            val businessParams = URLUtils.buildBusinessParams(hashMapOf(
                "jobId" to jobId,
                "source" to "$source"
            ))
            ProtocolHelper.parseProtocol(jobDetailH5Url, businessParams)
        }
    }

    fun jumpToBossJobCloseActivity(jobId: String) {
        UrlListManager.getUrlList {
            val jobCloseReportH5Url =
                it?.jobCloseReportH5Url?.takeIf { url -> url.isNotBlank() } ?: run {
                    TLog.error(TAG, "jumpToBossJobCloseActivity, jobCloseReportH5Url is blank")
                    return@getUrlList
                }
            TLog.info(TAG, "jumpToBossJobCloseActivity, jobCloseReportH5Url: $jobCloseReportH5Url")
            val businessParams = URLUtils.buildBusinessParams(
                hashMapOf(
                    "jobId" to jobId
                )
            )
            ProtocolHelper.parseProtocol(jobCloseReportH5Url, businessParams)
        }
    }

    fun jumpToBossJobPostResultActivity(
        context: Context,
        jobStatus: @JobStatus Int = -1,
        jobId: String = "",
        rejectReason: String = "",
        editJobFrom: String = ""
    ) {
        jumpToBossJobPostResultActivity(context, listOf(PostResultBean(jobId, jobStatus, rejectReason)), editJobFrom)
    }

    fun jumpToBossJobPostResultActivity(context: Context, postResults: List<PostResultBean>, editJobFrom: String = "") {
        RouterHelper.startPage(context, BossRouterPath.BOSS_JOB_POST_RESULT_PAGE, Bundle().apply {
            putSerializable(BUNDLE_POST_RESULT, ArrayList(postResults))
            putString(BUNDLE_EDIT_JOB_FROM, editJobFrom)
        })
    }

    fun jumpToBossJobManagementActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_JOB_MANAGEMENT_PAGE)
        }

    fun jumpToBossJobUpdateResultActivity(context: Context) {
        RouterHelper.startPage(context, BossRouterPath.BOSS_JOB_UPDATE_RESULT_PAGE)
    }


        fun jumpToBossCandidateResumeActivity(context: Context, securityId: String, pageFrom: Int = CandidatePageFrom.OTHER, lineMarkerType: Int? = null) {
            val userInfo = BossServiceRouter.getBossUserInfo()?.value?.bossInfoUserVO
            if (!userInfo?.firstName.isNullOrEmpty() && !userInfo?.firstName.isNullOrEmpty()) {
                RouterHelper.startPage(context, BossRouterPath.BOSS_CANDIDATE_RESUME_PAGE, Bundle().apply {
                    putString(BUNDLE_SECURITY_ID, securityId)
                    putInt(BUNDLE_INT, pageFrom)
                    lineMarkerType?.let {
                        putInt(BUNDLE_LINE_MARKER_TYPE, it)
                    }
                })
            } else {
                context.showConfirmDialog(
                    title = R.string.boss_beginner_guide_add_name_in_detail_pop_up_title.toResourceString(),
                    content = R.string.boss_beginner_guide_add_name_in_detail_pop_up_content.toResourceString(),
                    confirmText = R.string.common_go.toResourceString(),
                    onConfirm = {
                        jumpToBossPersonalInfoActivity(context)
                    }
                )
            }
        }

        fun jumpToBossCompleteFirstJobActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_COMPLETE_FIRST_JOB_PAGE)
        }

        fun jumpToBossCompanyEditActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_COMPANY_EDIT_PAGE)
        }

        fun jumpToBossCompanyPreviewActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_COMPANY_PREVIEW_PAGE)
        }

        fun jumpToBossPersonalInfoActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_PERSONAL_INFO_PAGE)
        }

        fun jumpToBossMyCandidateActivity(context: Context, tabIndex: Int) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_MY_CANDIDATE_PAGE, Bundle().apply {
                putInt(BUNDLE_TAB_INDEX, tabIndex)
            })
        }

        fun jumpToBossCompleteChooseTypeActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_COMPLETE_CHOOSE_TYPE_PAGE)
        }

        fun jumpToBossGenerateJobDescriptionActivity(
            context: Context,
            requestCode: Int = BOSS_EDIT_JOB_DESCRIPTION,
            jobTitle: String,
            originJobDescription: String?,
            hasGenerated: Boolean = false, //是否是AI生成的描述 用于判断生成按钮是否可用
            isApplied: Boolean = false, //是否应用了AI生成的描述 用于绿条显示
            from: String = ""
        ) {
            RouterHelper.startPage(
                context,
                BossRouterPath.BOSS_COMPLETE_JOB_DESCRIPTION_PAGE,
                Bundle().apply {
                    putString(BUNDLE_JOB_DESCRIPTION, originJobDescription)
                    putString(BUNDLE_JOB_TITLE, jobTitle)
                    putBoolean(BUNDLE_JOB_DESCRIPTION_HAS_GENERATED, hasGenerated)
                    putBoolean(BUNDLE_JOB_DESCRIPTION_IS_APPLIED, isApplied)
                    putString(BUNDLE_FROM, from)
                },
                requestCode
            )

        }

        fun jumpToBossRegisterProfileActivity(context: Context, hasBack: Boolean = true, hasRightMore: Boolean = false, bundle: Bundle? = null) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_REGISTER_PROFILE_PAGE, (bundle ?: Bundle()).apply {
                putBoolean(BUNDLE_BOSS_REGISTER_HAS_BACK, hasBack)
                putBoolean(BUNDLE_BOSS_REGISTER_HAS_MORE, hasRightMore)
            })
        }

        fun jumpToBossRegisterCompanyPreviewActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_REGISTER_COMPANY_PREVIEW_PAGE)
        }

        fun jumpToBossRegisterCompanyInfoActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_REGISTER_COMPANY_INFO_PAGE)
        }

        fun jumpToBossRegisterJobTemplateActivity(context: Context, companyId: String?) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_REGISTER_JOB_TEMPLATE_PAGE, Bundle().apply {
                putString(BUNDLE_STRING, companyId)
            })
        }

        fun jumpToBossRegisterTemplateDetailActivity(
            context: Context,
            jobTemplateId: String,
            fromRegister: Boolean = true,
            hasFillItem: Boolean = true
        ) {
            RouterHelper.startPage(
                context,
                BossRouterPath.BOSS_REGISTER_TEMPLATE_DETAIL_PAGE,
                Bundle().apply {
                    putString(BUNDLE_STRING, jobTemplateId)
                    putBoolean(BUNDLE_BOOLEAN, fromRegister)
                    putBoolean(BUNDLE_BOOLEAN_1, hasFillItem)
                })
        }

        fun jumpToBossTalentPoolActivity(context: Context) {
            RouterHelper.startPage(context, BossRouterPath.BOSS_TALENT_POOL_PAGE)
        }
    }