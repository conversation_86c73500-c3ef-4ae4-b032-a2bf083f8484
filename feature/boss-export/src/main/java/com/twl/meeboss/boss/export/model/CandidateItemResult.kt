package com.twl.meeboss.boss.export.model

import androidx.annotation.Keep
import com.twl.meeboss.base.ktx.buildFullName
import com.twl.meeboss.base.model.BaseEntity

@Keep
data class CandidateItemResult(
    val securityId: String? = null,
    val active: String? = null,
    val baseInfo: CandidateBaseInfo? = null,
    val eduLevelDesc: String? = null,
    val locationDesc: String? = null,
    val desiredJobTitle: CandidateCommonDesc? = null,
    val desiredLocation: CandidateCommonDesc? = null,
    val aboutMe: String? = null,
    val topMatchReason: String? = null,
    val recommendReason: CandidateCommonDesc? = null,
    val jobTitle: CandidateJobTitle? = null,
    val workExpList: List<CandidateWorkExp?>? = null,
    val eduExp: CandidateEduExp? = null,
    val labels: List<String?>? = null,
    val viewJobTitle: CandidateCommonDesc? = null,
    val favorJobTitle: CandidateCommonDesc? = null,
    val friendId: String? = null,
    val friended: Boolean? = false,
    val groupBy: String? = null, //收藏列表分组用这个字段
    val interested: Int = 0,
    val comment: String? = null,
): BaseEntity {
    @Transient
    var isShow: Boolean = true//卡片是否显示
    @Transient
    val fullName = buildFullName(baseInfo?.firstName, baseInfo?.lastName)

    fun isInterested() = interested == 1
}

@Keep
data class CandidateBaseInfo(
    val avatar: String? = null,
    val firstName: String? = null,
    val lastName: String? = null
): BaseEntity

@Keep
data class CandidateCommonDesc(
    val tag: String? = null,
    val desc: String? = null
): BaseEntity

@Keep
data class CandidateEduExp(
    val schoolName: String? = null,
    val majorName: String? = null,
    val timeDesc: String? = null
): BaseEntity

@Keep
data class CandidateWorkExp(
    val companyName: String? = null,
    val jobTitle: String? = null,
    val timeDesc: String? = null
): BaseEntity

@Keep
data class CandidateJobTitle(
    val tag: String? = null,
    val desc: String? = null
): BaseEntity
