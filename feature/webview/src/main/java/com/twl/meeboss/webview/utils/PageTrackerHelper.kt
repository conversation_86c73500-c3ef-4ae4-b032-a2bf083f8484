package com.twl.meeboss.webview.utils

import android.app.Activity
import com.blankj.utilcode.util.ActivityUtils
import com.twl.meeboss.common.log.XLog

object PageTrackerHelper {

    private const val TAG = "PageTrackerHelper"

    /**
     * 获取页面Id
     * 规则: activity类名+ $ + 哈希值
     */
    @JvmStatic
    fun Activity.getPageId() = componentName.className + "$" +hashCode()

    /**
     * 页面栈出栈，包括targetId标记的页面
     * 逻辑是出栈，但不是从栈顶开始退出。为了让底部的页面先关闭，防止露出下方也应该关闭的页面
     *
     * @param keepSelf 是否保留targetId标记的页面
     */
    fun popToTargetId(targetId: String, keepSelf: Boolean) {
        var startPop = false
        // ActivityUtils.getActivityList()的表现是最后打开的activity是在list的首位
        // 因此这里使用 asReversed() 来进行反向迭代，而不会修改原始的 activityList
        val reversedActivityList = ActivityUtils.getActivityList().asReversed()
        XLog.info(TAG, "popToTargetId# targetId: $targetId keepSelf:$keepSelf \n 页面栈: ${reversedActivityList.joinToString(",\n") { it.getPageId() }}")
        reversedActivityList.forEach {
            if (it.getPageId() == targetId) {
                startPop = true
                if (keepSelf) {
                    return@forEach
                }
            }
            if (startPop) {
                it.finish()
            }
        }
    }

    /**
     * 关闭指定 targetId 匹配上的页面
     */
    fun closeTheTarget(vararg targetIds: String) {
        // ActivityUtils.getActivityList()的表现是最后打开的activity是在list的首位
        // 因此这里使用 asReversed() 来进行反向迭代，而不会修改原始的 activityList
        val reversedActivityList = ActivityUtils.getActivityList().asReversed()
        XLog.info(TAG, "closeTheTarget# targetIds: $targetIds \n 页面栈: ${reversedActivityList.joinToString(",\n") { it.getPageId() }}")
        reversedActivityList.forEach {
            if (targetIds.contains(it.getPageId())) {
                it.finish()
            }
        }
    }

}