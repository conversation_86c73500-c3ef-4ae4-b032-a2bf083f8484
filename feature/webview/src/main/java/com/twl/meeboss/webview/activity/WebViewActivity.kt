package com.twl.meeboss.webview.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.flow.GlobalFlowManager
import com.twl.meeboss.base.flow.WebViewFlowEvent
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.URLUtils
import com.twl.meeboss.core.ui.activity.FoundationActivity
import com.twl.meeboss.core.ui.fragment.LoadingDialogFragment
import com.twl.meeboss.core.ui.utils.fullScreenAndBlackText
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.useBlackTextStatusBar
import com.twl.meeboss.webview.IWebViewCallback
import com.twl.meeboss.webview.WebViewCommon
import com.twl.meeboss.webview.WebViewConstant
import com.twl.meeboss.webview.action.createWebActionFactory
import com.twl.meeboss.webview.databinding.ActivityWebviewBinding
import com.twl.meeboss.webview.jsbridge.JsBridgeRegistry
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch

class WebViewActivity : FoundationActivity() {

    companion object {
        fun intent(context: Context, url: String, params: Map<String, String> = emptyMap(), startNewTask: Boolean = false) {
            context.startActivity(Intent(context, WebViewActivity::class.java).apply {
                if (startNewTask) {
                    setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                putExtra(BUNDLE_STRING, url)
                params.forEach { (key, value) ->
                    putExtra(key,value)
                }
            })
        }

        fun intentWithBusinessParams(context: Context, url: String, params: HashMap<String, String> = hashMapOf(), startNewTask: Boolean = false) {
            context.startActivity(Intent(context, WebViewActivity::class.java).apply {
                if (startNewTask) {
                    setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                putExtra(BUNDLE_STRING, url)
                // H5通过getBusinessParams jsBridge方法进行调用
                putExtra(JsBridgeRegistry.WEB_BUSINESS_PARAMS, params)
            })
        }
    }

    private val binding by lazy {
        ActivityWebviewBinding.inflate(layoutInflater)
    }

    private val common by lazy {
        val common = WebViewCommon(this, binding.webView)
        common.start(createWebActionFactory(common), object :
            IWebViewCallback {
            override fun onProgressChanged(newProgress: Int) {
                binding.progress.isVisible = newProgress in 1..99
                binding.progress.progress = newProgress
            }

            override fun onReceivedTitle(title: String) {
                binding.tvTitle.text = title
            }

            override fun hideNavigationBar(isHidden: Boolean) {
                binding.rlTitleBar.isGone = isHidden
            }
        })
        common
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        useBlackTextStatusBar()
        intent?.let {
            preInit(it)
        }
        setContentView(binding.root)
        initView()
        common.syncCookie(common.mCurrentUrl)
        val loadingDialogFragment = LoadingDialogFragment.newInstance(true).also {
            it.showSafely(this)
        }
        common.webViewBridge.loadUrlOrDownload(common.mCurrentUrl){
            loadingDialogFragment.dismissAllowingStateLoss()
        }
        lifecycleScope.launch {
            GlobalFlowManager.stickyWebViewFlow.collect {
                when (it) {
                    WebViewFlowEvent.CLOSE_WEB_VIEW -> {
                        common.activity.finish()
                    }
                }

                // 需要重置粘性状态，以防止事件反复触发
                GlobalFlowManager.stickyWebViewFlow.resetReplayCache()
            }
        }
    }

    private fun preInit(intent: Intent) {
        common.parseIntent(intent)
        parseUrlParam(common.mCurrentUrl)
    }

    private fun parseUrlParam(url:String?) {
        url?:return
        val paramMap = URLUtils.parseUrlParam(url)
        if (paramMap.containsKey(WebViewConstant.PARAM_NO_HEAD) && !paramMap[WebViewConstant.PARAM_NO_HEAD].isNullOrEmpty()) {
            try {
                val noHead = Integer.parseInt(paramMap[WebViewConstant.PARAM_NO_HEAD] ?: "")
                if (noHead == WebViewConstant.CONFIG_NO_HEAD) {
                    binding.rlTitleBar.isGone = true
                    // 沉浸式
                    fullScreenAndBlackText()
                }
            } catch (e: Throwable) {
                XLog.error(TAG, "parseUrlParam noHead error:${url}")
            }
        }
    }

    private fun initView() {
        binding.ivBack.setOnClickListener {
            onBackClick(this)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        common.activityResult(requestCode, resultCode, data)
    }

    private fun onBackClick(context: Context) {
        binding.webView.isFocusable = false
        binding.webView.isFocusableInTouchMode = false
        if (!common.goBack()) {
            finish()
        }
    }

    override fun onBackPressed() {
        if (!common.isInterceptCloseEvent()) {
            super.onBackPressed()
        }
    }

}