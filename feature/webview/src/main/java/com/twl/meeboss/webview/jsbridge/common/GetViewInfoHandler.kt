package com.twl.meeboss.webview.jsbridge.common

import androidx.annotation.Keep
import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.twl.meeboss.webview.jsbridge.callbackSuccess

/**
 * 获取Web页面信息
 */
class GetViewInfoHandler(private val viewId: String): AbsBridgeHandler() {

    override fun getInvokeMethodName() = "getViewInfo"

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        function.callbackSuccess(ViewInfoBean(viewId))
    }
}

@Keep
data class ViewInfoBean(val viewId: String)