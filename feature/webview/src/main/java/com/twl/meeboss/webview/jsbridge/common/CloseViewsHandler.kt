package com.twl.meeboss.webview.jsbridge.common

import com.hpbr.directhires.module.localhtml.jsbridge.CallBackFunction
import com.hpbr.directhires.module.localhtml.jsbridge.AbsBridgeHandler
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.webview.jsbridge.callbackError
import com.twl.meeboss.webview.jsbridge.callbackSuccess
import com.twl.meeboss.webview.utils.PageTrackerHelper
import org.json.JSONArray
import org.json.JSONException

/**
 * 用于关闭页面
 */
class CloseViewsHandler: AbsBridgeHandler() {

    override fun getInvokeMethodName() = "closeViews"

    override fun handler(params: Map<String, String>, function: CallBackFunction) {
        params["targetViewId"]?.takeIf { it.isNotBlank() }?.let { targetViewId ->
            val keepSelf = params["keepSelf"].toBoolean()
            PageTrackerHelper.popToTargetId(targetViewId, keepSelf)
            function.callbackSuccess()
            return
        }

        params["closeViewIds"]?.takeIf { it.isNotBlank() }?.let {closeViewIdJson ->
            try {
                val jsonArray = JSONArray(closeViewIdJson)
                val closeViewIds = Array<String>(jsonArray.length()){ index ->
                    jsonArray.getString(index)
                }
                PageTrackerHelper.closeTheTarget(*closeViewIds)
                function.callbackSuccess()
            } catch (e: JSONException) {
                TLog.error(TAG, e, "closeViewIds 解析异常")
                function.callbackError(e)
            }
        }
    }
}
