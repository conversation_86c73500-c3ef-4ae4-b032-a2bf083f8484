package com.twl.meeboss.webview.jsbridge

import android.content.Intent
import androidx.fragment.app.FragmentActivity
import com.hpbr.directhires.module.localhtml.jsbridge.webview.WebViewBridge
import com.twl.meeboss.webview.jsbridge.common.CheckLogoutBridgeHandler
import com.twl.meeboss.webview.jsbridge.common.CloseViewsHandler
import com.twl.meeboss.webview.jsbridge.common.GetBusinessParamsBridgeHandler
import com.twl.meeboss.webview.jsbridge.common.GetViewInfoHandler
import com.twl.meeboss.webview.jsbridge.common.PointBridgeHandler
import com.twl.meeboss.webview.jsbridge.common.ShowToastBridgeHandler
import com.twl.meeboss.webview.jsbridge.feature.PreviewPicBridgeHandler
import com.twl.meeboss.webview.jsbridge.feature.ShowCopiedJobAlertBridgeHandler
import com.twl.meeboss.webview.jsbridge.feature.UpdateJobStatusBridgeHandler
import com.twl.meeboss.webview.utils.PageTrackerHelper.getPageId
import java.io.Serializable

/**
 * 注册jsBridge的操作都写在这个类中
 */
class JsBridgeRegistry(
    private val activity: FragmentActivity,
    private val webViewBridge: WebViewBridge
) {
    private var businessParams: Serializable? = null

    companion object {
        const val WEB_BUSINESS_PARAMS = "BUSINESS_PARAMS"
    }

    fun parseIntent(intent: Intent) {
        businessParams = intent.getSerializableExtra(WEB_BUSINESS_PARAMS)
    }

    fun registerJsBridge() {
        webViewBridge.registerHandler(PointBridgeHandler())
        webViewBridge.registerHandler(CheckLogoutBridgeHandler())
        webViewBridge.registerHandler(PreviewPicBridgeHandler(activity))
        webViewBridge.registerHandler(UpdateJobStatusBridgeHandler())
        webViewBridge.registerHandler(GetBusinessParamsBridgeHandler {
            businessParams
        })
        webViewBridge.registerHandler(ShowToastBridgeHandler())
        webViewBridge.registerHandler(ShowCopiedJobAlertBridgeHandler())
        webViewBridge.registerHandler(GetViewInfoHandler(activity.getPageId()))
        webViewBridge.registerHandler(CloseViewsHandler())
    }
}