package com.twl.meeboss.boss.api

import com.twl.meeboss.base.model.CommonListResult
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.PageList
import com.twl.meeboss.base.model.job.JobSimpleDetailResult
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.boss.model.JobDescriptionGenerateResult
import com.twl.meeboss.boss.model.JobParseResult
import com.twl.meeboss.boss.module.candidates.recommend.model.JobTitleResult
import com.twl.meeboss.boss.module.job.manager.model.JobCopyResult
import com.twl.meeboss.boss.module.job.manager.model.JobListItemResult
import com.twl.meeboss.boss.module.job.manager.model.JobSalaryConfigResult
import com.twl.meeboss.boss.module.job.model.EditJobAddResult
import com.twl.meeboss.boss.module.job.model.JobAddressSuggestResult
import com.twl.meeboss.boss.module.job.model.PostJobInfoResult
import com.twl.meeboss.boss.module.job.model.UpdateJobStatusResult
import com.twl.meeboss.core.network.HttpResult
import okhttp3.ResponseBody
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.Streaming

interface JobApi {

    /**
     * 获取发布职位字段顺序和内容、弹框选项
     */
    @GET("api/job/jobSelect")
    suspend fun jobSelect(): HttpResult<PostJobInfoResult>

    /**
     * 获取职位联想
     */
    @GET("api/job/suggest/jobTitle")
    suspend fun suggestJobTitle(@Query("text") text: String): HttpResult<CommonListResult<HighlightBean>>


    /**
     * 获取职位位置联想
     */
    @GET("api/job/suggest/address")
    suspend fun suggestJobAddress(@Query("address") jobTitle: String): HttpResult<JobAddressSuggestResult>

    /**
     * 获取职位技能联想和推荐
     */
    @GET("api/job/suggest/skill")
    suspend fun suggestJobSkill(@Query("jobTitle") jobTitle: String, @Query("text") text: String): HttpResult<CommonListResult<HighlightBean>>

    @FormUrlEncoded
    @POST("api/job/add")
    suspend fun addJob(
        @Field("jobTitle") jobTitle: String,
        @Field("jobType") jobType: String,
        @Field("jobCategoryCode") jobCategoryCode: String,
        @Field("locationType") locationType: String,
        @Field("jobLocation") jobLocation: String,
        @Field("salaryType") salaryType: String,
        @Field("minSalary") minSalary: String,
        @Field("maxSalary") maxSalary: String,
        @Field("eduLevel") eduLevel: String,
        @Field("expLevel") expLevel: String,
        @Field("jobDesc") jobDesc: String,
        @Field("jobDescStyle") jobDescStyle: String, //职位详情-带样式 20240904新增入参
        @Field("skills") skills: String,
        @Field("languages") languages: String,
        @Field("jobTemplateId") jobTemplateId: String? = null,
        @Field("salaryUnit") salaryUnit: String? = null,
        @Field("templateId") templateId: String? = null,
        @Field("benefits") benefits: String? = null,
        @Field("visaSponsored") visaSponsored: String? = null,
    ): HttpResult<EditJobAddResult>

    @FormUrlEncoded
    @POST("api/job/add")
    suspend fun updateJob(
        @Field("jobId") jobId: String,
        @Field("jobTitle") jobTitle: String,
        @Field("jobType") jobType: String,
        @Field("jobCategoryCode") jobCategoryCode: String,
        @Field("locationType") locationType: String,
        @Field("jobLocation") jobLocation: String,
        @Field("salaryType") salaryType: String,
        @Field("minSalary") minSalary: String,
        @Field("maxSalary") maxSalary: String,
        @Field("eduLevel") eduLevel: String,
        @Field("expLevel") expLevel: String,
        @Field("jobDesc") jobDesc: String,
        @Field("jobDescStyle") jobDescStyle: String, //职位详情-带样式 20240904新增入参
        @Field("skills") skills: String,
        @Field("salaryUnit") salaryUnit: Int,
        @Field("languages") languages: String,
        @Field("benefits") benefits: String? = null,
        @Field("visaSponsored") visaSponsored: String? = null,
    ): HttpResult<EditJobAddResult>

    /**
     * 获取职位列表
     */
    @GET("api/job/list")
    suspend fun getJobList(
        @Query("status") status: Int,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): HttpResult<PageList<JobListItemResult>>

    /**
     * 获取职位详情
     */
    @GET("api/job/detail")
    suspend fun getJobDetail(@Query("jobId") jobId: String): HttpResult<JobDetailResult>

    /**
     * 更新职位状态
     */
    @FormUrlEncoded
    @POST("api/job/updateStatus")
    suspend fun updateJobStatus(
        @Field("jobId") jobId: String,
        @Field("status") status: Int
    ): HttpResult<UpdateJobStatusResult>

    /**
     * 删除职位
     */
    @FormUrlEncoded
    @POST("api/job/delete")
    suspend fun deleteJob(@Field("jobId") jobId: String): HttpResult<Any?>

    /**
     * 职位识别
     * @param material 物料， 有可能是链接，也有可能是一段文字
     */
    @FormUrlEncoded
    @POST("api/job/auto-parse")
    suspend fun jobAutoParse(@Field("material") material: String): HttpResult<JobParseResult>

    /**
     * v0.15 AI生成职位
     * @param content 物料， 有可能是链接，也有可能是一段文字
     * @param type 生成参数类型 0:JobLink 1:JobDescription 2:keywords
     */
    @FormUrlEncoded
    @POST("api/job/generate")
    suspend fun jobAIGenerate(
        @Field("content") content: String,
        @Field("type") type: Int,
    ): HttpResult<JobParseResult>

    /**
     * 获取推荐的职位标题列表
     */
    @GET("api/job/listJobTitles")
    suspend fun getRecommendJobTitleList(): HttpResult<JobTitleResult>

    @GET("api/job/simple-detail")
    suspend fun getJobSimpleDetail(
        @Query("jobId") jobId: String
    ): HttpResult<JobSimpleDetailResult>

    /**
     * AI生成职位描述（流式输出）
     * Content-Type	text/event-stream
     */
    @GET("api/job/generate-description-flux")
    @Streaming
    fun generateJobDescriptionFlux(
        @Query("jobTitle") jobTitle: String
    ): ResponseBody

    /**
     * AI生成职位描述
     */
    @FormUrlEncoded
    @POST("api/job/generate-description")
    suspend fun generateJobDescription(
        @Field("jobTitle") jobTitle: String
    ): HttpResult<JobDescriptionGenerateResult>


    /**
     * 【1.06.5】B端从Talent Pool 查看职位列表
     */
    @GET("api/talent/job/list")
    suspend fun getTalentJobList(
        @Query("friendId") friendId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): HttpResult<PageList<JobListItemResult>>
    /**
     * 【1.11.11】拷贝职位
     */
    @GET("api/job/copy")
    suspend fun copyJob(
        @Query("jobId") jobId: String,
    ): HttpResult<JobCopyResult>

    /**
     * 【1.12.7】获取职位薪资配置
     */
    @GET("api/job/v1/getSalaryConfig")
    suspend fun getSalaryConfig(): HttpResult<JobSalaryConfigResult>
}