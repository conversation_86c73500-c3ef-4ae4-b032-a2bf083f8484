package com.twl.meeboss.boss.module.main

import android.os.Bundle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.rememberCoroutineScope
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.foundation.fragment.BaseMviFragment
import com.twl.meeboss.base.function.FunctionsManager
import com.twl.meeboss.base.main.MainViewModel
import com.twl.meeboss.boss.module.main.functions.UnReadCountFunction
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.chat.export.ChatServiceRouter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

@AndroidEntryPoint
class BossMainFragment : BaseMviFragment<BossMainViewModel>() {

    val vmParent: MainViewModel by activityViewModels()

    companion object {

        private const val BUNDLE_TAB_INDEX = "tabIndex"

        fun newInstance(tabIndex: Int) = BossMainFragment().apply {
            arguments = Bundle().apply {
                putInt(BUNDLE_TAB_INDEX, tabIndex)
            }
        }
    }

    private val functionsManager by lazy {
        FunctionsManager(this)
    }

    override val viewModel: BossMainViewModel by viewModels()
    override fun preInit() {
        viewModel.currentTabIndex.value = arguments?.getInt(BUNDLE_TAB_INDEX, 0) ?: 0
        functionsManager.addFunction(UnReadCountFunction())
    }

    override fun initData() {
        liveEventBusObserve(EventBusKey.CHANGE_MAIN_TAB) { index: Int ->
            viewModel.currentTabIndex.value = index
        }
    }

    @Composable
    override fun ComposeContent() {
        val vm: BossMainViewModel = viewModel()
        val currentIndex by vm.currentTabIndex.collectAsStateWithLifecycle()
        val unreadCount by ChatServiceRouter.getUnreadCount().collectAsStateWithLifecycle(0)
        val systemNotificationCount =
            ChatServiceRouter.getSystemNotificationCount().observeAsState(initial = 0).value
        val coroutineScope = rememberCoroutineScope()

        BossMainPage(
            currentIndex = currentIndex,
            unreadCount = unreadCount,
            systemNotificationCount = systemNotificationCount,
            onTabClick = {
                vm.currentTabIndex.value = it
                when (it) {
                    1 -> {
                        coroutineScope.launch {
                            val jobs = ChatServiceRouter.getJobsWithConversation().first()
                            BossPointReporter.bossMainF2TabClick(jobs.size)
                        }
                    }
                    2->{
                        BossPointReporter.bossMainJobTabClick()
                    }

                    3 -> {
                        BossPointReporter.bossMainF3TabClick()
                    }

                    else -> {}
                }
            },
            showLoading = { show, cancelable ->
                if (show) {
                    vmParent.showLoadingDialog(cancelable)
                } else {
                    vmParent.dismissLoadingDialog()
                }
            })
    }
}
