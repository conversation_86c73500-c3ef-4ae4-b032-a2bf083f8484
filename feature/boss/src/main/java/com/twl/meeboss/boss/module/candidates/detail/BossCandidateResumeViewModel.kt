package com.twl.meeboss.boss.module.candidates.detail

import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.http.HttpErrorCodes
import com.twl.meeboss.base.model.enumeration.CollectStatus
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.model.CandidatePageFrom
import com.twl.meeboss.boss.repos.BossCandidatesRepository
import com.twl.meeboss.common.exp.ApiException
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.export_share.model.CandidateResumeResult
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年07月18日 15:15
 * @description:
 */
@HiltViewModel
class BossCandidateResumeViewModel @Inject constructor(
    private val candidateRepo: BossCandidatesRepository
) : BaseMviViewModel<BossCandidateResumeUiState, BossCandidateResumeUiIntent>() {
    override fun initUiState() = BossCandidateResumeUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossCandidateResumeUiIntent.GetCandidateResume -> getCandidateResume(intent.securityId, intent.candidatePageFrom, intent.lineMarkerType)
            is BossCandidateResumeUiIntent.CollectCandidate -> collectCandidate(
                intent.securityId,
                intent.collected
            )
        }
    }

    private fun getCandidateResume(securityId: String, candidatePageFrom :Int, lineMarkerType: Int?) {
        requestData(
            enableLoadState = true,
            request = {
                if(candidatePageFrom == CandidatePageFrom.POOL){
                    candidateRepo.getTalentCandidateResume(securityId)
                }else{
                    candidateRepo.getCandidateResume(securityId, lineMarkerType)
                }
            },
            success = {
                it?.let { result ->
                    sendUiState {
                        copy(candidateResume = result)
                    }
                    sendUiState {
                        copy(collected = result.favorite == true)
                    }
                }
            },
            fail = {
                if (it is ApiException) {
                    when (it.code) {
                        HttpErrorCodes.GEEK_UNABLE -> {
                            T.ss(it.message)
                            sendUiState { copy(finishPage = true) }
                        }

                        else -> {

                        }
                    }
                }
                XLog.error(TAG, "getCandidateResume fail: ${it.message}")
            }
        )
    }

    private fun collectCandidate(securityId: String, collected: Boolean) {
        requestData(
            request = {
                candidateRepo.collectCandidate(
                    securityId,
                    if (collected) CollectStatus.TO_COLLECTED else CollectStatus.TO_UNCOLLECTED
                )
            },
            success = {
                sendStringLiveEvent(EventBusKey.BOSS_COLLECT_CANDIDATE_CHANGE, securityId)
                sendUiState {
                    copy(collected = collected)
                }
                if (collected) {
                    T.ss(R.string.star_action_confirmation_message)
                } else {
                    T.ss(R.string.unstar_action_confirmation_message)
                }
            },
            fail = {
                XLog.error(TAG, "collectCandidate fail: ${it.message}")
                T.ss(it.message)
            }
        )
    }

    fun hasShownTip(): Boolean {
        val key = "topMatchesTipsShown"
        return SpManager.getUserBoolean(key, false).also {
            if (!it) SpManager.putUserBoolean(key, true)
        }
    }
}

data class BossCandidateResumeUiState(
    val finishPage:Boolean = false,
    val collected: Boolean = false,
    val candidateResume: CandidateResumeResult? = null,
) : IUiState

sealed class BossCandidateResumeUiIntent : IUiIntent {
    data class GetCandidateResume(val securityId: String, val candidatePageFrom: Int, val lineMarkerType: Int?) : BossCandidateResumeUiIntent()
    data class CollectCandidate(val securityId: String, val collected: Boolean) :
        BossCandidateResumeUiIntent()
}