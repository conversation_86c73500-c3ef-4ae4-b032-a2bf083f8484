package com.twl.meeboss.boss.module.job.preview.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.media.pictureselector.previewImages
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.export_share.preview.JobDetailPreviewParameterProvider

/**
 * @author: 冯智健
 * @date: 2024年07月05日 18:23
 * @description:
 */
@Preview
@Composable
fun JobDetailContent(
    @PreviewParameter(JobDetailPreviewParameterProvider::class)
    jobDetail: JobDetailResult,
    isTemplate: Boolean = false,
    isPostJob: Boolean = false,
) {
    val context = LocalContext.current
    Column(
        modifier = Modifier
            .background(Color.White)
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        jobDetail.jobDetailJobInfo?.let {
            JobDetailHeaderCard(it)
        }
        if (!isPostJob) {
            jobDetail.jobDetailBossInfo?.let {
                JobDetailBossCard(it) {
                    if (context is FragmentActivity && !it.avatar.isNullOrBlank()) {
                        context.previewImages(listOf(it.avatar ?: ""))
                        BossPointReporter.bossAvatarClick(1,if (isTemplate) 4 else 1, it.userId?:"", jobDetail.jobDetailCompanyInfo?.comId?:"")
                    }
                }
            }
        }

        jobDetail.jobDetailJobInfo?.benefits?.takeIf { it.isNotEmpty() } ?.let {
            JobDetailBenefitsCard(benefits = it)
        }
        jobDetail.jobDetailJobInfo?.jobDesc?.takeIf { it.isNotBlank() }?.let {
            JobDetailDescCard(it, jobDetail.jobDetailJobInfo?.jobDescStyle)
        }
        jobDetail.jobDetailJobInfo?.let {
            JobDetailMoreCard(it, !isPostJob)
        }
        if (!isPostJob) {
            jobDetail.jobDetailCompanyInfo?.let {
                JobDetailCompanyCard(it)
            }
        }
    }
}