package com.twl.meeboss.boss.module.complete.account.model

import androidx.lifecycle.MutableLiveData
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.repos.BossJobRepository
import com.twl.meeboss.boss.repos.BossRepository
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.core.network.handleDefaultError
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class BossRegisterTemplateDetailViewModel @Inject constructor(
    private val bossRepos: BossRepository,
    private val jobRepos: BossJobRepository,
) : BaseMviViewModel<BossRegisterTemplateDetailUiState, BossRegisterTemplateDetailUiIntent>() {

    var fromRegister = true
    val postJobFinish: MutableLiveData<Boolean> = MutableLiveData()
    val jobResult: MutableLiveData<Pair<Int,String>> = MutableLiveData()
    override fun initUiState(): BossRegisterTemplateDetailUiState = BossRegisterTemplateDetailUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossRegisterTemplateDetailUiIntent.GetTemplateDetail ->{
                getTemplateDetail(intent.templateId)
            }
            is BossRegisterTemplateDetailUiIntent.AddJob ->{
                addJobByTemplate(intent.templateId)
            }
            else->{

            }

        }
    }

    private fun getTemplateDetail(templateId: String) {
        requestData(
            enableLoadState = true,
            request = {
                bossRepos.getJobTemplateDetail(templateId)
            },
            success = {
                if (it == null) {
                    sendLoadUiState(LoadState.Empty())
                } else {
                    sendLoadUiState(LoadState.Success)
                    sendUiState {
                        copy(
                            jobDetail = it
                        )
                    }
                }
            },
            fail = {
                it.handleDefaultError()
            }
        )
    }

    private fun addJobByTemplate(templateId: String) {
        val jobDetail = uiStateFlow.value.jobDetail
        showLoadingDialog()
        requestData(
            request = {
                jobRepos.addJob(
                    jobTitle = jobDetail?.jobDetailJobInfo?.jobTitle?:"",
                    jobType = jobDetail?.jobDetailJobInfo?.jobType?.map { it.code }?.toJson()?:"",
                    jobCode = jobDetail?.jobDetailJobInfo?.jobCode?:"",
                    workplaceType = jobDetail?.jobDetailJobInfo?.locationType?.code?.toString()?:"",
                    jobLocation = jobDetail?.jobDetailJobInfo?.address?.map { it.code }?.toJson()?:"",
                    salaryType = jobDetail?.jobDetailJobInfo?.salaryType?.code?.toString()?:"",
                    minSalary = jobDetail?.jobDetailJobInfo?.minSalary?.toString()?:"",
                    maxSalary = jobDetail?.jobDetailJobInfo?.maxSalary?.toString()?:"",
                    educationRequirement = jobDetail?.jobDetailJobInfo?.eduLevel?.code?.toString()?:"",
                    experienceRequirement = (jobDetail?.jobDetailJobInfo?.expLevel?.map { it.code } ?: mutableListOf<Long>()).toJson(),
                    skills = (jobDetail?.jobDetailJobInfo?.skills ?: mutableListOf<HighlightBean>()).toJson(),
                    jobDesc = jobDetail?.jobDetailJobInfo?.jobDesc ?: "",
                    jobDescStyle = jobDetail?.jobDetailJobInfo?.jobDescStyle ?: "",
                    languages = jobDetail?.jobDetailJobInfo?.languages?.map { it.code }?.toJson()?:"",
                    salaryUnit = jobDetail?.jobDetailJobInfo?.salaryUnit?.code?.toString(),
                    templateId = templateId,
                    benefits = jobDetail?.jobDetailJobInfo?.benefits?.map { it.code }?.toJson() ?: "",
                    visaSponsorship = jobDetail?.jobDetailJobInfo?.visaSponsored?.toString(),
                )
            },
            success = {
                it?.jobId?.apply {
                    onlineJob(this)
                }?:apply {
                    dismissLoadingDialog()
                }
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )
    }

    private fun onlineJob(jobId: String) {
        requestData(
            request = {
                jobRepos.updateJobStatus(jobId, JobStatus.OPENING)
            },
            success = {
                dismissLoadingDialog()
                if(fromRegister){
                    postJobFinish.value = true
                }else{
                    jobResult.value = JobStatus.OPENING to jobId
                }
            },
            fail = {
                dismissLoadingDialog()
                if(fromRegister){
                    postJobFinish.value = true
                }else{
                    it.handleDefaultError()
                }
            }
        )
    }

}

data class BossRegisterTemplateDetailUiState(
    val canSave: Boolean = false,
    val jobDetail: JobDetailResult? = null,
) : IUiState

sealed class BossRegisterTemplateDetailUiIntent : IUiIntent {
    data class GetTemplateDetail(val templateId:String) : BossRegisterTemplateDetailUiIntent()
    data class AddJob(val templateId:String) : BossRegisterTemplateDetailUiIntent()
}