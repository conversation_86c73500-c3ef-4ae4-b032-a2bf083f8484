package com.twl.meeboss.boss.module.candidates.detail

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.pagestate.XStatePage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.components.titlebar.XTitleBarRightMoreWithPopup
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.model.CandidatePageFrom
import com.twl.meeboss.boss.module.candidates.detail.components.CandidateResumeContent
import com.twl.meeboss.boss.module.candidates.detail.preview.CandidateResumePreviewData
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.theme.COLOR_00BC5E
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.export_share.model.CandidateResumeResult
import kotlinx.coroutines.delay

/**
 * @author: 冯智健
 * @date: 2024年07月19日 16:39
 * @description:
 */
@Composable
fun CandidateResumePage(
    loadState: LoadState = LoadState.Success,
    candidateResume: CandidateResumeResult? = null,
    collected: Boolean = false,
    candidatePageFrom: Int = CandidatePageFrom.POOL,
    showFloatingTip: Boolean = false,
    retryOnClick: () -> Unit = {},
    collectOnClick: () -> Unit = {},
    chatOnClick: () -> Unit = {},
    onMoreClick: (Rect) -> Unit = {},
    onTalentPoolCardClick: (Rect) -> Unit = {},
    onTopMatchesClick: () -> Unit = {},
    onFloatingTipDismiss: () -> Unit = {}
) {
    XTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            if (candidatePageFrom != CandidatePageFrom.POOL) {
                XTitleBar {
                    if (loadState == LoadState.Success) {
                        val vectorResource =
                            if (collected) R.drawable.ui_icon_star_filled
                            else R.drawable.ui_icon_star_outline
                        Row(
                            modifier = Modifier.fillMaxHeight(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                modifier = Modifier.size(24.dp).noRippleClickable { collectOnClick() },
                                imageVector = ImageVector.vectorResource(id = vectorResource),
                                contentDescription = null,
                                tint = if (collected) Color(0xFFF7Cb6A) else Color.Unspecified
                            )

                            XTitleBarRightMoreWithPopup(
                                onClick = onMoreClick,
                            )
                        }
                    }
                }
            } else {
                XTitleBar()
            }

            Box(modifier = Modifier.weight(1f)) {
                XStatePage(
                    loadState = loadState,
                    retryOnClick = retryOnClick
                ) {
                    Column {
                        Box(modifier = Modifier.weight(1f)) {
                            candidateResume?.apply {
                                CandidateResumeContent(
                                    candidateResume = this,
                                    onTalentPoolCardClick = onTalentPoolCardClick,
                                    onTopMatchesClick = onTopMatchesClick,
                                )
                            }
                        }
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            XCommonButton(
                                modifier = Modifier
                                    .padding(16.dp)
                                    .height(48.dp),
                                text = stringResource(id = R.string.common_message),
                                onClick = {
                                    // 点击按钮时隐藏浮层
                                    if (showFloatingTip) {
                                        onFloatingTipDismiss()
                                    }
                                    chatOnClick()
                                }
                            )
                            
                            // 浮层提示 - 显示在按钮上方
                            CubicAnimatingVisibility(
                                visible = showFloatingTip,
                                modifier = Modifier
                                    .align(Alignment.TopCenter)
                                    .offset(y = (-48).dp) // 向上偏移，留出间距
                            ) {
                                FloatingTip(
                                    text = stringResource(R.string.recommended_list_top_matches_message_guide),
                                    onDismiss = { onFloatingTipDismiss() }
                                )
                            }
                            
                            // 3秒自动隐藏逻辑
                            LaunchedEffect(showFloatingTip) {
                                if (showFloatingTip) {
                                    delay(3000)
                                    onFloatingTipDismiss()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun CubicAnimatingVisibility(
    visible: Boolean,
    modifier: Modifier = Modifier,
    content: @Composable AnimatedVisibilityScope.() -> Unit
) {
    val animationSpec = remember {
        tween<Float>(
            durationMillis = 300,
            easing = CubicBezierEasing(0.2f, 0.8f, 0.2f, 1f)
        )
    }

    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(
            animationSpec = animationSpec
        ) + scaleIn(
            animationSpec = animationSpec,
            initialScale = 0.8f,
            transformOrigin = TransformOrigin(0.5f, 1f)
        ),
        exit = fadeOut(
            animationSpec = animationSpec
        ) + scaleOut(
            animationSpec = animationSpec,
            targetScale = 0.8f,
            transformOrigin = TransformOrigin(0.5f, 1f)
        ),
        modifier = modifier
    ) {
        content()
    }
}

/**
 * 浮层提示组件
 * 显示绿色气泡样式的提示信息，带有指向下方的箭头
 */
@Composable
fun FloatingTip(
    text: String,
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {}
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 气泡内容
        Box(
            modifier = Modifier
                .background(
                    color = COLOR_00BC5E, // 绿色背景
                    shape = RoundedCornerShape(12.dp)
                )
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .noRippleClickable { 
                    // 点击浮层可以手动隐藏
                    onDismiss()
                }
        ) {
            Text(
                text = text,
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
        }
        
        // 箭头指向下方
        Canvas(
            modifier = Modifier
                .size(width = 12.dp, height = 8.dp)
        ) {
            val path = Path().apply {
                // 绘制向下的三角形箭头
                moveTo(0f, 0f) // 左上角
                lineTo(size.width * 0.5f, size.height) // 底部中心点
                lineTo(size.width, 0f) // 右上角
                close()
            }
            drawPath(
                path = path,
                color = COLOR_00BC5E
            )
        }
    }
}

@Preview
@Composable
fun PreviewFloatingTip() {
    FloatingTip(stringResource(R.string.recommended_list_top_matches_message_guide))
}

@Preview
@Composable
fun PreviewCandidateResumePage() {
    CandidateResumePage(
        loadState = LoadState.Success,
        candidateResume = CandidateResumePreviewData.candidateResume,
        showFloatingTip = true
    )
}