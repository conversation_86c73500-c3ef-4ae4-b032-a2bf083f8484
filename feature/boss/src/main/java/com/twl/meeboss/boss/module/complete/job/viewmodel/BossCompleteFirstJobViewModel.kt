package com.twl.meeboss.boss.module.complete.job.viewmodel

import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.boss.R
import com.twl.meeboss.export_share.model.JobDetailResult
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.model.JobParseResult
import com.twl.meeboss.boss.module.complete.ai.BossCompleteChooseType
import com.twl.meeboss.boss.module.job.ktx.toBossEditData
import com.twl.meeboss.boss.module.job.model.EditJobLocalData
import com.twl.meeboss.boss.module.job.model.JobEditItem
import com.twl.meeboss.boss.module.job.model.JobEditItemFactory
import com.twl.meeboss.boss.repos.BossJobRepository
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.handleDefaultError
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class BossCompleteFirstJobViewModel @Inject constructor(
    private val jobRepo: BossJobRepository
) : BaseMviViewModel<BossCompleteFirstJobUiState, BossCompleteFirstJobUiIntent>() {
    var generateWithoutChange:Boolean = false //生成完之后是否有编辑

    val showLoading: MutableLiveData<Boolean> = MutableLiveData()
    val postJobFinish: MutableLiveData<Boolean> = MutableLiveData()

    override fun initUiState(): BossCompleteFirstJobUiState = BossCompleteFirstJobUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossCompleteFirstJobUiIntent.Init -> {
                var chooseType = intent.chooseType
                val jobParseResult = intent.jobParseResult
                if (jobParseResult?.jobDetail == null) {
                    chooseType = BossCompleteChooseType.TYPE_WRITE_MANUALLY
                }
                generateWithoutChange = chooseType != BossCompleteChooseType.TYPE_WRITE_MANUALLY
                sendUiState {
                    copy(
                        chooseType = chooseType
                    )
                }
                if (chooseType != BossCompleteChooseType.TYPE_WRITE_MANUALLY) {
                    applyParsedResult(jobParseResult)
                } else {
                    applyJobDetailResult(intent.jobDetailResult)
                }
            }

            is BossCompleteFirstJobUiIntent.Next -> {
                postJob()
            }

            is BossCompleteFirstJobUiIntent.UpdateList -> {
                sendUiState {
                    copy(list = intent.list, localStorage = intent.localStorage).checkCanSave()
                }
                generateWithoutChange = false
            }

            else -> {

            }

        }
    }


    private fun applyParsedResult(parseResult: JobParseResult?) {
        TLog.info("applyParsedResult","parseResult1: $parseResult")
        parseResult?.run {
            parseResult.toBossEditData()?.let {
                //region
                it.jobDescHasGenerate = !it.jobDesc.isNullOrBlank()
                it.jobDescIsApplied = !it.jobDesc.isNullOrBlank()
                //endregion
                sendUiState {
                    copy(
                        list = it.applyDataForList(list),
                        localStorage = it
                    ).checkCanSave()
                }
            }
            T.ss(R.string.boss_autofill_completed_toast)
        }

    }

    private fun applyJobDetailResult(jobDetailResult: JobDetailResult?) {
        TLog.info(TAG,"applyJobDetailResult: $jobDetailResult")
        jobDetailResult?.jobDetailJobInfo?.apply {
            toBossEditData().let {
                sendUiState {
                    copy(
                        list = it.applyDataForList(list),
                        localStorage = it
                    ).checkCanSave()
                }
            }
        }
    }

    private fun postJob() {
        val data = uiStateFlow.value.localStorage
        showLoadingDialog()
        requestData(
            request = {
                jobRepo.addJob(
                    jobTitle = data.jobTitle.name,
                    jobType = data.employmentType.map { it.code }.toJson(),
                    jobCode = data.jobTitle.code.toString(),
                    workplaceType = data.workplaceType.code.toString(),
                    jobLocation = (data.jobLocation?.map { it.code })?.toJson() ?: "",
                    salaryType = data.salaryType.code.toString(),
                    salaryUnit = LocalManager.getSalaryUnitParamValue().toString(),
                    minSalary = data.minSalary.toString(),
                    maxSalary = data.maxSalary.toString(),
                    educationRequirement = data.eduLevel.code.toString(),
                    experienceRequirement = (data.expLevel?.map { it.code } ?: mutableListOf<Long>()).toJson(),
                    jobDesc = data.jobDesc ?: "",
                    jobDescStyle = data.jobDescStyle ?: "",
                    skills = (data.jobSkills ?: mutableListOf<HighlightBean>()).toJson(),
                    languages = data.languages.map { it.code }.toJson(),
                    benefits = data.benefits.map { it.code }.toJson(),
                    visaSponsorship = data.visaSponsorship.toString()
                )
            },
            success = {
                it?.jobId?.apply {
                    onlineJob(this)
                }
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )
    }

    private fun onlineJob(jobId: String) {
        requestData(
            request = {
                jobRepo.updateJobStatus(jobId, JobStatus.OPENING)
            },
            success = {
                dismissLoadingDialog()
                postJobFinish.value = true
            },
            fail = {
                dismissLoadingDialog()
                postJobFinish.value = true
            }
        )
    }

    /**
     * 检查本地数据是否完整
     * @return true:数据完整 false:数据不完整
     */
    private fun checkParamsByLocal(data: EditJobLocalData):Boolean{
        return when{
            data.jobTitle.name.isBlank()->{
                T.ss(R.string.boss_please_fill_in_job_title)
                false
            }
            data.employmentType.isEmpty()->{
                T.ss(R.string.boss_please_fill_in_employment_type)
                false
            }
            data.workplaceType.name.isBlank()->{
                T.ss(R.string.boss_please_fill_in_workplace_type)
                false
            }
            data.jobLocation.isNullOrEmpty() && data.workplaceType.code != DefaultValueConstants.FULL_REMOTE_CODE->{
                T.ss(R.string.boss_please_fill_in_job_location)
                false
            }
            data.salaryType.name.isBlank()->{
                T.ss(R.string.boss_please_fill_in_pay)
                false
            }
            data.jobDesc.isNullOrBlank()->{
                T.ss(R.string.boss_please_fill_in_job_description)
                false
            }
            else->{
                true
            }
        }
    }

}

data class BossCompleteFirstJobUiState(
    val canSave: Boolean = false,
    val list: List<JobEditItem> = JobEditItemFactory().createItemList(),
    val localStorage: EditJobLocalData = EditJobLocalData(),
    val isPostSuccess: Boolean = false,
    val chooseType: @BossCompleteChooseType Int = BossCompleteChooseType.TYPE_LINK,
) : IUiState {
    fun checkCanSave(): BossCompleteFirstJobUiState {
        var completed = true
        val items = list
        for (item in items) {
            if (!item.optional && item.content.isNullOrBlank()) {
                completed = false
                break
            }
        }
        return this.copy(canSave = completed)
    }
}

sealed class BossCompleteFirstJobUiIntent : IUiIntent {
    data object Next : BossCompleteFirstJobUiIntent()
    data class UpdateList(val list: List<JobEditItem>, val localStorage: EditJobLocalData) : BossCompleteFirstJobUiIntent()
    data class Init(val chooseType: @BossCompleteChooseType Int, val jobParseResult: JobParseResult?, val jobDetailResult: JobDetailResult?) : BossCompleteFirstJobUiIntent()
}