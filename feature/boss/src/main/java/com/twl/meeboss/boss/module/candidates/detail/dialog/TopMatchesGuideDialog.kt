package com.twl.meeboss.boss.module.candidates.detail.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.fragment.CommonBottomDialogFragment
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable

class TopMatchesGuideDialog : CommonBottomDialogFragment() {

    @Composable
    override fun DialogContent() {
        Column(
            Modifier
                .fillMaxWidth()
                .noRippleClickable {/* 阻止事件向上传递 */ }
                .background(Color.White, shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .padding(top = 20.dp, bottom = 16.dp, start = 16.dp, end = 16.dp)

        ) {
            Image(
                painter = painterResource(R.drawable.ui_dailog_close),
                contentDescription = "close",
                modifier = Modifier
                    .clickable {
                        dismissSafely()
                    }
                    .size(20.dp)

            )

            Text(
                modifier = Modifier
                    .padding(top = 28.dp),
                text = stringResource(R.string.employer_top_matches_instruction_ai_title),
                fontSize = 24.sp,
                fontWeight = FontWeight.SemiBold,
                color = COLOR_222222
            )

            Text(
                modifier = Modifier
                    .padding(vertical = 24.dp)
                    .align(Alignment.CenterHorizontally),
                text = stringResource(R.string.employer_top_matches_instruction_ai_subtitle),
                fontSize = 14.sp,
                color = COLOR_484848
            )

            XCommonButton(
                modifier = Modifier.padding(top = 16.dp),
                text = stringResource(R.string.top_matches_instruction_button),
                enabled = true,
                onClick = {
                    dismissSafely()
                }
            )
        }
    }
}