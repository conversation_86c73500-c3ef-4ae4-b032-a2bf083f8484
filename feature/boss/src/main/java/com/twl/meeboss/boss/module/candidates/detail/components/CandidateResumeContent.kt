package com.twl.meeboss.boss.module.candidates.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.candidates.detail.dialog.TopMatchesGuideDialog
import com.twl.meeboss.export_share.model.CandidateResumeResult
import com.twl.meeboss.boss.module.candidates.detail.preview.CandidateResumePreviewParameterProvider
import com.twl.meeboss.chat.export.component.HighlyMatchReasonCard
import com.twl.meeboss.core.ui.utils.showSafely

/**
 * @author: 冯智健
 * @date: 2024年07月19日 21:01
 * @description:
 */
@Preview
@Composable
fun CandidateResumeContent(
    @PreviewParameter(CandidateResumePreviewParameterProvider::class)
    candidateResume: CandidateResumeResult,
    onTalentPoolCardClick: (Rect) -> Unit = {},
    onTopMatchesClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .background(Color.White)
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        CandidateResumeHeaderCard(candidateResume = candidateResume)

        val topMatchReason = candidateResume.topMatchReason
        if (topMatchReason.isNullOrBlank()) {
            CandidateTalentPoolCard(
                showJoinedTalentPool = candidateResume.hasJoinedCompany(),
                message = candidateResume.comment,
                onClick = onTalentPoolCardClick
            )
        } else {
            HighlyMatchReasonCard(
                title = stringResource(R.string.employer_top_matches_instruction_ai_title),
                reason = topMatchReason
            ) {
                onTopMatchesClick()
            }
        }


        candidateResume.workExpList?.mapNotNull { it }?.takeIf { it.isNotEmpty() }?.let {
            CandidateResumeWorkExpCard(it)
        }
        candidateResume.projectExpList?.mapNotNull { it }?.takeIf { it.isNotEmpty() }?.let {
            CandidateResumeProjectExpCard(it)
        }
        candidateResume.eduExpList?.mapNotNull { it }?.takeIf { it.isNotEmpty() }?.let {
            CandidateResumeEduExpCard(it)
        }
        CandidateResumeFooterCard(candidateResume)
    }
}