package com.twl.meeboss.boss.module.candidates.detail.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.facade.CandidateResumeInteract
import com.twl.meeboss.boss.module.candidates.detail.preview.TopMatchesCandidateResumePreviewParameterProvider
import com.twl.meeboss.chat.export.component.HighlyMatchReasonCard
import com.twl.meeboss.export_share.model.CandidateResumeResult
import com.twl.meeboss.export_share.topmatches.TopHighlyMatchedDetailModel

/**
 * @author: musa on 2025/05/13
 * @e-mail: <EMAIL>
 * @desc: 顶部匹配的布局
 */

@Preview
@Composable
fun TopMatchesCandidateResumeContent(
    @PreviewParameter(TopMatchesCandidateResumePreviewParameterProvider::class)
    topMatchesCandidateResume: Pair<TopHighlyMatchedDetailModel, CandidateResumeResult>,
    modifier: Modifier = Modifier.background(Color.White),
    candidateResumeInteract: CandidateResumeInteract? = null,
) {
    val candidateResume = topMatchesCandidateResume.second
    val reason = topMatchesCandidateResume.first.reason
    Column(
        modifier = modifier
    ) {
        CandidateResumeHeaderCard(
            modifier = Modifier.padding(bottom = 4.dp),
            candidateResume = candidateResume
        )

        reason.takeIf { it.isNotEmpty() }?.let {
            HighlyMatchReasonCard(
                title = stringResource(R.string.employer_top_matches_instruction_ai_title),
                reason = it,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            ) {
                candidateResumeInteract?.onClickHighlyMatchedReason()
            }
        }

        candidateResume.workExpList?.mapNotNull { it }?.takeIf { it.isNotEmpty() }?.let {
            CandidateResumeWorkExpCard(it)
        }
        candidateResume.projectExpList?.mapNotNull { it }?.takeIf { it.isNotEmpty() }?.let {
            CandidateResumeProjectExpCard(it)
        }
        candidateResume.eduExpList?.mapNotNull { it }?.takeIf { it.isNotEmpty() }?.let {
            CandidateResumeEduExpCard(it)
        }
        CandidateResumeFooterCard(candidateResume)
    }
}