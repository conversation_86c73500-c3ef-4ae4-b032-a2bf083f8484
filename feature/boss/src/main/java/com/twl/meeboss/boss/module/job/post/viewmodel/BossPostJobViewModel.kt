package com.twl.meeboss.boss.module.job.post.viewmodel

import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.eventbus.sendIntLiveEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.base.model.job.JobStatus
import com.twl.meeboss.boss.model.JobParseResult
import com.twl.meeboss.boss.module.job.ktx.toBossEditData
import com.twl.meeboss.boss.module.job.model.EditJobLocalData
import com.twl.meeboss.boss.module.job.model.JobEditItem
import com.twl.meeboss.boss.module.job.model.JobEditItemFactory
import com.twl.meeboss.boss.module.job.post.repository.BossEditJobDraftRepository
import com.twl.meeboss.boss.repos.BossJobRepository
import com.twl.meeboss.boss.repos.BossRepository
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.handleDefaultError
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import javax.inject.Inject


@HiltViewModel
class BossPostJobViewModel @Inject constructor(
    private val repos: BossJobRepository,
    private val bossRepos: BossRepository,
    private val draftRepository: BossEditJobDraftRepository
) : BaseMviViewModel<BossPostUiState, BossPostUiIntent>() {

    private var parsingJob: Job? = null

    val showDraftDialog: MutableLiveData<EditJobLocalData> = MutableLiveData()

    init {
        showJobTemplates()
    }

    override fun initUiState(): BossPostUiState = BossPostUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossPostUiIntent.UpdateList -> {
                sendUiState {
                    copy(list = intent.list, localStorage = intent.localStorage).checkCanSave()
                }
            }

            is BossPostUiIntent.Save -> {
                saveJob()
            }

            is BossPostUiIntent.GetDraft -> {
                loadDraft()
            }

            is BossPostUiIntent.SaveDraft -> {
                launcherOnIO {
                    draftRepository.saveDraft(uiStateFlow.value.localStorage)
                }
            }

            is BossPostUiIntent.DeleteDraft -> {
                deleteDraft()
            }

            is BossPostUiIntent.ApplyDraft -> {
                applyDraft(intent.draft)
            }

            is BossPostUiIntent.OnGetJobParseResult -> {
                applyParsedResult(intent.parseResult)
            }

            is BossPostUiIntent.OnGetEditJobLocalData ->{
                sendUiState {
                    copy(
                        list = intent.editJobLocalData.applyDataForList(JobEditItemFactory().createItemList()),
                        localStorage = intent.editJobLocalData
                    ).checkCanSave()
                }
            }

        }
    }

    private fun deleteDraft() {
        launcherOnIO {
            draftRepository.deleteDraft()
        }
    }

    fun needSaveDraft(): Boolean {
        val items = uiStateFlow.value.list
        for (item in items) {
            if (!item.content.isNullOrBlank()) {
                return true
            }
        }
        return false
    }

    private fun loadDraft() {
        launcherOnIO {
            val data = draftRepository.readDraft()
            data?.run {
                showDraftDialog.postValue(this)
            }
        }
    }

    private fun applyDraft(draft: EditJobLocalData){
        sendUiState {
            copy(list = draft.applyDataForList(list), localStorage = draft).checkCanSave()
        }
    }

    private fun applyParsedResult(parseResult: JobParseResult?) {
        TLog.info("applyParsedResult","parseResult1: $parseResult")
        parseResult?.jobDetail?.toBossEditData()?.let {
            sendUiState {
                copy(
                    list = it.applyDataForList(JobEditItemFactory().createItemList()),
                    localStorage = it
                ).checkCanSave()
            }
        }
    }


    private fun saveJob() {
        val items = uiStateFlow.value.list
        for (item in items) {
            if (!item.optional && item.content.isNullOrBlank()) {
                T.ss(R.string.common_validate_rule_empty)
                return
            }
        }
        sendUiState { copy(showLoading = true) }
        requestData(
            enableLoadState = false,
            request = {
                val data = uiStateFlow.value.localStorage
                sendUiState { copy(showLoading = false) }
                if(uiStateFlow.value.jobId.isBlank()){
                    repos.addJob(jobTitle = data.jobTitle.name,
                        jobType = (data.employmentType.map { it.code } ?: mutableListOf<Long>()).toJson(),
                        jobCode = data.jobTitle.code.toString(),
                        workplaceType = data.workplaceType.code.toString(),
                        jobLocation = data.jobLocation?.map { it.code }?.toJson()?:"",
                        salaryType = data.salaryType.code.toString(),
                        salaryUnit = LocalManager.getSalaryUnitParamValue().toString(),
                        minSalary = data.minSalary.toString(),
                        maxSalary = data.maxSalary.toString(),
                        educationRequirement = data.eduLevel.code.toString(),
                        experienceRequirement = (data.expLevel?.map { it.code } ?: mutableListOf<Long>()).toJson(),
                        jobDesc = data.jobDesc ?: "",
                        jobDescStyle = data.jobDescStyle ?: "",
                        skills = (data.jobSkills ?: mutableListOf<HighlightBean>()).toJson(),
                        languages = data.languages.map { it.code }.toJson(),
                        benefits = data.benefits.map { it.code }.toJson(),
                        visaSponsorship = data.visaSponsorship.toString())
                }else{
                    repos.updateJob(
                        jobId = uiStateFlow.value.jobId,
                        jobTitle = data.jobTitle.name,
                        jobType = data.employmentType.map { it.code }.toJson(),
                        jobCode = data.jobTitle.code.toString(),
                        workplaceType = data.workplaceType.code.toString(),
                        jobLocation = data.jobLocation?.map { it.code }?.toJson()?:"",
                        salaryType = data.salaryType.code.toString(),
                        minSalary = data.minSalary.toString(),
                        maxSalary = data.maxSalary.toString(),
                        educationRequirement = data.eduLevel.code.toString(),
                        experienceRequirement = (data.expLevel?.map { it.code } ?: mutableListOf<Long>()).toJson(),
                        jobDesc = data.jobDesc ?: "",
                        jobDescStyle = data.jobDescStyle ?: "",
                        skills = (data.jobSkills ?: mutableListOf<HighlightBean>()).toJson(),
                        languages = data.languages.map { it.code }.toJson(),
                        benefits = data.languages.map { it.code }.toJson(),
                        visaSponsorship = data.visaSponsorship.toString()
                    )
                }

            },
            success = {
                it?.let {result->
                    deleteDraft()
                    if(result.jobId != null){
                        updateJobStatus(result.jobId)
                    }
                }
            },
            fail = {
                it.handleDefaultError()
                XLog.error(TAG, "getJobDetail fail: ${it.message}")
            }
        )
    }

    private fun updateJobStatus(jobId: String, status: @JobStatus Int = JobStatus.OPENING) {
        requestData(
            enableLoadState = false,
            request = {
                repos.updateJobStatus(jobId, status)
            },
            success = {
                sendUiState {
                    copy(isPostSuccess = true, jobStatus = it?.jobStatus ?: JobStatus.UNKNOWN, jobId = jobId)
                }
                sendIntLiveEvent(BossEventBusKey.UPDATE_JOB_STATUS, status)
            },
            fail = {
                T.ss(it.message)
                XLog.error(TAG, "getJobDetail fail: ${it.message}")
            }
        )
    }

    private fun showJobTemplates(){
        requestData(
            enableLoadState = false,
            request = {
                bossRepos.showJobTemplates()
            },
            success = {
                sendUiState {
                    copy(showTemplate = it?.show ?: false)
                }
            }
        )
    }

}

data class BossPostUiState(
    val canSave: Boolean = false,
    val showTemplate: Boolean = false,
    val list: List<JobEditItem> = JobEditItemFactory().createItemList(),
    val localStorage: EditJobLocalData = EditJobLocalData(),
    val isPostSuccess: Boolean = false,
    val showLoading: Boolean = false,
    val jobStatus:Int = JobStatus.OPENING,
    val jobId:String = ""
) : IUiState {
    fun checkCanSave(): BossPostUiState {
        var completed = true
        val items = list
        for (item in items) {
            if (!item.optional && item.content.isNullOrBlank()) {
                completed = false
                break
            }
        }
        return this.copy(canSave = completed)
    }
}

sealed class BossPostUiIntent : IUiIntent {
    data object GetDraft : BossPostUiIntent()
    data object Save : BossPostUiIntent()
    data class UpdateList(val list: List<JobEditItem>, val localStorage: EditJobLocalData) : BossPostUiIntent()
    data object SaveDraft : BossPostUiIntent()
    data object DeleteDraft : BossPostUiIntent()
    data class ApplyDraft(val draft:EditJobLocalData) : BossPostUiIntent()
    data class OnGetJobParseResult(val parseResult:JobParseResult) : BossPostUiIntent()
    data class OnGetEditJobLocalData(val editJobLocalData:EditJobLocalData) : BossPostUiIntent()

}

fun Int.getVisaSponsorshipDesc() = when(this){
    1 -> R.string.employer_job_posting_visa_sponsor_1.toResourceString()
    2 -> R.string.employer_job_posting_visa_sponsor_2.toResourceString()
    else -> ""
}
