package com.twl.meeboss.boss.module.job.model

import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.boss.R
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.core.ui.utils.toResourceStringWithOption

const val EMPLOYEE_LOCATION_KEY = "employeeLocation"
const val JOB_LOCATION_KEY = "jobLocation"
const val JOB_TITLE_KEY = "jobTitle"
const val EMPLOYMENT_TYPE_KEY = "employmentType"
const val WORKPLACE_TYPE_KEY = "workplaceType"
const val PAY_KEY = "pay"
const val JOB_DESC_KEY = "jobDesc"
const val EDU_LEVEL_KEY = "eduLevel"
const val EXP_LEVEL_KEY = "expLevel"
const val JOB_SKILLS_KEY = "jobSkills"
const val LANGUAGE_KEY = "language"
const val VISA_SPONSORSHIP = "visaSponsorship"

data class JobEditItem(
    val key: String = "",
    val title: String = "", //标题
    val placeHolder: String = "", //内容为空的时候的提示
    var content: String? = "", //内容
    val optional: Boolean = false, //是否选填:true 必填 ; false 选填
    val canEdit: Boolean = true,
    val canDelete: Boolean = false,
    var localTempList: List<String>? = listOf("") //本地临时数据
) : BaseEntity {


    fun isNecessary(): Boolean {
        return true
    }
}


class JobEditItemFactory {
    fun createItemList(): MutableList<JobEditItem> {
        return mutableListOf(
            JobEditItem(
                key = JOB_TITLE_KEY,
                title = R.string.common_job_title.toResourceString(),
                placeHolder = R.string.common_enter_job_title.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = EMPLOYMENT_TYPE_KEY,
                title = R.string.common_employment_type.toResourceString(),
                placeHolder = R.string.job_select_employment_type.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = WORKPLACE_TYPE_KEY,
                title = R.string.common_workplace_type.toResourceString(),
                placeHolder = R.string.job_select_workplace_type.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem().toJobLocationItem(),
            JobEditItem(
                key = PAY_KEY,
                title = R.string.job_pay.toResourceString(),
                placeHolder = R.string.job_enter_pay.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = JOB_DESC_KEY,
                title = R.string.job_description.toResourceString(),
                placeHolder = R.string.job_enter_description.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = EDU_LEVEL_KEY,
                title = R.string.job_minimum_education_level.toResourceString(),
                placeHolder = R.string.job_select_minimum_education_level.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = EXP_LEVEL_KEY,
                title = R.string.job_experience_level.toResourceString(),
                placeHolder = R.string.job_select_experience_level.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = JOB_SKILLS_KEY,
                title = R.string.job_skills.toResourceString(),
                placeHolder = R.string.job_select_skills.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = LANGUAGE_KEY,
                title = R.string.common_language.toResourceString(),
                placeHolder = R.string.boss_post_job_language_hint.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = VISA_SPONSORSHIP,
                title = R.string.employer_job_posting_visa_sponsor.toResourceString(),
                placeHolder = R.string.job_seeker_chat_visa_sponsor_ask.toResourceString(),
                optional = true,
                canEdit = true,
            )
        )

    }

    fun createItemListForEditMode(): MutableList<JobEditItem> {
        return mutableListOf(
            JobEditItem(
                key = JOB_TITLE_KEY,
                title = R.string.common_job_title.toResourceString(),
                placeHolder = R.string.common_enter_job_title.toResourceString(),
                optional = false,
                canEdit = false
            ),
            JobEditItem(
                key = EMPLOYMENT_TYPE_KEY,
                title = R.string.common_employment_type.toResourceString(),
                placeHolder = R.string.job_select_employment_type.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = WORKPLACE_TYPE_KEY,
                title = R.string.common_workplace_type.toResourceString(),
                placeHolder = R.string.job_select_workplace_type.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem().toJobLocationItem().copy(canEdit = true),
            JobEditItem(
                key = PAY_KEY,
                title = R.string.job_pay.toResourceString(),
                placeHolder = R.string.job_enter_pay.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = JOB_DESC_KEY,
                title = R.string.job_description.toResourceString(),
                placeHolder = R.string.job_enter_description.toResourceString(),
                optional = false,
                canEdit = true
            ),
            JobEditItem(
                key = EDU_LEVEL_KEY,
                title = R.string.job_minimum_education_level.toResourceString(),
                placeHolder = R.string.job_select_minimum_education_level.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = EXP_LEVEL_KEY,
                title = R.string.job_experience_level.toResourceString(),
                placeHolder = R.string.job_select_experience_level.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = JOB_SKILLS_KEY,
                title = R.string.job_skills.toResourceString(),
                placeHolder = R.string.job_select_skills.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = LANGUAGE_KEY,
                title = R.string.common_language.toResourceString(),
                placeHolder = R.string.boss_post_job_language_hint.toResourceString(),
                optional = true,
                canEdit = true
            ),
            JobEditItem(
                key = VISA_SPONSORSHIP,
                title = R.string.employer_job_posting_visa_sponsor.toResourceString(),
                placeHolder = R.string.job_seeker_chat_visa_sponsor_ask.toResourceString(),
                optional = true,
                canEdit = true,
            )
        )
    }


    companion object {
        fun JobEditItem.toJobLocationItem(): JobEditItem {
            return this.copy(
                key = JOB_LOCATION_KEY,
                title = R.string.job_location.toResourceString(),
                placeHolder = R.string.job_enter_location.toResourceString(),
                content = "",
                optional = false,
                canEdit = true
            )
        }

        fun JobEditItem.toEmployeeLocationItem(): JobEditItem {
            return this.copy(
                key = EMPLOYEE_LOCATION_KEY,
                title = R.string.job_employee_location.toResourceString(),
                placeHolder = R.string.job_enter_employee_location.toResourceString(),
                content = "",
                optional = true,
                canEdit = true
            )
        }
    }
}