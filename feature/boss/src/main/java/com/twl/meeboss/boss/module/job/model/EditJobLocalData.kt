package com.twl.meeboss.boss.module.job.model

import com.blankj.utilcode.util.EncryptUtils
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.ktx.toMoneyString
import com.twl.meeboss.base.model.BaseEntity
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.common.CommonBrandBean
import com.twl.meeboss.boss.module.job.model.JobEditItemFactory.Companion.toEmployeeLocationItem
import com.twl.meeboss.boss.module.job.post.viewmodel.getVisaSponsorshipDesc
import com.twl.meeboss.core.ui.utils.toResourceString

data class EditJobLocalData(
    var jobTitle: HighlightBean = HighlightBean(),
    var employmentType: List<OptionBean> = listOf(),
    var workplaceType: OptionBean = OptionBean(),
    var salaryType: OptionBean = OptionBean(),
    var salaryUnit: OptionBean = OptionBean(),
    var minSalary: Long = 0L,
    var maxSalary: Long = 0L,
    var jobLocation: MutableList<CityBean>? = mutableListOf(),
    var jobDesc: String? = "",
    var jobDescStyle: String? = "",
    var eduLevel: OptionBean = OptionBean(),
    var expLevel: List<OptionBean>? = listOf(),
    var jobSkills: MutableList<HighlightBean>? = mutableListOf(),
    var languages: List<IndexBean> = listOf(),
    var firstName: String? = "",
    var lastName: String? = "",
    var bossPosition: String? = "",
    var companyInfo: CommonBrandBean = CommonBrandBean(),
    var jobDescHasGenerate:Boolean = false, //职位描述是否已经生成过,更换jobTitle之后可以重新生成
    var jobDescIsApplied:Boolean = false, //职位描述是否已经应用过,用于显示绿条
    var benefits: List<OptionBean> = listOf(), // 福利
    var visaSponsorship: Int = 0, //是否提供签证担保
) : BaseEntity {

    fun applyDataForList(originList: List<JobEditItem>): MutableList<JobEditItem> {
        val list = originList.toMutableList()
        var needHandleEmployeeLocation = false
        var locationIndex = -1
        list.forEachIndexed { index, item ->
            when (item.key) {
                JOB_TITLE_KEY -> {
                    item.content = jobTitle.name
                }

                EMPLOYMENT_TYPE_KEY -> {
                    item.content = employmentType?.joinToString(", ") { it.name }
                }

                WORKPLACE_TYPE_KEY -> {
                    item.content = workplaceType.name
                    if (workplaceType.code == DefaultValueConstants.FULL_REMOTE_CODE) {
                        needHandleEmployeeLocation = true
                    }
                }

                JOB_LOCATION_KEY -> {
                    item.content = jobLocation?.joinToString("; ") { it.name } ?: ""
                    locationIndex = index
                }

                EMPLOYEE_LOCATION_KEY -> {
                    item.content = jobLocation?.joinToString("; ") { it.name } ?: ""
                }

                PAY_KEY -> {
                    if (minSalary > 0L) {
                        val unitString = LocalManager.getSalaryUnitNameValue()
                        val showSalaryStr = if (minSalary == maxSalary) {
                            "${unitString}${minSalary.toMoneyString()} ${salaryType.name}"
                        } else {
                            "${unitString}${minSalary.toMoneyString()}-${unitString}${maxSalary.toMoneyString()} ${salaryType.name}"
                        }
                        item.content = showSalaryStr
                    }

                }

                JOB_DESC_KEY -> {
                    item.content = jobDesc
                }

                EDU_LEVEL_KEY -> {
                    item.content = eduLevel.name
                }

                EXP_LEVEL_KEY -> {
                    item.content = expLevel?.joinToString(", ") { it.name } ?: ""
                }

                JOB_SKILLS_KEY -> {
                    item.content = jobSkills?.joinToString(", ") { it.name } ?: ""
                }
                LANGUAGE_KEY->{
                    item.content = languages.joinToString(", ") { it.name }
                }
                VISA_SPONSORSHIP->{
                    item.content = visaSponsorship.getVisaSponsorshipDesc()
                }
            }
        }
        if (needHandleEmployeeLocation && locationIndex >= 0) {
            list[locationIndex] = list[locationIndex].toEmployeeLocationItem().also {
                it.content = jobLocation?.joinToString("; ") { l -> l.name } ?: ""
            }
        }
        return list
    }

    fun getDataFingerprint(): String {
        return EncryptUtils.encryptMD5ToString(this.toString())
    }
}