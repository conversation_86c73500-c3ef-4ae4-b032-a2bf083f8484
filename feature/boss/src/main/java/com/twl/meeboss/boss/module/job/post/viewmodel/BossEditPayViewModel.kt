package com.twl.meeboss.boss.module.job.post.viewmodel

import com.twl.meeboss.base.api.BaseApi
import com.twl.meeboss.base.components.dialog.CommonDataType
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.repo.toResult
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.api.JobApi
import com.twl.meeboss.boss.module.job.manager.model.JobSalaryConfigResult
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.core.network.getService
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class BossEditPayViewModel @Inject constructor() : BaseMviViewModel<BossEditPayUiState, BossEditPayUiIntent>() {
    companion object {
        private const val TAG = "BossEditPayViewModel"
        const val TYPE_HIDE_SUGGEST_SALARY = 0
        const val TYPE_SHOW_MIN_SUGGEST_SALARY = 1
        const val TYPE_SHOW_MAX_SUGGEST_SALARY = 2
    }

    var jobSalaryConfig: JobSalaryConfigResult? = null

    override fun initUiState(): BossEditPayUiState = BossEditPayUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossEditPayUiIntent.Init -> {
                init(intent.firstBean, intent.secondBean, intent.thirdBean,
                    intent.minSalary, intent.maxSalary, intent.salaryType,
                    intent.jobTitle, intent.jobLocation, intent.benefits)
            }

            is BossEditPayUiIntent.OnInputMinSalary -> {
                setMinSalaryValue(intent.minSalary)
            }

            is BossEditPayUiIntent.OnInputMaxSalary -> {
                setMaxSalaryValue(intent.maxSalary)
            }

            is BossEditPayUiIntent.OnSelectSalaryType -> {
                sendUiState { copy(currentSalaryType = intent.salaryType) }
            }

            is BossEditPayUiIntent.OnInputMinStateChanged -> {
                sendUiState { copy(minState = intent.state) }
            }

            is BossEditPayUiIntent.OnInputMaxStateChanged -> {
                sendUiState { copy(maxState = intent.state) }
            }

            is BossEditPayUiIntent.GetBenefits -> {
                getBenefits()
            }

            is BossEditPayUiIntent.OnSelectBenefit -> {
                val newSelectedBenefits = uiStateFlow.value.selectedBenefits.toMutableList()
                if (newSelectedBenefits.contains(intent.benefit)) {
                    newSelectedBenefits.remove(intent.benefit)
                } else {
                    newSelectedBenefits.add(intent.benefit)
                }
                sendUiState { copy(selectedBenefits = newSelectedBenefits) }
            }

            is BossEditPayUiIntent.OnChangeSuggestSalary -> {
                val suggestSalary = intent.suggestSalary
                sendUiState { copy(suggestSalary = suggestSalary) }
            }

            is BossEditPayUiIntent.OnCheckMaxSalary -> {
                val maxSalary = intent.maxSalary
                checkMaxSalary(maxSalary.toIntOrNull() ?: 0)
            }
            else -> {

            }

        }
    }

    private fun init(firstBean: OptionBean,
                     secondBean: OptionBean,
                     thirdBean: OptionBean,
                     minSalary: Long,
                     maxSalary: Long,
                     salaryType: Long,
                     jobTitle: String,
                     jobLocation: String,
                     benefits: List<OptionBean>
    ) {
        val type = if (salaryType == 0L) {
            firstBean.code
        } else {
            salaryType
        }
        sendUiState {
            copy(
                minSalary = minSalary,
                maxSalary = maxSalary,
                firstBean = firstBean,
                secondBean = secondBean,
                thirdBean = thirdBean,
                jobTitle = jobTitle,
                jobLocation = jobLocation,
                currentSalaryType = type,
                selectedBenefits = benefits
            ).checkCanSave()
        }

        // 获取职位薪资配置
        getJobSalaryConfig()
    }

    private fun setMaxSalaryValue(string: String) {
        sendUiState {
            copy(maxSalary = string.toLongOrNull() ?: 0L,
                maxState = InputState.Focus,
                minState = InputState.None,
                errorMsg = "",
                suggestSalary = SuggestSalary(TYPE_HIDE_SUGGEST_SALARY)
            ).checkCanSave()
        }
    }

    private fun setMinSalaryValue(string: String) {
        sendUiState {
            copy(minSalary = string.toLongOrNull() ?: 0L,
                maxState = InputState.None,
                minState = InputState.Focus,
                errorMsg = "",
                suggestSalary = SuggestSalary(TYPE_HIDE_SUGGEST_SALARY)
            ).checkCanSave()
        }
        checkMaxSalary(uiStateFlow.value.maxSalary.toInt())
    }


    fun checkCanConfirm(callback: () -> Unit) {
        if (uiStateFlow.value.minSalary > uiStateFlow.value.maxSalary) {
            sendUiState {
                copy(errorMsg = R.string.job_min_not_less_than_max_error.toResourceString(),
                    minState = InputState.Error,
                    maxState = InputState.Error)
            }
        } else if (checkMinSalary()) {
            callback.invoke()
            PointHelper.reportPoint("postjob-pay-save"){
                addP(uiStateFlow.value.currentSalaryType.toString())
                addP2(uiStateFlow.value.minSalary.toString())
                addP3(uiStateFlow.value.maxSalary.toString())
            }
        }
    }

    /**
     * 检查输入的最小薪资是否小于最小建议薪资
     */
    private fun checkMinSalary(): Boolean {
        return jobSalaryConfig?.let {
            val minSuggestSalary = getMinSuggestSalary(it)
            if (uiStateFlow.value.minSalary < minSuggestSalary) {
                sendUiState {
                    copy(
                        suggestSalary = SuggestSalary(type = TYPE_SHOW_MIN_SUGGEST_SALARY, salary = minSuggestSalary),
                        minState = InputState.Error
                    )
                }
                PointHelper.reportPoint("salaryexceed-minimum-popup"){
                    addP(uiStateFlow.value.minSalary.toString())
                    addP2(uiStateFlow.value.jobTitle)
                    addP3(uiStateFlow.value.jobLocation)
                }
                return false
            }
            return true
        } ?: true
    }

    private fun getMinSuggestSalary(jobSalaryConfig: JobSalaryConfigResult) : Int {
        return when (uiStateFlow.value.currentSalaryType) {
            1L -> jobSalaryConfig.yearMinSalary
            2L -> jobSalaryConfig.monthMinSalary
            3L -> jobSalaryConfig.hourMinSalary
            else -> 0
        }
    }

    /**
     * 检查输入的最大薪资是否大于最大建议薪资
     */
    private fun checkMaxSalary(maxSalary: Int): Boolean {
        val minSalary = uiStateFlow.value.minSalary
        if (minSalary == 0L) {
            return true
        }
        val suggestMaxSalary = minSalary * (jobSalaryConfig?.maxSalaryMultiple ?: 2)
        if (maxSalary > suggestMaxSalary) {
            sendUiState {
                copy(
                    suggestSalary = SuggestSalary(type = TYPE_SHOW_MAX_SUGGEST_SALARY, salary = suggestMaxSalary.toInt()),
                )
            }
            PointHelper.reportPoint("salaryexceed-maximum-popup"){
                addP(maxSalary.toString())
                addP2(uiStateFlow.value.jobTitle)
                addP3(uiStateFlow.value.jobLocation)
            }
            return false
        }

        return true
    }

    fun getSalaryTypeName(): String {
        return when (uiStateFlow.value.currentSalaryType) {
            uiStateFlow.value.firstBean.code -> uiStateFlow.value.firstBean.name
            uiStateFlow.value.secondBean.code -> uiStateFlow.value.secondBean.name
            uiStateFlow.value.thirdBean.code -> uiStateFlow.value.thirdBean.name
            else -> ""
        }
    }

    private fun getBenefits() {
        launcherOnIO {
            getService(BaseApi::class.java)
                .commonListByType(CommonDataType.BENEFITS.type.toString())
                .toResult()
                .onSuccess {
                    sendUiState {
                        copy(
                            benefits = it.list ?: listOf()
                        )
                    }
                }
                .onFailure {
                    XLog.error(BossEditPayViewModel.TAG, "getBenefits onFailure: ${it.message}")
                }
        }
    }

    private fun getJobSalaryConfig() {
        launcherOnIO {
            getService(JobApi::class.java)
                .getSalaryConfig()
                .toResult()
                .onSuccess {
                   jobSalaryConfig = it
                }
                .onFailure {
                    XLog.error(BossEditPayViewModel.TAG, "getJobSalaryConfig onFailure: ${it.message}")
                }
        }
    }
}


data class BossEditPayUiState(val canSave: Boolean = false,
                              val minSalary: Long = 0L,
                              val maxSalary: Long = 0L,
                              val minState: InputState = InputState.None,
                              val maxState: InputState = InputState.None,
                              val currentSalaryType: Long = 0L,
                              val errorMsg: String = "",
                              val firstBean: OptionBean = OptionBean(),
                              val secondBean: OptionBean = OptionBean(),
                              val thirdBean: OptionBean = OptionBean(),
                              val jobTitle: String = "",
                              val jobLocation: String = "",
                              val benefits: List<OptionBean> = listOf(),
                              val selectedBenefits: List<OptionBean> = listOf(),
                              val suggestSalary: SuggestSalary = SuggestSalary()
) : IUiState {
    fun checkCanSave() = copy(canSave = minSalary > 0 && maxSalary > 0)
}

sealed class BossEditPayUiIntent : IUiIntent {
    data class Init(val firstBean: OptionBean = OptionBean(),
                    val secondBean: OptionBean = OptionBean(),
                    val thirdBean: OptionBean = OptionBean(),
                    val minSalary: Long,
                    val maxSalary: Long,
                    val salaryType: Long,
                    val jobTitle: String = "",
                    val jobLocation: String = "",
                    val benefits: List<OptionBean> = listOf()
    ) : BossEditPayUiIntent()

    data class OnInputMinSalary(val minSalary: String) : BossEditPayUiIntent()
    data class OnInputMaxSalary(val maxSalary: String) : BossEditPayUiIntent()
    data class OnSelectSalaryType(val salaryType: Long) : BossEditPayUiIntent()
    data class OnInputMinStateChanged(val state: InputState) : BossEditPayUiIntent()
    data class OnInputMaxStateChanged(val state: InputState) : BossEditPayUiIntent()
    data object GetBenefits : BossEditPayUiIntent()
    data class OnSelectBenefit(val benefit: OptionBean) : BossEditPayUiIntent()
    data class OnChangeSuggestSalary(val suggestSalary: SuggestSalary) : BossEditPayUiIntent()
    data class OnCheckMaxSalary(val maxSalary: String) : BossEditPayUiIntent()
}

data class SuggestSalary(
    /**
     * 0 不展示建议薪资
     * 1 展示最小建议薪资
     * 2 展示最大建议薪资
     */
    val type: Int = BossEditPayViewModel.TYPE_HIDE_SUGGEST_SALARY,
    val salary: Int = 0
)