package com.twl.meeboss.boss.module.me.home.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.glide.GlideImage
import com.skydoves.landscapist.placeholder.placeholder.PlaceholderPlugin
import com.twl.meeboss.base.ktx.getJoinString
import com.twl.meeboss.base.model.boss.BossInfo
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.module.me.home.preview.BossMePreviewParameterProvider
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.COLOR_F5F5F5
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString

/**
 * @author: 冯智健
 * @date: 2024年08月07日 16:57
 * @description:
 */
@Preview
@Composable
fun BossMeBaseInfoArea(
    @PreviewParameter(BossMePreviewParameterProvider::class)
    bossBaseInfoResult: BossInfo?
) {
    val context = LocalContext.current
    val localView = LocalView.current
    val userInfo = bossBaseInfoResult?.bossInfoUserVO
    val bossInfo = bossBaseInfoResult?.bossInfoBossVO
    Column(
        modifier = Modifier
            .background(Color.White)
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .noRippleClickable {
                    BossPointReporter.personalInfo(2)
                    BossPageRouter.jumpToBossPersonalInfoActivity(context)
                },
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier.padding(top = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                GlideImage(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(RoundedCornerShape(40.dp)),
                    imageModel = {
                        userInfo?.avatar
                    },
                    imageOptions = ImageOptions(
                        contentScale = ContentScale.Crop,
                    ),
                    previewPlaceholder = painterResource(id = R.mipmap.base_avatar_placeholder),
                    component = rememberImageComponent {
                        +PlaceholderPlugin.Loading(painterResource(id = R.mipmap.base_avatar_placeholder))
                        +PlaceholderPlugin.Failure(painterResource(id = R.mipmap.base_avatar_placeholder))
                    }
                )
            }
            val unknownStr = if (localView.isInEditMode) "Unknown" else R.string.common_unknown.toResourceString()
            val annotated = buildAnnotatedString {
                withStyle(
                    style = SpanStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Black222222,
                    )
                ) {
                    if (userInfo?.firstName.isNullOrEmpty() && userInfo?.lastName.isNullOrEmpty()) {
                        append(unknownStr)
                    } else {
                        append(getJoinString(
                            listOf(userInfo?.firstName?.trim(), userInfo?.lastName?.trim()),
                            " "
                        ) ?: unknownStr)
                    }
                }
                appendInlineContent(id = "inlineImage")
            }
            Text(
                modifier = Modifier.padding(top = 16.dp),
                text = annotated,
                textAlign = TextAlign.Center,
                inlineContent = mapOf("inlineImage" to InlineTextContent(Placeholder(24.sp, 24.sp, PlaceholderVerticalAlign.TextCenter)) {
                    Image(
                        painter = painterResource(id = R.drawable.ui_edit_name_icon),
                        modifier = Modifier
                            .width(24.dp)
                            .height(24.dp),
                        contentDescription = null
                    )
                })
            )
            Text(
                modifier = Modifier.padding(top = 8.dp),
                text = bossInfo?.position ?: "",
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                color = Black484848,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            Text(
                modifier = Modifier.padding(top = 4.dp),
                text = bossInfo?.companyName ?: "",
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                color = Black484848,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,

            )
        }
        CandidatesInfoBar(
            modifier = Modifier.padding(top = 20.dp),
            imageVector = ImageVector.vectorResource(id = R.drawable.ui_chat_icon),
            contentText = stringResource(id = com.twl.meeboss.boss.R.string.common_in_process),
            count = bossInfo?.appliedCandidateCount ?: 0
        ) {
            BossPageRouter.jumpToBossMyCandidateActivity(context, 1)
        }
        CandidatesInfoBar(
            modifier = Modifier.padding(top = 16.dp),
            imageVector = ImageVector.vectorResource(id = R.drawable.ui_icon_star_outline),
            contentText = stringResource(id = R.string.employer_me_starred_candidates_label),
            count = bossInfo?.myFavoriteJobSeekerCount ?: 0
        ) {
            BossPageRouter.jumpToBossMyCandidateActivity(context, 0)
        }
    }
}

@Composable
private fun CandidatesInfoBar(
    modifier: Modifier = Modifier,
    imageVector: ImageVector,
    contentText: String = "",
    count: Int = 0,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = modifier
            .background(
                color = COLOR_F5F5F5,
                shape = RoundedCornerShape(12.dp)
            )
            .fillMaxWidth()
            .height(76.dp)
            .noRippleClickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            modifier = Modifier
                .padding(start = 20.dp)
                .size(22.dp),
            imageVector = imageVector,
            contentDescription = null,
            tint = Black484848
        )
        Text(
            modifier = Modifier
                .padding(start = 10.dp)
                .weight(1F),
            text = contentText,
            textAlign = TextAlign.Start,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = Black222222
        )
        Text(
            modifier = Modifier.padding(horizontal = 4.dp),
            text = count.toString(),
            textAlign = TextAlign.Center,
            fontSize = 14.sp,
            color = Black222222
        )
        Icon(
            modifier = Modifier
                .padding(start = 4.dp, end = 16.dp)
                .size(20.dp),
            imageVector = ImageVector.vectorResource(id = R.drawable.ui_right_arrow),
            contentDescription = null,
            tint = Color.Unspecified
        )
    }
}