package com.twl.meeboss.boss.module.candidates.recommend

import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.ktx.getDefaultPage
import com.twl.meeboss.base.ktx.getDefaultPageSize
import com.twl.meeboss.base.model.CommonListResult
import com.twl.meeboss.base.model.setting.SettingCheckItemBean
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.base.protocol.ProtocolHelper
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.boss.foundation.moduleservice.BossUserInfoService
import com.twl.meeboss.boss.module.candidates.recommend.model.BossExtendBean
import com.twl.meeboss.boss.module.candidates.recommend.model.BossF1CardType
import com.twl.meeboss.boss.module.candidates.recommend.model.BossF1ListBean
import com.twl.meeboss.boss.module.candidates.recommend.model.BossListExtentType
import com.twl.meeboss.boss.module.candidates.recommend.model.GuideJobItem
import com.twl.meeboss.boss.module.candidates.recommend.model.JobTitleInfo
import com.twl.meeboss.boss.repos.BossCandidatesRepository
import com.twl.meeboss.boss.repos.BossJobRepository
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.common.exp.ApiException
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.handleDefaultError
import com.twl.meeboss.core.ui.utils.toResourceString
import com.twl.meeboss.setting.export.SettingServiceRouter
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年07月11日 11:50
 * @description:
 */
@HiltViewModel
class BossCandidatesViewModel @Inject constructor(
    private val jobRepo: BossJobRepository,
    private val bossRepo: BossCandidatesRepository
) : BaseMviViewModel<BossCandidatesUiState, BossCandidatesUiIntent>() {
    /**
     * 切换子tab时（RECOMMEND、VIEWED、FAVORED）获取候选人数据的Job
     */
    private var switchRequestTypeJob: Job? = null

    init {
        refreshNotification()
        resetTab()
    }

    override fun initUiState() = BossCandidatesUiState()

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossCandidatesUiIntent.ResetTabPage -> resetTab()
            is BossCandidatesUiIntent.SwitchTabPage -> switchTab(intent.tabIndex)
            is BossCandidatesUiIntent.SwitchRequestType -> switchRequestType(intent.requestType)
            is BossCandidatesUiIntent.HideBannerHeader -> hideHeaderBanner()
            is BossCandidatesUiIntent.Refresh -> {
                getCandidatesList1(getCurrentJobId(), requestType = getCurrentRequestType(), isRefresh = true, needSkip = false)
            }

            is BossCandidatesUiIntent.LoadMore -> {
                getCandidatesList1(getCurrentJobId(), requestType = getCurrentRequestType(), isRefresh = false, needSkip = false)
            }

            is BossCandidatesUiIntent.OnClickClose -> {
                sendStringLiveEvent(BossEventBusKey.UPDATE_JOB_STATUS, "")
            }

            is BossCandidatesUiIntent.DeleteBeginnerVerifyEmail -> {
                removeGeekBeginnerGuideCard(BossListExtentType.VERIFY_USER_EMAIL)
            }
            is BossCandidatesUiIntent.OnCardButtonClick -> {
                onExtendCardButtonClick(intent.bean)
            }
            is BossCandidatesUiIntent.RemoveTopMatchedCandidate -> {
                removeTopMatches(intent.friendId)
            }
            is BossCandidatesUiIntent.DeleteCompleteNameCard -> {
                val userInfo = BossUserInfoService.bossUserInfo.value?.bossInfoUserVO
                if (!userInfo?.firstName.isNullOrBlank() && !userInfo?.lastName.isNullOrBlank()) {
                    removeGeekBeginnerGuideCard(BossListExtentType.COMPLETE_USER_NAME)
                }
            }

            is BossCandidatesUiIntent.RefreshNotification -> {
                refreshNotification()
            }

            is BossCandidatesUiIntent.EnableNotification -> {
                enableNotification()
                BossPointReporter.f1TabNotificationOn(getCurrentJobId())
            }
        }
    }

    private fun resetTab() {
        getJobTitleList { result ->
            val jobIdList = result.values.mapNotNull { it.jobId }.filter { it.isNotBlank() }
            val map =
                jobIdList.associateWith {
                    MutableStateFlow(BossCandidatesListUiState(
                        listState = XRefreshListState.getDefault<BossF1ListBean>().copy(
                            loadFinishText = R.string.boss_candidates_list_load_finish_tips.toResourceString()
                        )
                    ))
                }
            sendUiState {
                copy(candidatesListUiStateMap = map)
            }
            sendStringLiveEvent(SWITCH_TAB_0, "")
            result[0]?.jobId?.takeIf { it.isNotBlank() }?.let {
                getCandidatesList1(it, requestType = RequestType.RECOMMEND, true, false)
            }
        }
    }

    private fun getTabIndex(): Int = uiStateFlow.value.tabIndex

    private fun getCurrentJobId(): String =
        uiStateFlow.value.jobTitleMap[getTabIndex()]?.jobId.notNull()

    private fun getCurrentRequestType(): RequestType =
        uiStateFlow.value.candidatesListUiStateMap[getCurrentJobId()]?.value?.requestType ?: RequestType.RECOMMEND

    private fun switchTab(tabIndex: Int) {
        if (uiStateFlow.value.tabIndex == tabIndex) {
            return
        }
        sendUiState { copy(tabIndex = tabIndex) }
        uiStateFlow.value.jobTitleMap[tabIndex]?.jobId?.let {
            val hasRequest = uiStateFlow.value.candidatesListUiStateMap[it]?.value?.hasRequested?:false
            getCandidatesList1(it, RequestType.RECOMMEND, true, hasRequest)
        }
    }

    private fun switchRequestType(requestType: RequestType) {
        if (getCurrentRequestType() == requestType) {
            return
        }

        // 先取消之前的job
        switchRequestTypeJob?.cancel()
        val showHeaderBanner = requestType != RequestType.RECOMMEND
        val jobId = getCurrentJobId()
        val candidatesUiState = uiStateFlow.value.candidatesListUiStateMap[jobId]?.value ?: return
        setUiState(jobId, candidatesUiState.copy(requestType = requestType, showHeaderBanner = showHeaderBanner, listState = candidatesUiState.listState.loading()))
        switchRequestTypeJob = getCandidatesList1(getCurrentJobId(), requestType, true, needSkip = false, isSwitchRequestType = true)
    }

    private fun hideHeaderBanner() {
        val jobId = getCurrentJobId()
        val candidatesUiState = uiStateFlow.value.candidatesListUiStateMap[jobId]?.value ?: return
        setUiState(jobId, candidatesUiState.copy(showHeaderBanner = false))
    }

    private fun getJobTitleList(success: ((Map<Int, JobTitleInfo>) -> Unit)? = {}) {
        requestData(
            request = {
                jobRepo.getRecommendJobTitleList()
            },
            success = { data ->
                data?.list?.takeIf { it.isNotEmpty() }?.apply {
                    val jobTitleList =
                        this.mapNotNull { it }.filter { it.jobTitle?.isNotBlank() == true }
                    val jobTitleMap =
                        jobTitleList.withIndex().associate { (index, value) -> index to value }
                    sendUiState {
                        copy(
                            jobTitleMap = jobTitleMap,
                            networkException = false,
                            getJobGuideEmpty = false,
                            apiExceptionMessage = "",
                            waitOpenJobList = data.guideJobs ?: listOf()
                        )
                    }
                    success?.invoke(jobTitleMap)
                }?:apply {
                    XLog.info(TAG, "getJobTitleList list is null:${data?.guideJobs?.size}")
                    sendUiState {
                        copy(
                            jobTitleMap = emptyMap(),
                            getJobGuideEmpty = true,
                            networkException = false,
                            apiExceptionMessage = "",
                            waitOpenJobList = data?.guideJobs ?: listOf()
                        )
                    }
                }
            },
            fail = {
                it.handleDefaultError(
                    handleApiException = {
                        if (uiStateFlow.value.jobTitleMap.isEmpty()) {
                            //之前的数据不为空就不刷到异常状态
                            sendUiState {
                                copy(
                                    networkException = it.message.isNullOrEmpty(), //服务端没有返回message,用networkException兜底
                                    getJobGuideEmpty = false,
                                    apiExceptionMessage = it.message?:"",
                                )
                            }
                        }
                    },
                    handleNetworkException = {
                        sendUiState {
                            copy(
                                networkException = true,
                                getJobGuideEmpty = false,
                                apiExceptionMessage = "",
                            )
                        }
                    },
                )
                XLog.error(TAG, "getJobTitleList fail: ${it.message}")
            }
        )
    }

    private fun enableNotification() {
        val settingRepository = SettingServiceRouter.getSettingRepository()
        requestData(
            request = {
                val appResult = settingRepository.notificationUpdateSwitch(12, 1)
                val emailResult = settingRepository.notificationUpdateSwitch(16, 1)
                if(appResult.isFailure){
                    appResult
                }else{
                    emailResult
                }
            },
            success = {
                T.ss(R.string.candidates_recommendation_empty_list_action_click.toResourceString())
                sendUiState {
                    copy(
                        notificationEnable = true
                    )
                }
            },
            fail = {
                it.handleDefaultError()
            }
        )
    }

    private fun refreshNotification() {
        val settingRepository = SettingServiceRouter.getSettingRepository()
        requestData(
            request = {
                val appSetting = settingRepository.getNotificationConfig(3)
                val emailSetting = settingRepository.getNotificationConfig(4)
                if (emailSetting.isFailure) {
                    emailSetting
                } else if (appSetting.isFailure) {
                    appSetting
                } else {
                    val list = arrayListOf<SettingCheckItemBean>()
                    emailSetting.getOrNull()?.list?.forEach {
                        if (it.type == 16L) {
                            list.add(it)
                        }
                    }
                    appSetting.getOrNull()?.list?.forEach {
                        if (it.type == 12L) {
                            list.add(it)
                        }
                    }
                    Result.success(CommonListResult(list = list))
                }
            },
            success = {
                sendUiState {
                    copy(
                        notificationEnable = it?.list?.size == 2 && it.list?.filter { it.status == 1 }?.size == 2
                    )
                }
            },
            fail = {
                it.handleDefaultError()
            }
        )
    }

    private fun getCandidatesList1(
        jobId: String,
        requestType: RequestType,
        isRefresh: Boolean,
        needSkip: Boolean,
        // 是否是切换子tab触发的请求
        isSwitchRequestType: Boolean = false
    ): Job? {
        if (needSkip) {
            return null
        }
        val candidatesUiState = uiStateFlow.value.candidatesListUiStateMap[jobId]?.value ?: return null
        var pageIndex = candidatesUiState.pageIndex
        if (isRefresh) {
            pageIndex = getDefaultPage()
        }
        return requestData(
            enableLoadState = false,
            request = {
                bossRepo.getRecommendCandidateList(jobId, requestType, pageIndex, getDefaultPageSize())
            },
            success = { result ->
                result?.run {
                    pageIndex++
                    val resultState = if (isRefresh) {
                        candidatesUiState.copy(
                            hasRequested = true,
                            pageIndex = pageIndex,
                            listState = candidatesUiState.listState.refreshSuccess(
                                result.getInsertedList(),
                                result.hasMore
                            ),
                            requestType = requestType
                        )
                    } else {
                        candidatesUiState.copy(
                            hasRequested = true,
                            pageIndex = pageIndex,
                            listState = candidatesUiState.listState.loadMoreSuccess(
                                result.getInsertedList(),
                                result.hasMore
                            ),
                            requestType = requestType
                        )
                    }
                    setUiState(jobId, resultState)
                    if (isSwitchRequestType) {
                        pointSubListClick(jobId, lid, requestType)
                    }
                }
            },
            fail = {
                val resultState = if (isRefresh) {
                    candidatesUiState.copy(
                        hasRequested = true,
                        listState = candidatesUiState.listState.refreshFail(),
                        requestType = requestType
                    )
                } else {
                    candidatesUiState.copy(
                        hasRequested = true,
                        listState = candidatesUiState.listState.loadMoreFail(),
                        requestType = requestType
                    )
                }
                setUiState(jobId, resultState)
                if (isSwitchRequestType) {
                    pointSubListClick(jobId, "", requestType)
                }
                if (it is ApiException) {
                    if (it.code == CANDIDATES_LIST_MQTT_FAIL) {
                        sendStringLiveEvent(BossEventBusKey.UPDATE_JOB_LIST, "")
                    }
                }
                XLog.error(TAG, it.message)
            }
        )
    }

    private fun pointSubListClick(jobId: String, lid: String, requestType: RequestType) {
        PointHelper.reportPoint("f1tab-sublist-click"){
            addP(jobId)
            addP2(lid)
            addP3(
                when (requestType) {
                    RequestType.RECOMMEND -> "1"
                    RequestType.VIEWED -> "2"
                    RequestType.FAVORED -> "3"
                }
            )
        }
    }

    private fun setUiState(jobId: String, resultState: BossCandidatesListUiState) {
        val newResultState = BossCandidatesListUiState(
            hasRequested = resultState.hasRequested,
            pageIndex = resultState.pageIndex,
            listState = resultState.listState.copy(
                loadFinishText = R.string.boss_candidates_list_load_finish_tips.toResourceString()
            ),
            requestType = resultState.requestType,
            showHeaderBanner = resultState.showHeaderBanner,
        )
        sendUiState {
            val newMap = mutableMapOf<String,StateFlow<BossCandidatesListUiState>>()
            newMap.putAll(candidatesListUiStateMap)
            newMap[jobId] = MutableStateFlow(newResultState).also { it.update { newResultState } }
            copy(candidatesListUiStateMap = newMap)
        }
    }

    private fun onExtendCardButtonClick(bean: BossExtendBean) {
        when (bean.type) {
            BossListExtentType.VERIFY_USER_EMAIL -> {
                launcherOnIO {
                     SettingServiceRouter.getSettingRepository().sendEmailAuth().onSuccess {
                         T.ss(R.string.setting_sent_successfully)
                     }.onFailure {
                         T.ss(it.message)
                     }
                }
            }
            else->{
                ProtocolHelper.parseProtocol(bean.url)
            }
        }
    }

//    private fun getCandidatesList(jobId: String, notSkip: Boolean) {
//        launcherOnIO {
//            uiStateFlow.value.candidatesListUiStateMap[jobId]?.let { candidatesUiState ->
//                if (candidatesUiState.hasRequested && notSkip) {
//                    return@launcherOnIO
//                }
//                val pager = getCandidatesList(jobId)
//                sendUiState {
//                    val map = candidatesListUiStateMap.toMutableMap()
//                    map[jobId] = BossCandidatesListUiState(hasRequested = true, pagingData = pager)
//                    copy(candidatesListUiStateMap = map)
//                }
//            }
//        }
//    }


//    private suspend fun getCandidatesList(jobId: String) = pagerRequestPage(fail = {
//        if(it is ApiException){
//            if(it.code == CANDIDATES_LIST_MQTT_FAIL){
//                sendStringLiveEvent(BossEventBusKey.UPDATE_JOB_LIST,"")
//            }
//        }
//    }) { page, pageSize ->
//        bossRepo.getRecommendCandidateList(jobId, page, pageSize)
//    }

    private fun removeTopMatches(friendId: String) {
        uiStateFlow.value.jobTitleMap.forEach { (k, v) ->
            v.jobId?.let { jobId ->
                uiStateFlow.value.candidatesListUiStateMap[jobId]?.let {
                    val list = it.value.listState.list.toMutableList()
                    list.removeAll { item ->
                        item.cardType == BossF1CardType.CANDIDATE
                                && !item.candidate?.topMatchReason.isNullOrBlank()
                                && item.candidate?.friendId == friendId
                    }
                    sendUiState {
                        val map = candidatesListUiStateMap.toMutableMap()
                        map[jobId] = MutableStateFlow(it.value.copy(listState = it.value.listState.copy(list = list)))
                        copy(candidatesListUiStateMap = map)
                    }
                }
            }
        }
    }

    private fun removeGeekBeginnerGuideCard(@BossListExtentType type: Int) {
        uiStateFlow.value.jobTitleMap.forEach { (k, v) ->
            v.jobId?.let { jobId ->
                uiStateFlow.value.candidatesListUiStateMap[jobId]?.let {
                    val list = it.value.listState.list.toMutableList()
                    list.removeAll { item ->
                        item.cardType == BossF1CardType.BEGINNER_GUIDE_CARD && item.extend?.type == type
                    }
                    sendUiState {
                        val map = candidatesListUiStateMap.toMutableMap()
                        map[jobId] = MutableStateFlow(it.value.copy(listState = it.value.listState.copy(list = list)))
                        copy(candidatesListUiStateMap = map)
                    }
                }
            }
        }
    }
}

data class BossCandidatesUiState(
    val tabIndex: Int = 0,
    val jobTitleMap: Map<Int, JobTitleInfo> = mapOf(0 to JobTitleInfo("", "")),
    val candidatesListUiStateMap: Map<String, StateFlow<BossCandidatesListUiState>> = mapOf(),
    val networkException: Boolean = false, //网络异常
    val apiExceptionMessage: String = "", //获取职位列表接口异常
    val waitOpenJobList: List<GuideJobItem> = listOf(),  //1.04.4没有在线职位的时候展示的待开放职位
    val getJobGuideEmpty: Boolean = false, //没有推荐职位
    val notificationEnable: Boolean = false, //通知状态
) : IUiState

enum class RequestType {
    RECOMMEND,
    VIEWED,
    FAVORED
}

data class BossCandidatesListUiState(
    val hasRequested: Boolean = false,
    val pageIndex: Int = 0,
    val listState: XRefreshListState<BossF1ListBean>,
    val requestType: RequestType = RequestType.RECOMMEND,
    val showHeaderBanner: Boolean = false,
)

sealed class BossCandidatesUiIntent : IUiIntent {
    data object ResetTabPage : BossCandidatesUiIntent()
    data class RemoveTopMatchedCandidate(val friendId: String) : BossCandidatesUiIntent()
    data class SwitchTabPage(val tabIndex: Int) : BossCandidatesUiIntent()
    data class SwitchRequestType(val requestType: RequestType) : BossCandidatesUiIntent()
    data object HideBannerHeader: BossCandidatesUiIntent()
    data object Refresh : BossCandidatesUiIntent()
    data object LoadMore : BossCandidatesUiIntent()
    data class OnClickClose(val bean: BossF1ListBean) : BossCandidatesUiIntent()
    data object DeleteBeginnerVerifyEmail : BossCandidatesUiIntent()
    data class OnCardButtonClick(val bean: BossExtendBean) : BossCandidatesUiIntent()
    data object DeleteCompleteNameCard : BossCandidatesUiIntent()
    data object RefreshNotification : BossCandidatesUiIntent()
    data object EnableNotification : BossCandidatesUiIntent()
}

//如果某种原因mqtt消息未到，服务端在f1接口返回2031
const val CANDIDATES_LIST_MQTT_FAIL = 2031
const val SWITCH_TAB_0 = "switch_tab_0"