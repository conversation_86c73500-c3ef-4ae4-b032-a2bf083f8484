package com.twl.meeboss.boss.module.candidates.recommend

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.accompanist.pager.rememberPagerState
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.flow.GlobalFlowManager
import com.twl.meeboss.base.flow.RECOMMENDED_SUB_TAB
import com.twl.meeboss.base.main.router.BasePageRouter
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossEventBusKey
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.foundation.moduleservice.BossUserInfoService
import com.twl.meeboss.boss.module.candidates.recommend.components.BossCandidatePageNoOnlineJobComponent
import com.twl.meeboss.boss.module.candidates.recommend.components.BossCandidatesContent
import com.twl.meeboss.boss.module.candidates.recommend.components.BossF1ListContent
import com.twl.meeboss.boss.utils.BossPointReporter
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.state.XErrorContent
import com.twl.meeboss.core.ui.utils.ellipsizeText
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @author: 冯智健
 * @date: 2024年07月11日 11:50
 * @description:
 */
@Composable
fun BossCandidatesPage(
    vm: BossCandidatesViewModel = viewModel(),
    setFitSystemWindow:(Boolean)->Unit = {},
    onGetGuideJobCount:(Int)->Unit = {},
) {
    val owner = LocalLifecycleOwner.current
    val context = LocalContext.current
    val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
    val candidatesTabData by GlobalFlowManager.candidatesTabFlow.collectAsStateWithLifecycle()
    val tabTitleList = uiState.jobTitleMap.values.mapNotNull { it.jobTitle }
    val waitOpenJobList = uiState.waitOpenJobList
    val pageState = rememberPagerState(initialPage = uiState.tabIndex)
    val coroutineScope = rememberCoroutineScope()
    var toastState by remember { mutableStateOf<String?>(null) }

    owner.liveEventBusObserve<Any>(BossEventBusKey.UPDATE_JOB_STATUS) {
        if (uiState.jobTitleMap.isEmpty()) {
            vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
        }
    }
    LaunchedEffect(owner) {
        owner.liveEventBusObserve(BossEventBusKey.UPDATE_JOB_STATUS) { _: Int ->
            vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
        }
        owner.liveEventBusObserve(BossEventBusKey.POST_JOB_SUCCESS) { _: String ->
            vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
        }
        owner.liveEventBusObserve(BossEventBusKey.DELETE_JOB_SUCCESS) { _: String ->
            vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
        }
        owner.liveEventBusObserve(BossEventBusKey.UPDATE_JOB_LIST) { _: String ->
            vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
        }
        owner.liveEventBusObserve(SWITCH_TAB_0) { _: String ->
            coroutineScope.launch {
                pageState.animateScrollToPage(0)
            }
        }
        owner.liveEventBusObserve(EventBusKey.EMAIL_VERIFIED_SUCCESS){_:String->
            vm.sendUiIntent(BossCandidatesUiIntent.DeleteBeginnerVerifyEmail)
        }
        owner.liveEventBusObserve(EventBusKey.BOSS_NOTIFICATION_CHANGED) { type: String ->
            if (type == "12" || type == "16") {
                vm.sendUiIntent(BossCandidatesUiIntent.RefreshNotification)
            }
        }
        owner.liveEventBusObserve(EventBusKey.NOT_INTERESTED_IN_CANDIDATE) { friendId: String ->
            vm.sendUiIntent(BossCandidatesUiIntent.RemoveTopMatchedCandidate(friendId))
        }
        BossUserInfoService.bossUserInfo.observe(owner) {
            vm.sendUiIntent(BossCandidatesUiIntent.DeleteCompleteNameCard)
        }
    }

    LaunchedEffect(toastState) {
        delay(150) // 自动滚动防抖
        if (!toastState.isNullOrBlank()) {
            T.ssd(toastState)
            toastState = null
        }
    }

    LaunchedEffect(pageState.currentPage) {
        if (uiState.tabIndex != pageState.currentPage) {
            val titleInfo = uiState.jobTitleMap[pageState.currentPage]
            if (titleInfo?.jobTitle != null && !titleInfo.salaryDesc.isNullOrBlank()) {
                val title = context.ellipsizeText(titleInfo.jobTitle)
                val salary = context.ellipsizeText(titleInfo.salaryDesc)
                toastState = "$title\n$salary"
            }
        }
        vm.sendUiIntent(BossCandidatesUiIntent.SwitchTabPage(pageState.currentPage))
    }

    LaunchedEffect(waitOpenJobList) {
        snapshotFlow { waitOpenJobList.size }.collectLatest {
            onGetGuideJobCount(it)
        }
    }
    LaunchedEffect(candidatesTabData.subTabIndex) {
        if (candidatesTabData.subTabIndex == RECOMMENDED_SUB_TAB) {
            vm.sendUiIntent(BossCandidatesUiIntent.SwitchRequestType(RequestType.RECOMMEND))
            GlobalFlowManager.resetCandidatesTabState()
        }
    }

    val stateMap = uiState.candidatesListUiStateMap
    when {
        uiState.networkException -> {
            setFitSystemWindow(true)
            XErrorContent {
                vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
            }
        }
        uiState.apiExceptionMessage.isNotEmpty() -> {
            XErrorContent(text = uiState.apiExceptionMessage) {
                vm.sendUiIntent(BossCandidatesUiIntent.ResetTabPage)
            }
        }
        uiState.getJobGuideEmpty -> { //没有在线职位或者推荐职位异常
            setFitSystemWindow(false)
            BossCandidatePageNoOnlineJobComponent(
                jobInfo = if (waitOpenJobList.isEmpty()) null else waitOpenJobList[0]
            )
        }
        else -> {
            setFitSystemWindow(true)
            BossCandidatesContent(
                options = tabTitleList,
                pageState,
                onAddClick = {
                    BasePageRouter.jumpToMainActivity(
                        context = context,
                        tabIndex = 2,
                        openNew = false
                    )
                },
                onPostJobClick = {
                    BossPointReporter.bossClickPostAJob(3)
                    BossPageRouter.jumpToBossPostJobActivity(context)
                }
            ) { page ->
                val jobId = uiState.jobTitleMap[page]?.jobId.notNull()
                val candidateList = stateMap[jobId] ?: return@BossCandidatesContent
                val contentState by candidateList.collectAsState()
                val requestType = contentState.requestType
                val showHeaderBanner = contentState.showHeaderBanner
                LaunchedEffect(requestType, showHeaderBanner) {
                    if (requestType != RequestType.RECOMMEND && showHeaderBanner) {
                        delay(1000)
                        vm.sendUiIntent(BossCandidatesUiIntent.HideBannerHeader)
                    }
                }

                BossF1ListContent(
                    emptyText = stringResource(id = when (requestType) {
                        RequestType.RECOMMEND -> R.string.boss_candidates_list_no_matching_candidates
                        RequestType.VIEWED -> R.string.candidates_viewed_empty_text
                        RequestType.FAVORED -> R.string.candidates_saved_empty_text
                    }),
                    listState = contentState.listState,
                    notificationEnable = uiState.notificationEnable,
                    requestType = requestType,
                    showHeaderBanner = showHeaderBanner,
                    onNotificationClick = {
                        vm.sendUiIntent(BossCandidatesUiIntent.EnableNotification)
                    },
                    onRefresh = {
                        vm.sendUiIntent(BossCandidatesUiIntent.Refresh)
                    },
                    onLoadMore = {
                        vm.sendUiIntent(BossCandidatesUiIntent.LoadMore)
                    },
                    onClickClose = {
                        vm.sendUiIntent(BossCandidatesUiIntent.OnClickClose(it))
                    },
                    emptyButtonClick = {
                        vm.sendUiIntent(BossCandidatesUiIntent.Refresh)
                    }, onButtonClick = {
                        vm.sendUiIntent(BossCandidatesUiIntent.OnCardButtonClick(it))
                    },
                    onHeaderClick = {
                        vm.sendUiIntent(BossCandidatesUiIntent.SwitchRequestType(it))
                    }
                )
            }
        }
    }

}

