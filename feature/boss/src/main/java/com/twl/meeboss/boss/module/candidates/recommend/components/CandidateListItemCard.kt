package com.twl.meeboss.boss.module.candidates.recommend.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.model.enumeration.InteractionType
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.module.candidates.recommend.preview.CandidatesListItemPreviewParameterProvider
import com.twl.meeboss.core.ui.theme.COLOR_484848
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.geek.export.GeekServiceRouter

/**
 * @author: 冯智健
 * @date: 2024年07月11日 15:15
 * @description: 候选人卡片组件，支持根据 topMatchReason 动态显示渐变背景
 */
@Preview
@Composable
fun CandidateListItemCard(
    @PreviewParameter(CandidatesListItemPreviewParameterProvider::class)
    item: CandidateItemResult,
    interactionType: @InteractionType Int = InteractionType.NONE,
    showTalent: Boolean = true,
    border: BorderStroke? = null,
    onItemClick: () -> Unit = {}
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .noRippleClickable(onClick = onItemClick),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        shape = RoundedCornerShape(12.dp),
        border = border
    ) {
        // 根据 topMatchReason 动态设置背景样式
        val columnModifier = if (item.topMatchReason.isNullOrBlank()) {
            // 无 topMatchReason 时使用默认样式
            Modifier.padding(vertical = 20.dp, horizontal = 16.dp)
        } else {
            Modifier
                .background(
                    brush = Brush.verticalGradient(
                        colorStops = arrayOf(
                            0.0f to Color(0x99FFF9C9),
                            0.5f to Color(0x00FFF9C9),
                            1.0f to Color(0x00FFF9C9)
                        )
                    )
                )
                .padding(vertical = 20.dp, horizontal = 16.dp)
        }

        Column(modifier = columnModifier) {
            CandidateCardHeadArea(item = item)
            CandidateCardSummaryArea(item = item)
            CandidateCardDescArea(showTalent = showTalent, item = item)
            item.recommendReason?.apply {
                desc?.takeIf { it.isNotBlank() }?.let {
                    GeekServiceRouter.GetJobCardHighLightArea(Modifier.padding(top = 8.dp), tag, it)
                }
            }
            // Top matches 区域渲染
            item.topMatchReason?.takeIf { it.isNotBlank() }?.let { reason ->
                CandidateTopMatchReason(reason)
            }
            CandidateCardFooterArea(item, interactionType)
        }
    }
}

@Composable
private fun CandidateTopMatchReason(reason: String) {
    val density = LocalDensity.current
    val iconSize = with(density) { 14.sp.toDp() }

    val inlineContent = mapOf(
        "topMatchTag" to InlineTextContent(
            placeholder = Placeholder(
                width = 105.sp,
                height = 20.sp,
                placeholderVerticalAlign = PlaceholderVerticalAlign.Center
            )
        ) {
            // Top matches 标签内容
            Box(
                modifier = Modifier
                    .background(
                        color = Color(0xFFFFF692), // 黄色背景
                        shape = RoundedCornerShape(4.dp) // 圆角矩形
                    )
                    .padding(horizontal = 6.dp, vertical = 3.dp)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painter = painterResource(id = R.drawable.boss_icon_candidate_item_top_matches),
                        contentDescription = null,
                        modifier = Modifier.size(iconSize),
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = stringResource(R.string.recommended_list_top_matches_icon),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = COLOR_484848
                    )
                }
            }
        }
    )

    Text(
        text = buildAnnotatedString {
            // 插入 Top matches 标签
            appendInlineContent(
                id = "topMatchTag",
                alternateText = stringResource(R.string.message_list_enterance_top_matches_title)
            )

            append(reason)
        },
        inlineContent = inlineContent,
        modifier = Modifier.padding(top = 12.dp),
        color = COLOR_484848,
        fontSize = 12.sp,
        lineHeight = 1.5.em,
        maxLines = 2,
        overflow = TextOverflow.Ellipsis,
    )
}
