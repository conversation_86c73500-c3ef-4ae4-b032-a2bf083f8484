package com.twl.meeboss.boss.module.candidates.detail

import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.geometry.Rect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.constants.BUNDLE_INT
import com.twl.meeboss.base.constants.EventBusKey
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.eventbus.liveEventBusObserve
import com.twl.meeboss.base.eventbus.sendStringLiveEvent
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.getJoinString
import com.twl.meeboss.base.ktx.showRightMoreDialog
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.export.BossRouterPath
import com.twl.meeboss.boss.export.model.CandidatePageFrom
import com.twl.meeboss.boss.module.candidates.detail.dialog.BossCandidateResumeJobSelectBottomSheet
import com.twl.meeboss.boss.module.candidates.detail.dialog.TopMatchesGuideDialog
import com.twl.meeboss.chat.export.ChatPageRouter
import com.twl.meeboss.chat.export.ChatServiceRouter
import com.twl.meeboss.chat.export.constant.ReportScene
import com.twl.meeboss.common.ktx.notNull
import com.twl.meeboss.core.ui.utils.DisposableEffectWithLifecycle
import com.twl.meeboss.core.ui.utils.showSafely
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay

/**
 * @author: 冯智健
 * @date: 2024年07月18日 15:15
 * @description:
 */
@AndroidEntryPoint
@RouterPage(path = [BossRouterPath.BOSS_CANDIDATE_RESUME_PAGE])
class BossCandidateResumeActivity : BaseMviActivity<BossCandidateResumeViewModel>() {

    private var securityId = ""

    private var lineMarkerType : Int? = null

    private var candidatePageFrom = CandidatePageFrom.OTHER

    override val viewModel: BossCandidateResumeViewModel by viewModels()

    override fun preInit(intent: Intent) {
        securityId = intent.getStringExtra(BossPageRouter.BUNDLE_SECURITY_ID) ?: ""
        val lineMarkerType = intent.getIntExtra(BossPageRouter.BUNDLE_LINE_MARKER_TYPE, -1)
        if(lineMarkerType != -1){
            this.lineMarkerType = lineMarkerType
        }
        candidatePageFrom = intent.getIntExtra(BUNDLE_INT, CandidatePageFrom.OTHER)
    }

    override fun initData() {
        liveEventBusObserve(EventBusKey.BOSS_ADD_FRIEND_SUCCESS) { securityId: String ->
            if (viewModel.uiStateFlow.value.candidateResume?.securityId == securityId) { //添加好友成功，更新数据
                viewModel.sendUiIntent(BossCandidateResumeUiIntent.GetCandidateResume(securityId, candidatePageFrom, lineMarkerType))
                //因为geekDetail接口返回的securityId和外面传进来的securityId不同,所以只能在geekDetail页做一次中转
                sendStringLiveEvent(
                    EventBusKey.BOSS_CANDIDATE_DETAIL_ADD_FRIEND_SUCCESS,
                    this.securityId
                )
            }
        }

    }

    @Composable
    override fun ComposeContent() {
        val vm: BossCandidateResumeViewModel = viewModel()
        val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()
        val loadState by vm.loadUiStateFlow.collectAsStateWithLifecycle()
        var tips by remember { mutableStateOf(false) }

        LaunchedEffect(loadState.loadState) {
            if (loadState.loadState is LoadState.Success) {
                delay(300)
                tips = !uiState.candidateResume?.topMatchReason.isNullOrBlank() && !vm.hasShownTip()
            }
        }

        DisposableEffectWithLifecycle(
            onCreate = {
                vm.sendUiIntent(BossCandidateResumeUiIntent.GetCandidateResume(securityId, candidatePageFrom, lineMarkerType))
            }
        )
        if (uiState.finishPage) {
            finish()
        }
        CandidateResumePage(
            loadState = loadState.loadState,
            candidateResume = uiState.candidateResume,
            collected = uiState.collected,
            candidatePageFrom = candidatePageFrom,
            showFloatingTip = tips,
            retryOnClick = {
                vm.sendUiIntent(BossCandidateResumeUiIntent.GetCandidateResume(securityId, candidatePageFrom, lineMarkerType))
            },
            collectOnClick = {
                vm.sendUiIntent(
                    BossCandidateResumeUiIntent.CollectCandidate(
                        securityId,
                        !uiState.collected
                    )
                )
            },
            chatOnClick = {
                onChatClick()
            },
            onMoreClick = this::showRightMoreDialog,
            onTalentPoolCardClick = this::showTalentPoolDialog,
            onTopMatchesClick = {
                TopMatchesGuideDialog().showSafely(this)
            },
            onFloatingTipDismiss = {
                tips = false
            }
        )
    }

    private fun showTalentPoolDialog(rect: Rect){
        showRightMoreDialog(rect,R.layout.boss_candidate_detail_talent_pool)
    }

    private fun showRightMoreDialog(rect: Rect) {
        showRightMoreDialog(
            rect,
            R.layout.boss_candidate_detail_more_dialog
        ) { contentView, popupWindow ->
            contentView.findViewById<View>(R.id.clReport).setOnClickListener {
                popupWindow.dismiss()
                ChatPageRouter.jumpToChatReportActivity(
                    this@BossCandidateResumeActivity,
                    securityId,
                    ReportScene.CANDIDATE_DETAIL
                )
            }
        }
    }

    private fun onChatClick() {
        if (candidatePageFrom == CandidatePageFrom.CHAT) {
            onBackPressed()
            return
        }

        viewModel.uiStateFlow.value.candidateResume?.run {
            if (this.securityId.isNullOrBlank()) {
                BossCandidateResumeJobSelectBottomSheet.newInstance(
                    friendId = this.friendId,
                    friended = this.friended ?: false,
                    candidateName = getJoinString(
                        listOf(this.baseInfo?.firstName, this.baseInfo?.lastName),
                        " "
                    ) ?: "",
                ).showSafely(activity = this@BossCandidateResumeActivity)
            } else {
                ChatServiceRouter.checkStartChat(
                    context = this@BossCandidateResumeActivity,
                    securityId = this.securityId.notNull(),
                    friendId = this.friendId.notNull(),
                    friendIdentity = UserConstants.GEEK_IDENTITY,
                    isFriend = this.friended ?: false,
                    source = ChatSource.Detail,
                    addFriendSource = if (this.topMatchReason.isNullOrBlank()) 0 else 1
                )
            }

        }
    }
}