package com.twl.meeboss.boss.module.job.post.utils

import android.app.Activity
import android.content.Intent
import androidx.fragment.app.FragmentActivity
import com.twl.meeboss.base.components.dialog.CommonDataType
import com.twl.meeboss.base.components.dialog.DialogHelper
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.constants.BOSS_EDIT_JOB_DESCRIPTION
import com.twl.meeboss.base.constants.BOSS_EDIT_JOB_PAY
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION_HAS_GENERATED
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION_IS_APPLIED
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.ktx.toMoneyString
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.boss.module.job.model.EDU_LEVEL_KEY
import com.twl.meeboss.boss.module.job.model.EMPLOYEE_LOCATION_KEY
import com.twl.meeboss.boss.module.job.model.EMPLOYMENT_TYPE_KEY
import com.twl.meeboss.boss.module.job.model.EXP_LEVEL_KEY
import com.twl.meeboss.boss.module.job.model.EditJobLocalData
import com.twl.meeboss.boss.module.job.model.JOB_DESC_KEY
import com.twl.meeboss.boss.module.job.model.JOB_LOCATION_KEY
import com.twl.meeboss.boss.module.job.model.JOB_SKILLS_KEY
import com.twl.meeboss.boss.module.job.model.JOB_TITLE_KEY
import com.twl.meeboss.boss.module.job.model.JobEditItem
import com.twl.meeboss.boss.module.job.model.JobEditItemFactory.Companion.toEmployeeLocationItem
import com.twl.meeboss.boss.module.job.model.JobEditItemFactory.Companion.toJobLocationItem
import com.twl.meeboss.boss.module.job.model.LANGUAGE_KEY
import com.twl.meeboss.boss.module.job.model.PAY_KEY
import com.twl.meeboss.boss.module.job.model.WORKPLACE_TYPE_KEY
import com.twl.meeboss.boss.module.job.model.VISA_SPONSORSHIP
import com.twl.meeboss.boss.module.job.post.BossEditJobPayActivity
import com.twl.meeboss.boss.module.job.post.dialog.JobLocationBottomSheet
import com.twl.meeboss.boss.module.job.post.dialog.JobSkillBottomSheet
import com.twl.meeboss.boss.module.job.post.dialog.JobTitleBottomSheet
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.components.language.MultiSelectLanguageBottomSheet
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString

/**
 * 职位编辑页面交互帮助类
 * 所有弹窗，跳转页面获取数据都在这里进行，回调也在这里处理，方便首善发布职位、发布职位、编辑职位等页面复用
 */
class BossEditJobInteractHelper(
    private val activity: FragmentActivity,
    private val from: String,
    private val listProvider: () -> List<JobEditItem>,
    private val localDataProvider: () -> EditJobLocalData,
    private val callback: (List<JobEditItem>, EditJobLocalData) -> Unit,
) {

    private val dialogHelper = DialogHelper(activity)

    fun onItemClick(item: JobEditItem) {
        when (item.key) {
            JOB_TITLE_KEY -> {
                //点击了职位名称
                showJobTitleSelect(item)
            }

            EMPLOYMENT_TYPE_KEY -> {
                //点击了聘用类型
                showSelectEmploymentTypeDialog(item)
            }

            WORKPLACE_TYPE_KEY -> {
                //点击了工作地点类型
                showSelectWorkplaceTypeDialog(item)
            }

            JOB_LOCATION_KEY -> {
                //点击了工作地点
                showJobLocationSheet(item)
            }
            //点击了工作地点
            EMPLOYEE_LOCATION_KEY -> {
                showEmployeeLocationSheet(item)
            }

            PAY_KEY -> {
                //点击了薪资
                showSalaryActivity()
            }

            JOB_DESC_KEY -> {
                //点击了职位描述
                showJobDescriptionSheet(item)
            }

            EDU_LEVEL_KEY -> {
                //点击了学历要求
                showEduLevelDialog(item)
            }

            EXP_LEVEL_KEY -> {
                //点击了经验要求
                showExpLevelDialog(item)
            }

            JOB_SKILLS_KEY -> {
                //点击了技能要求
                showJobSkillsSheet(item)
            }
            LANGUAGE_KEY->{
                showSelectLanguageDialog(item)
            }
            VISA_SPONSORSHIP -> {
                showVisaSponsorshipDialog(item)
            }
        }

    }

    fun onDeleteItem(item: JobEditItem) {
        when (item.key){
            VISA_SPONSORSHIP -> {
                deleteVisaSponsorship()
            }
        }
    }

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                BOSS_EDIT_JOB_PAY -> {
                    val minSalary = data?.getLongExtra("minSalary", 0)
                    val maxSalary = data?.getLongExtra("maxSalary", 0)
                    val salaryType = data?.getLongExtra("salaryType", 0) ?: 0L
                    val salaryTypeName = data?.getStringExtra("salaryTypeName") ?: ""
                    val benefits = data?.getSerializableExtra("benefits") as? ArrayList<OptionBean> ?: listOf()

                    val unitString = com.twl.meeboss.core.ui.R.string.common_salary_unit.toResourceString()
                    val showSalaryStr = if (minSalary == maxSalary) {
                        "${unitString}${minSalary.toMoneyString()} ${salaryTypeName}"
                    } else {
                        "${unitString}${minSalary.toMoneyString()}-${unitString}${maxSalary.toMoneyString()} ${salaryTypeName}"
                    }
                    val list = listProvider().updateList("pay", showSalaryStr, listOf(minSalary.toString(), maxSalary.toString()))
                    val localData = localDataProvider().copy(
                        salaryType = OptionBean(code = salaryType, name = salaryTypeName),
                        salaryUnit = OptionBean(code = LocalManager.getSalaryUnitParamValue().toLong(), name = LocalManager.getSalaryUnitNameValue()),
                        minSalary = minSalary ?: 0,
                        maxSalary = maxSalary ?: 0,
                        benefits = benefits
                    )
                    callback(list, localData)
                }

                BOSS_EDIT_JOB_DESCRIPTION -> {
                    val jobDesc = data?.getStringExtra(BUNDLE_JOB_DESCRIPTION)
                    val jobDescHasGenerate = data?.getBooleanExtra(BUNDLE_JOB_DESCRIPTION_HAS_GENERATED,false)?:false
                    val jobDescIsApplied = data?.getBooleanExtra(BUNDLE_JOB_DESCRIPTION_IS_APPLIED, false)?:false

                    callback(listProvider().updateList(JOB_DESC_KEY, jobDesc?:""), localDataProvider().copy(jobDesc = jobDesc, jobDescStyle = "", jobDescHasGenerate = jobDescHasGenerate, jobDescIsApplied = jobDescIsApplied))

                }
            }
        }

    }

    private fun showJobTitleSelect(item: JobEditItem) {
        val currentJobTitle = localDataProvider().jobTitle.name
        val currentJobDescHasGenerate = localDataProvider().jobDescHasGenerate
        JobTitleBottomSheet.newInstance(HighlightBean()) {
            callback(listProvider().updateList(item.key, it.name), localDataProvider().copy(jobTitle = it, jobDescHasGenerate = if (currentJobTitle != it.name) false else currentJobDescHasGenerate))
        }.showSafely(activity)
    }


    /**
     * 显示选择聘用类型弹框
     */
    private fun showSelectEmploymentTypeDialog(item: JobEditItem) {
        dialogHelper.showMultiChoiceDialog(CommonDataType.EMPLOYMENT_TYPE, localDataProvider().employmentType.map { it.name }) {
            val showString = it.joinToString(" ,") { bean -> bean.name }
            callback(listProvider().updateList(item.key, showString), localDataProvider().copy(employmentType = it))
        }
    }

    /**
     * 选择工作地点类型
     */
    private fun showSelectWorkplaceTypeDialog(item: JobEditItem) {
        dialogHelper.showSingleChoiceDialog(CommonDataType.WORKPLACE_TYPE, item.content ?: "") {type->
            val items = listProvider().toMutableList()
            val itemKeys = items.map {item-> item.key }
            val data = if (type.code == DefaultValueConstants.FULL_REMOTE_CODE) { //这次选择的是Full Remote类型
                val position = itemKeys.indexOf(JOB_LOCATION_KEY)
                if (position >= 0) { //如果包含，说明是jobLocation
                    items[position] = items[position].toEmployeeLocationItem() //修改item
                }
                if(!localDataProvider().jobLocation.isNullOrEmpty()){
                    T.ss(R.string.job_enter_employee_location_toast)
                }
                localDataProvider().copy(
                    workplaceType = type,
                    jobLocation = mutableListOf()//选择工作类型以后，下面的地里位置要清空
                )
            } else {
                val position = itemKeys.indexOf(EMPLOYEE_LOCATION_KEY)
                if (position >= 0) { //如果包含，说明是employeeLocation
                    items[position] = items[position].toJobLocationItem() //修改item
                    if(!localDataProvider().jobLocation.isNullOrEmpty()){
                        T.ss(R.string.job_enter_job_location_toast)
                    }
                    localDataProvider().copy(
                        workplaceType = type,
                        jobLocation = mutableListOf()//选择工作类型以后，下面的地里位置要清空

                    )
                }else{
                    localDataProvider().copy(
                        workplaceType = type
                    )
                }

            }
            callback(items.updateList(item.key, type.name), data)
        }
    }

    private fun showSalaryActivity() {
        localDataProvider().run {
            BossEditJobPayActivity.intent(
                context = activity,
                optionBeans = ArrayList(),
                minSalary = minSalary,
                maxSalary = maxSalary,
                salaryType = salaryType.code,
                jobTitle = jobTitle.name,
                jobLocation = jobLocation?.joinToString("; ") { it.name } ?: "",
                benefits = ArrayList(benefits)
            )
        }

    }

    private fun showJobDescriptionSheet(item: JobEditItem) {
        val currentJobTitle = localDataProvider().jobTitle.name
        val currentJobDescription = localDataProvider().jobDesc
        BossPageRouter.jumpToBossGenerateJobDescriptionActivity(
            context = activity,
            jobTitle = currentJobTitle,
            originJobDescription = currentJobDescription,
            hasGenerated = localDataProvider().jobDescHasGenerate,
            isApplied = localDataProvider().jobDescIsApplied,
            from = from
        )
    }


    /**
     * 选择学历要求
     */
    private fun showEduLevelDialog(item: JobEditItem) {
        dialogHelper.showSingleChoiceDialog(CommonDataType.EDUCATION_LEVEL, item.content ?: "") {
            callback(listProvider().updateList(item.key, it.name), localDataProvider().copy(eduLevel = it))
        }
    }

    /**
     * 选择经验要求
     */
    private fun showExpLevelDialog(item: JobEditItem) {
        dialogHelper.showMultiChoiceDialog(CommonDataType.WORK_EXPERIENCE_JOB, localDataProvider().expLevel?.map { it.name } ?: listOf(),
            exclusiveItemCode = DefaultValueConstants.N_A_CODE,
        ) {
            val showString = it.joinToString(" ,") { bean -> bean.name }
            callback(listProvider().updateList(item.key, showString), localDataProvider().copy(expLevel = it))
        }
    }

    /**
     * 选择职位地址
     */
    private fun showJobLocationSheet(item: JobEditItem) {
        JobLocationBottomSheet.newInstance(localDataProvider().jobLocation ?: mutableListOf(), isEmployeeLocation = false) {
            val showString = it.joinToString(" ;") { bean -> bean.name }
            callback(listProvider().updateList(item.key, showString), localDataProvider().copy(jobLocation = it))
        }.showSafely(activity)
    }

    /**
     * 选择Full Remote的employee地址
     */
    private fun showEmployeeLocationSheet(item: JobEditItem) {
        JobLocationBottomSheet.newInstance(localDataProvider().jobLocation ?: mutableListOf(), isEmployeeLocation = true) {
            val showString = it.joinToString(" ;") { bean -> bean.name }
            callback(listProvider().updateList(item.key, showString), localDataProvider().copy(jobLocation = it))
        }.showSafely(activity)
    }

    /**
     * 选择技能要求
     */
    private fun showJobSkillsSheet(item: JobEditItem) {
        JobSkillBottomSheet.newInstance(localDataProvider().jobSkills ?: mutableListOf(), jobTitle = localDataProvider().jobTitle) {
            val showString = it.joinToString(" ,") { bean -> bean.name }
            callback(listProvider().updateList(item.key, showString), localDataProvider().copy(jobSkills = it))
        }.showSafely(activity)
    }

    private fun showSelectLanguageDialog(item: JobEditItem) {
        MultiSelectLanguageBottomSheet.newInstance(selectedList = localDataProvider().languages){
            val showString = it.joinToString(" ,") { bean -> bean.name }
            callback(listProvider().updateList(item.key, showString), localDataProvider().copy(languages = it))
        }.showSafely(activity)
    }

    private fun showVisaSponsorshipDialog(item: JobEditItem) {
        dialogHelper.showSingleChoiceDialog(CommonDataType.BOSS_VISA_SPONSORSHIP, item.content ?: "") {
            callback(listProvider().updateList(item.key, it.name), localDataProvider().copy(visaSponsorship = it.code.toInt()))
        }
    }

    private fun deleteVisaSponsorship() {
        callback(listProvider().updateList(VISA_SPONSORSHIP, ""), localDataProvider().copy(visaSponsorship = 0))
    }

    private fun List<JobEditItem>.updateList(key: String, value: String, localTempList: List<String> = listOf()): List<JobEditItem> {
        val newList = mutableListOf<JobEditItem>()
        this.forEach {
            newList.add(it.copy().also { n ->
                if (n.key == key) {
                    n.content = value
                    n.localTempList = localTempList
                }
            })
        }
        return newList
    }
}