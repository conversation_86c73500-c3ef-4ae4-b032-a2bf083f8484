package com.twl.meeboss.boss.module.job.ktx

import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.model.CityBean
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.boss.BossCompanyInfoBean
import com.twl.meeboss.base.model.common.CommonBrandBean
import com.twl.meeboss.base.model.common.CommonTypeBean
import com.twl.meeboss.export_share.model.JobDetailJobInfo
import com.twl.meeboss.boss.model.JobParseResult
import com.twl.meeboss.boss.module.candidates.recommend.model.GuideJobItem
import com.twl.meeboss.boss.module.job.model.EditJobLocalData
import com.twl.meeboss.common.ktx.formatMoney
import com.twl.meeboss.common.ktx.notNull

internal fun JobDetailJobInfo.toBossEditData(): EditJobLocalData {
    return EditJobLocalData(
        jobTitle = HighlightBean(
            name = jobTitle.notNull(),
            code = jobCode?.toLongOrNull() ?: 0L
        ),
        employmentType = jobType?.map {
            it.toOptionBean()
        } ?: listOf(),
        workplaceType = locationType.toOptionBean(),
        salaryType = salaryType.toOptionBean(),
        salaryUnit = salaryUnit.toOptionBean(),
        minSalary = minSalary ?: 0L,
        maxSalary = maxSalary ?: 0L,
        jobLocation = address?.map {
            CityBean(it.code ?: 0L, it.name.notNull())
        }?.toMutableList(),
        jobDesc = jobDesc.notNull(),
        jobDescStyle = jobDescStyle.notNull(),
        eduLevel = eduLevel.toOptionBean(),
        expLevel = expLevel?.map { it.toOptionBean() } ?: listOf(),
        jobSkills = skills?.map {
            HighlightBean(name = it.name.notNull(), code = it.code ?: 0L)
        }?.toMutableList(),
        languages = languages,
        benefits = benefits,
        visaSponsorship = visaSponsored ?: 0
    )
}

internal fun JobParseResult.toBossEditData(): EditJobLocalData? {
    if (jobDetail == null) {
        return null
    }
    val jobLocalData = EditJobLocalData(
        jobTitle = HighlightBean(
            name = jobDetail.jobTitle.notNull(),
            code = jobDetail.jobCode?.toLongOrNull() ?: 0L
        ),
        employmentType = jobDetail.jobType?.map {
            it.toOptionBean()
        } ?: listOf(),
        workplaceType = jobDetail.locationType.toOptionBean(),
        salaryType = jobDetail.salaryType.toOptionBean(),
        salaryUnit = jobDetail.salaryUnit.toOptionBean(),
        minSalary = jobDetail.minSalary ?: 0L,
        maxSalary = jobDetail.maxSalary ?: 0L,
        jobLocation = jobDetail.address?.map {
            CityBean(it.code ?: 0L, it.name.notNull())
        }?.toMutableList(),
        jobDesc = jobDetail.jobDesc.notNull(),
        jobDescStyle = jobDetail.jobDescStyle.notNull(),
        eduLevel = jobDetail.eduLevel.toOptionBean(),
        expLevel = jobDetail.expLevel?.map { it.toOptionBean() } ?: listOf(),
        jobSkills = jobDetail.skills?.map {
            HighlightBean(name = it.name.notNull(), code = it.code ?: 0L)
        }?.toMutableList(),
        languages = jobDetail.languages
    )
    if (bossCompanyDetail != null) {
        jobLocalData.apply {
            firstName = bossCompanyDetail.firstName
            lastName = bossCompanyDetail.lastName
            companyInfo = bossCompanyDetail.toCommonBrandBean()
            bossPosition = bossCompanyDetail.bossPosition
        }
    }
    return jobLocalData
}

internal fun BossCompanyInfoBean.toCommonBrandBean():CommonBrandBean {
    return CommonBrandBean(
        companyId = companyId,
        companyName = companyName,
        companyShortName = companyShortName,
        industry = industry,
        sizeType = sizeType,
        companyDesc = companyDesc,
        website = website,
        companyLogo = companyLogo,
        companyLogoThumbnail = companyLogoThumbnail,
        companyEnvironments = companyEnvironments,
        companyEnvironmentThumbnails = companyEnvironmentThumbnails,
    )
}

internal fun CommonTypeBean?.toOptionBean(): OptionBean {
    return OptionBean(code = this?.code ?: 0L, name = this?.name.notNull())
}

/**
 * v1.04.4 F1空状态页职位卡片根据职位生成副标题
 */
internal fun GuideJobItem.getJobCardSubTitle():String {
    val minSalaryStr =
        this.minSalary?.formatMoney(LocalManager.getAppLocale()) ?: ""
    val maxSalaryStr =
        this.maxSalary?.formatMoney(LocalManager.getAppLocale()) ?: ""
    val jobSalaryStr = when {
        this.minSalary != null && this.maxSalary != null -> {
            "${this.salaryUnit?.name ?: ""}${minSalaryStr}-${this.salaryUnit?.name ?: ""}${maxSalaryStr}${this.salaryShortType?.name ?: ""}"
        }

        this.minSalary != null -> {
            "${this.salaryUnit?.name ?: ""}${minSalaryStr}${this.salaryShortType?.name ?: ""}"
        }

        this.maxSalary != null -> {
            "${this.salaryUnit?.name ?: ""}${maxSalaryStr}${this.salaryShortType?.name ?: ""}"
        }

        else -> {
            ""
        }
    }

    val jobDescStrList = mutableListOf<String>().also {
        it.addAll(this.jobType?.map { jobTypeItem-> jobTypeItem.name ?: "" } ?: listOf())
        it.add(this.locationType?.name ?: "")
        it.add(jobSalaryStr)
    }.filter {
        it.isNotBlank()
    }

    return jobDescStrList.joinToString(" · ")
}