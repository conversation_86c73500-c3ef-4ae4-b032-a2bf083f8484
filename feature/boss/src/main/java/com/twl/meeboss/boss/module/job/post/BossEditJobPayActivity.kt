package com.twl.meeboss.boss.module.job.post

import android.app.Activity
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.constants.BOSS_EDIT_JOB_PAY
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.stringResourceWithOption
import com.twl.meeboss.base.model.IndexBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossRouterPath
import com.twl.meeboss.boss.module.job.post.viewmodel.BossEditPayUiIntent
import com.twl.meeboss.boss.module.job.post.viewmodel.BossEditPayUiState
import com.twl.meeboss.boss.module.job.post.viewmodel.BossEditPayViewModel
import com.twl.meeboss.boss.module.job.post.viewmodel.SuggestSalary
import com.twl.meeboss.core.ui.component.button.XCommonButton
import com.twl.meeboss.core.ui.component.layout.FlowLayout
import com.twl.meeboss.core.ui.component.textfield.XMoneyTextField
import com.twl.meeboss.core.ui.resource.employer_job_pay_range_too_low_tip
import com.twl.meeboss.core.ui.resource.employer_job_pay_range_too_wide_subtitle_tip
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_028847
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.COLOR_DDDDDD
import com.twl.meeboss.core.ui.theme.COLOR_E0F8EB
import com.twl.meeboss.core.ui.theme.COLOR_E5E5EA
import com.twl.meeboss.core.ui.theme.COLOR_E86802
import com.twl.meeboss.core.ui.theme.COLOR_FFF3E9
import com.twl.meeboss.core.ui.theme.Primary
import com.twl.meeboss.core.ui.theme.RedBD222B
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.theme.alpha
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.AndroidEntryPoint

@RouterPage(path = [BossRouterPath.BOSS_EDIT_JOB_PAY_PAGE])
@AndroidEntryPoint
class BossEditJobPayActivity() : BaseMviActivity<BossEditPayViewModel>() {

    override val viewModel: BossEditPayViewModel by viewModels()

    override fun preInit(intent: Intent) {
        val firstBean = OptionBean(1, R.string.job_per_year.toResourceString())
        val secondBean = OptionBean(2, R.string.job_per_month.toResourceString())
        val thirdBean = OptionBean(3, R.string.job_per_hour.toResourceString())
        val minSalary = intent.getLongExtra("minSalary", 0)
        val maxSalary = intent.getLongExtra("maxSalary", 0)
        val salaryType = intent.getLongExtra("salaryType", 0)
        val jobTitle = intent.getStringExtra("jobTitle") ?: ""
        val jobLocation = intent.getStringExtra("jobLocation") ?: ""
        val benefits = intent.getSerializableExtra("benefits") as? ArrayList<OptionBean> ?: ArrayList()
        viewModel.sendUiIntent(BossEditPayUiIntent.Init(firstBean,
            secondBean,thirdBean,
            minSalary, maxSalary,
            salaryType, jobTitle,
            jobLocation, benefits)
        )
    }

    override fun initData() {
        viewModel.sendUiIntent(BossEditPayUiIntent.GetBenefits)
    }

    @Composable
    override fun ComposeContent() {
        BossEditPayComponent(viewModel, onClickSave = this::onClickSave)

    }


    private fun onClickSave() {
        viewModel.checkCanConfirm {
            viewModel.uiStateFlow.value.run {
                Intent().apply {
                    putExtra("minSalary", minSalary)
                    putExtra("maxSalary", maxSalary)
                    putExtra("salaryType", currentSalaryType)
                    putExtra("salaryTypeName", viewModel.getSalaryTypeName())
                    putExtra("benefits", ArrayList(selectedBenefits))
                    setResult(Activity.RESULT_OK, this)
                    finish()
                }
            }

        }

    }

    companion object {
        fun intent(context: Activity, optionBeans: ArrayList<OptionBean>,
                   minSalary: Long, maxSalary: Long, salaryType: Long,
                   jobTitle: String, jobLocation: String, benefits: ArrayList<OptionBean>,
                   requestCode: Int = BOSS_EDIT_JOB_PAY) {
            val intent = Intent(context, BossEditJobPayActivity::class.java).also {
                it.putExtra("optionBeans", optionBeans)
                it.putExtra("minSalary", minSalary)
                it.putExtra("maxSalary", maxSalary)
                it.putExtra("salaryType", salaryType)
                it.putExtra("jobTitle", jobTitle)
                it.putExtra("jobLocation", jobLocation)
                it.putExtra("benefits", benefits)
            }
            context.startActivityForResult(intent, requestCode)

        }
    }

}


@Composable
fun BossEditPayComponent(vm: BossEditPayViewModel = viewModel(), onClickSave: () -> Unit = {}) {
    XTheme {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
        ) {

            val uiState by vm.uiStateFlow.collectAsStateWithLifecycle()

            XTitleBar(title = stringResource(id = R.string.job_pay))
            HorizontalDivider(thickness = 0.5.dp, color = BlackEBEBEB)

            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
            ) {
                Text(text = stringResource(id = R.string.job_rate), modifier = Modifier.padding(16.dp, 20.dp),
                    fontSize = 20.sp, fontWeight = FontWeight.SemiBold,
                    color = Black222222)
                BossEditPayMethods(uiState) {
                    vm.sendUiIntent(BossEditPayUiIntent.OnSelectSalaryType(it))
                }
                Text(text = "${stringResource(id = R.string.job_pay_description)}\n${stringResource(id = R.string.employer_job_pay_benefits_subtitle)}",
                    modifier = Modifier.padding(16.dp, 12.dp, 16.dp, 0.dp),
                    fontWeight = FontWeight.Normal,
                    fontSize = 13.sp, color = Black484848)

                HorizontalDivider(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp), thickness = 0.5.dp, color = BlackEBEBEB)

                Text(text = stringResource(id = R.string.job_pay_range), modifier = Modifier.padding(16.dp, 20.dp),
                    fontSize = 20.sp, fontWeight = FontWeight.SemiBold,
                    color = Black222222)


                Row(modifier = Modifier.padding(16.dp, 0.dp), verticalAlignment = Alignment.CenterVertically) {
                    XMoneyTextField(value = if(uiState.minSalary <=0) "" else uiState.minSalary.toString(),
                        modifier = Modifier.weight(1F),
                        locale = LocalManager.getAppLocale(),
                        innerTitle = com.twl.meeboss.core.ui.R.string.common_minimum,
                        placeHolder = com.twl.meeboss.core.ui.R.string.common_minimum, onValueChange = {
                            vm.sendUiIntent(BossEditPayUiIntent.OnInputMinSalary(it))
                        }, state = uiState.minState, onStateChanged = {
                            vm.sendUiIntent(BossEditPayUiIntent.OnInputMinStateChanged(it))
                        })
                    HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB, modifier = Modifier
                        .padding(12.dp, 0.dp)
                        .width(20.dp))
                    XMoneyTextField(value = if(uiState.maxSalary<=0) "" else uiState.maxSalary.toString(),
                        modifier = Modifier.weight(1F),
                        locale = LocalManager.getAppLocale(),
                        innerTitle = com.twl.meeboss.core.ui.R.string.common_maximum,
                        placeHolder = com.twl.meeboss.core.ui.R.string.common_maximum, onValueChange = {
                            vm.sendUiIntent(BossEditPayUiIntent.OnInputMaxSalary(it))
                            vm.sendUiIntent(BossEditPayUiIntent.OnCheckMaxSalary(it))
                        }, state = uiState.maxState, onStateChanged = {
                            vm.sendUiIntent(BossEditPayUiIntent.OnInputMaxStateChanged(it))
                        })
                }

                if (uiState.errorMsg.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(text = uiState.errorMsg, modifier = Modifier.padding(16.dp, 0.dp),
                        fontWeight = FontWeight.Medium,
                        fontSize = 12.sp, color = RedBD222B)
                }

                val suggestSalary = uiState.suggestSalary
                if (suggestSalary.type != BossEditPayViewModel.TYPE_HIDE_SUGGEST_SALARY) {
                    Row(
                        modifier = Modifier
                            .padding(top = 8.dp, start = 16.dp, end = 16.dp)
                            .background(
                                COLOR_FFF3E9,
                                RoundedCornerShape(8.dp)
                            )
                            .padding(PaddingValues(12.dp, 8.dp)),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            modifier = Modifier
                                .padding(end = 12.dp)
                                .weight(1F),
                            text = if (suggestSalary.type == BossEditPayViewModel.TYPE_SHOW_MIN_SUGGEST_SALARY)
                                employer_job_pay_range_too_low_tip("$${suggestSalary.salary}")
                            else
                                employer_job_pay_range_too_wide_subtitle_tip("$${suggestSalary.salary}"),
                            textAlign = TextAlign.Start,
                            fontWeight = FontWeight.Normal,
                            fontSize = 13.sp,
                            color = COLOR_222222
                        )

                        Text(
                            modifier = Modifier.noRippleClickable {
                                if (suggestSalary.type == BossEditPayViewModel.TYPE_SHOW_MIN_SUGGEST_SALARY) {
                                    PointHelper.reportPoint("salaryexceed-minimum-apply"){
                                        addP(uiState.minSalary.toString())
                                        addP2(uiState.jobTitle)
                                        addP3(uiState.jobLocation)
                                    }
                                    vm.sendUiIntent(BossEditPayUiIntent.OnInputMinSalary(suggestSalary.salary.toString()))
                                } else {
                                    PointHelper.reportPoint("salaryexceed-maximum-apply"){
                                        addP(uiState.maxSalary.toString())
                                        addP2(uiState.jobTitle)
                                        addP3(uiState.jobLocation)
                                    }
                                    vm.sendUiIntent(BossEditPayUiIntent.OnInputMaxSalary(suggestSalary.salary.toString()))
                                }
                                // 隐藏建议薪资
                                vm.sendUiIntent(BossEditPayUiIntent.OnChangeSuggestSalary(
                                    SuggestSalary(BossEditPayViewModel.TYPE_HIDE_SUGGEST_SALARY)
                                ))
                            },
                            text = stringResource(id = R.string.employer_job_pay_range_too_wide_use_button),
                            textAlign = TextAlign.End,
                            fontWeight = FontWeight.Medium,
                            fontSize = 12.sp,
                            color = COLOR_E86802
                        )
                    }
                }

                if (uiState.benefits.isNotEmpty()) {
                    HorizontalDivider(modifier = Modifier.padding(start = 16.dp, end = 16.dp, top = 20.dp), thickness = 0.5.dp, color = BlackEBEBEB)

                    Text(
                        text = stringResourceWithOption(id = R.string.job_pay_benefits),
                        modifier = Modifier.padding(16.dp, 20.dp),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Black222222
                    )

                    FlowLayout(
                        modifier = Modifier.padding(16.dp, 0.dp),
                        horizontalSpacing = 8.dp,
                        verticalSpacing = 8.dp,
                        maxLine = Int.MAX_VALUE
                    ) {
                        uiState.benefits.forEach {
                            val isSelected = uiState.selectedBenefits.contains(it)
                            Box(
                                modifier = Modifier
                                    .background(
                                        if (isSelected) COLOR_E0F8EB else Color.Transparent,
                                        RoundedCornerShape(8.dp)
                                    )
                                    .border(
                                        if (isSelected) 0.dp else 1.dp,
                                        COLOR_E5E5EA,
                                        RoundedCornerShape(8.dp)
                                    )
                                    .noRippleClickable {
                                        vm.sendUiIntent(BossEditPayUiIntent.OnSelectBenefit(it))
                                    }
                                    .padding(12.dp)
                            ) {
                                Text(
                                    text = it.name,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Medium,
                                    fontSize = 13.sp,
                                    color = if (isSelected) COLOR_028847 else Black484848
                                )
                            }
                        }
                    }
                }
            }


            XCommonButton(modifier = Modifier.padding(16.dp), enabled = uiState.canSave,
                text = stringResource(id = R.string.common_button_save),
                onClick = onClickSave)

        }
    }
}


@Composable
fun BossEditPayMethods(uiState: BossEditPayUiState, modifier: Modifier = Modifier, onClick: (Long) -> Unit = {}) {

    Row(modifier = modifier
        .padding(16.dp, 0.dp)
        .fillMaxWidth()
        .height(49.dp)) {
        val shape1 = RoundedCornerShape(topStart = 8.dp, topEnd = 0.dp, bottomStart = 8.dp, bottomEnd = 0.dp)
        Box(modifier = Modifier
            .fillMaxHeight()
            .weight(1F)
            .background(
                if (uiState.currentSalaryType == uiState.firstBean.code) Primary.alpha(0.08f) else Color.White,
                shape1
            )
            .border(
                1.dp,
                if (uiState.currentSalaryType == uiState.firstBean.code) Secondary else COLOR_DDDDDD,
                shape1
            )
            .noRippleClickable {
                onClick(uiState.firstBean.code)
            }
        ) {
            Text(text = uiState.firstBean.name,
                modifier = Modifier.align(Alignment.Center),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp, color = if (uiState.currentSalaryType == uiState.firstBean.code) Secondary else Black222222)
        }

        Box(modifier = Modifier
            .fillMaxHeight()
            .weight(1F)
            .background(
                if (uiState.currentSalaryType == uiState.secondBean.code) Primary.alpha(
                    0.08f
                ) else Color.White
            )
            .border(
                1.dp,
                if (uiState.currentSalaryType == uiState.secondBean.code) Secondary else COLOR_DDDDDD
            )
            .noRippleClickable {
                onClick(uiState.secondBean.code)
            }
        ) {
            Text(text = uiState.secondBean.name,
                modifier = Modifier.align(Alignment.Center),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp, color = if (uiState.currentSalaryType == uiState.secondBean.code) Secondary else Black222222)
        }

        val shape2 = RoundedCornerShape(topStart = 0.dp, topEnd = 8.dp, bottomStart = 0.dp, bottomEnd = 8.dp)
        Box(modifier = Modifier
            .fillMaxHeight()
            .weight(1F)
            .background(
                if (uiState.currentSalaryType == uiState.thirdBean.code) Primary.alpha(0.08f) else Color.White,
                shape2
            )
            .border(
                1.dp,
                if (uiState.currentSalaryType == uiState.thirdBean.code) Secondary else COLOR_DDDDDD,
                shape2
            )
            .noRippleClickable {
                onClick(uiState.thirdBean.code)
            }
        ) {
            Text(text = uiState.thirdBean.name,
                modifier = Modifier.align(Alignment.Center),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp, color = if (uiState.currentSalaryType == uiState.thirdBean.code) Secondary else Black222222)
        }


    }
}

@Preview
@Composable
private fun PreviewBossEditPayComponent() {
    val vm: BossEditPayViewModel = viewModel<BossEditPayViewModel>().also {
        val firstBean = OptionBean(1, stringResource(id = R.string.job_per_year))
        val secondBean = OptionBean(2, stringResource(id = R.string.job_per_month))
        val thirdBean = OptionBean(3, stringResource(id = R.string.job_per_hour))
        it.sendUiIntent(BossEditPayUiIntent.Init(firstBean, secondBean, thirdBean, 0, 0, 1))
    }
    BossEditPayComponent(vm = vm)
}

