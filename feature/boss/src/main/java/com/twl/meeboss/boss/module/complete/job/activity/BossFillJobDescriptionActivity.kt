package com.twl.meeboss.boss.module.complete.job.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.sankuai.waimai.router.annotation.RouterPage
import com.twl.meeboss.base.components.tip.XClosableTip
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_FROM
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION_HAS_GENERATED
import com.twl.meeboss.base.constants.BUNDLE_JOB_DESCRIPTION_IS_APPLIED
import com.twl.meeboss.base.constants.BUNDLE_JOB_TITLE
import com.twl.meeboss.base.constants.DefaultValueConstants
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.ktx.subString
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.export.BossRouterPath
import com.twl.meeboss.boss.module.complete.job.dialog.BossGenerateJobDescriptionBottomSheet
import com.twl.meeboss.boss.module.complete.job.dialog.BossGenerateJobTipBottomSheet
import com.twl.meeboss.boss.module.complete.job.viewmodel.BossFillJobDescriptionUiIntent
import com.twl.meeboss.boss.module.complete.job.viewmodel.BossFillJobDescriptionViewModel
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.button.OutlineGreenColors
import com.twl.meeboss.core.ui.dialog.showConfirmDialog
import com.twl.meeboss.core.ui.theme.Black222222
import com.twl.meeboss.core.ui.theme.Black484848
import com.twl.meeboss.core.ui.theme.BlackEBEBEB
import com.twl.meeboss.core.ui.theme.COLOR_AAAAAA
import com.twl.meeboss.core.ui.theme.GRAY_AAAAAA
import com.twl.meeboss.core.ui.theme.Primary
import com.twl.meeboss.core.ui.theme.RedBD222B
import com.twl.meeboss.core.ui.theme.Secondary
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.core.ui.theme.alpha
import com.twl.meeboss.core.ui.utils.dismissSafely
import com.twl.meeboss.core.ui.utils.noRippleClickable
import com.twl.meeboss.core.ui.utils.showSafely
import com.twl.meeboss.core.ui.utils.toResourceString
import dagger.hilt.android.AndroidEntryPoint


@RouterPage(path = [BossRouterPath.BOSS_COMPLETE_JOB_DESCRIPTION_PAGE])
@AndroidEntryPoint
class BossFillJobDescriptionActivity() : BaseMviActivity<BossFillJobDescriptionViewModel>() {

    override val viewModel: BossFillJobDescriptionViewModel by viewModels()
    private val mOriginJobDescription by lazy {
        intent.getStringExtra(BUNDLE_JOB_DESCRIPTION)?:""
    }
    private val mOriginHasGenerated by lazy {
        // 这个字段决定了生成按钮是否可用
        intent.getBooleanExtra(BUNDLE_JOB_DESCRIPTION_HAS_GENERATED, false)
    }
    private val mOriginIsApplied by lazy {
        // 这个字段决定了顶部的绿条是否展示
        intent.getBooleanExtra(BUNDLE_JOB_DESCRIPTION_IS_APPLIED, false)
    }
    private val mJobTitle by lazy {
        intent.getStringExtra(BUNDLE_JOB_TITLE)
    }
    private val mFrom by lazy {
        intent.getStringExtra(BUNDLE_FROM)
    }
    private val mGenerateDialog by lazy {
        BossGenerateJobDescriptionBottomSheet.newInstance(
            generateJobDescriptionStatus = viewModel.generateJobDescriptionStatus,
            generateJobDescription = viewModel.generateJobDescription,
            onApplyClick = {
                val currentInput = viewModel.uiStateFlow.value.jobDescription
                if (currentInput.text.isBlank()) {
                    viewModel.sendUiIntent(BossFillJobDescriptionUiIntent.ReplaceJobDescription(
                        TextFieldValue(viewModel.generateJobDescription.value?:"", TextRange(viewModel.generateJobDescription.value?.length?:0))))
                    dismissGenerateDialog()
                } else {
                    onApplyJobDescriptionClick()
                }
            }
        )
    }

    override fun preInit(intent: Intent) {
        if (mJobTitle.isNullOrEmpty()) {
            XLog.info(TAG, "preInit jobTitle empty")
        }
        viewModel.sendUiIntent(
            BossFillJobDescriptionUiIntent.Init(
                mJobTitle?:"",
                mOriginJobDescription,
                mOriginHasGenerated,
                mOriginIsApplied,
                mFrom?:""
            )
        )
        viewModel.generateJobDescriptionStatus.observe(this) {
            if (it == 2) {
                mGenerateDialog.dismissSafely()
            }
        }
    }

    override fun initData() {
    }

    private fun onSaveClick() {
        if (viewModel.uiStateFlow.value.jobDescription.text.length < DefaultValueConstants.MIN_INPUT_JOB_DESCRIPTION) {
            T.ss(R.string.common_input_least_characters.toResourceString(DefaultValueConstants.MIN_INPUT_JOB_DESCRIPTION.toString()))
            return
        }
        if (viewModel.generateNeedWarning) {
            showConfirmDialog(
                title = R.string.boss_perfect_way_alert_title.toResourceString(),
                content = R.string.boss_perfect_way_alert_desc.toResourceString(),
                onConfirm = {
                    sendResult()
                }
            )
        } else {
            sendResult()
        }
    }

    private fun sendResult() {
        val uiState = viewModel.uiStateFlow.value
        setResult(RESULT_OK, Intent().apply {
            putExtra(BUNDLE_JOB_DESCRIPTION, uiState.jobDescription.text)
            putExtra(BUNDLE_JOB_DESCRIPTION_HAS_GENERATED, uiState.hasGenerate)
            putExtra(BUNDLE_JOB_DESCRIPTION_IS_APPLIED, uiState.isApplied)
        })
        finish()
    }

    private fun onApplyJobDescriptionClick() {
        showConfirmDialog(
            title = R.string.boss_job_description_confirm_title.toResourceString(),
            content = R.string.boss_job_description_confirm_content.toResourceString(),
            confirmText = R.string.common_replace.toResourceString(),
            cancelText = R.string.common_insert.toResourceString(),
            onConfirm = {
                viewModel.sendUiIntent(BossFillJobDescriptionUiIntent.ReplaceJobDescription(
                    TextFieldValue(viewModel.generateJobDescription.value?:"", TextRange(viewModel.generateJobDescription.value?.length?:0))))
                dismissGenerateDialog()
            },
            onCancel = {
                viewModel.sendUiIntent(BossFillJobDescriptionUiIntent.InsertJobDescription(viewModel.generateJobDescription.value?:""))
                dismissGenerateDialog()
            }
        )
    }

    private fun dismissGenerateDialog(){
        mGenerateDialog.dismissSafely()
    }

    @Composable
    override fun ComposeContent() {
        val uiState = viewModel.uiStateFlow.collectAsStateWithLifecycle()
        BossFillJobDescriptionContent(
            content = uiState.value.jobDescription,
            showTip = uiState.value.showTip,
            canGenerate = !uiState.value.hasGenerate,
            onSaveClick = {
                this.onSaveClick()
            },
            onGenerateJobDescriptionClick = {
                if (uiState.value.jobTitle.isBlank()) {
                    T.ss(R.string.boss_please_fill_in_job_title.toResourceString())
                    return@BossFillJobDescriptionContent
                }
                mGenerateDialog.showSafely(this)
                viewModel.sendUiIntent(BossFillJobDescriptionUiIntent.GenerateJobDescription)
            },
            onValueChange = {
                it.subString(DefaultValueConstants.MAX_INPUT_JOB_DESCRIPTION, true){
                    T.ss(R.string.common_description_out_of_max_range.toResourceString(DefaultValueConstants.MAX_INPUT_JOB_DESCRIPTION.toString()))
                }.let { result ->
                    viewModel.sendUiIntent(BossFillJobDescriptionUiIntent.OnJobDescriptionValueChange(result))
                }

            },
            onClearAllClick = {
                viewModel.sendUiIntent(BossFillJobDescriptionUiIntent.OnJobDescriptionValueChange(TextFieldValue("",
                    TextRange.Zero)))
            },
            onClickTip = {
                BossGenerateJobTipBottomSheet.newInstance().showSafely(this)
            }
        )
    }

}

@Composable
fun BossFillJobDescriptionContent(
    content:TextFieldValue = TextFieldValue("", TextRange.Zero),
    showTip:Boolean = false,
    canGenerate:Boolean = true,
    onSaveClick: (String) -> Unit = {},
    onGenerateJobDescriptionClick: () -> Unit = {},
    onValueChange: (TextFieldValue) -> Unit = {},
    onClearAllClick:() -> Unit = {},
    onClickTip: () -> Unit = {},
) {
    val tipVisibility = remember {
        mutableStateOf(true)
    }
    XTheme {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)) {
            XTitleBar(
                title = stringResource(id = R.string.job_description),
            ) {
                Text(
                    modifier = Modifier
                        .padding(16.dp, 10.dp)
                        .noRippleClickable(onClick = {
                            onSaveClick(content.text)
                        }),
                    text = stringResource(id = R.string.common_button_save),
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.W500,
                        color = if(content.text.length < DefaultValueConstants.MIN_INPUT_JOB_DESCRIPTION) Secondary.alpha(0.5f) else Secondary,
                    )
                )
            }

            if (showTip && tipVisibility.value) {
                XClosableTip(
                    tipText = stringResource(id = R.string.boss_complete_generate_job_tip),
                    onCloseTipClick = {
                        tipVisibility.value = false
                    }
                )
            }


            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(top = 28.dp, start = 16.dp, end = 16.dp)
            ) {
                if (content.text.isBlank()) {
                    Text(
                        text = stringResource(
                            id = R.string.job_description_place_holder
                        ),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W400,
                            color = COLOR_AAAAAA,
                        )
                    )
                }

                BasicTextField(
                    value = content,
                    textStyle = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = Black222222,
                    ),
                    onValueChange = onValueChange,
                    modifier = Modifier.fillMaxSize()
                )
            }

            Row(modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 12.dp)) {
                Text(
                    text = stringResource(id = R.string.common_clear_all),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 21.sp,
                        fontWeight = FontWeight.W500,
                        color = if(content.text.isBlank()) GRAY_AAAAAA else Black222222,
                    ),
                    modifier = Modifier.noRippleClickable(onClick = {
                        if(content.text.isNotBlank()){
                            onClearAllClick()
                        }

                    })
                )
                
                Spacer(modifier = Modifier.weight(1f))

                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
                    Spacer(modifier = Modifier.height(12.dp))
                    val count = content.text.length
                    Text(text = "${count}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (content.text.isBlank()) {
                            Black222222
                        } else if (content.text.length < DefaultValueConstants.MIN_INPUT_JOB_DESCRIPTION) {
                            RedBD222B
                        }  else {
                            Primary
                        })
                    Text(text = "/${DefaultValueConstants.MAX_INPUT_JOB_DESCRIPTION}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Black484848
                    )
                }
            }

            HorizontalDivider(thickness = 1.dp, color = BlackEBEBEB)

            Spacer(modifier = Modifier
                .fillMaxWidth()
                .height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth()
            ) {
                OutlinedButton(
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .alpha(if (canGenerate) 1f else 0.5f),
                    colors = OutlineGreenColors(),
                    contentPadding = PaddingValues(20.dp, 8.dp),
                    border = BorderStroke(1.dp, OutlineGreenColors().contentColor),
                    onClick = {
                        tipVisibility.value = true
                        onGenerateJobDescriptionClick()
                    },
                    enabled = canGenerate,
                ) {

                    Image(
                        painter = painterResource(id = R.drawable.boss_icon_write_with_ai),
                        contentDescription = "",
                        modifier = Modifier
                            .size(20.dp)
                            .padding(end = 4.dp)
                    )

                    Text(
                        text = stringResource(id = R.string.boss_write_with_ai),
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                    )
                }

                Spacer(modifier = Modifier.width(16.dp))
                Image(
                    painter = painterResource(id = R.drawable.base_icon_title_question_tips),
                    contentDescription = "",
                    modifier = Modifier
                        .size(16.dp)
                        .align(Alignment.CenterVertically)
                        .noRippleClickable(onClick = onClickTip)
                )
            }

            Spacer(modifier = Modifier
                .fillMaxWidth()
                .height(12.dp))
        }
    }
}

@Preview
@Composable
private fun PreviewBossFillJobDescriptionContent() {
    BossFillJobDescriptionContent(
    )
}
