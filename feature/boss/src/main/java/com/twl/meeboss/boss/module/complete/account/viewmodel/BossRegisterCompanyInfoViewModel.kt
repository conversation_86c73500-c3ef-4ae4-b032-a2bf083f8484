package com.twl.meeboss.boss.module.complete.account.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.twl.meeboss.base.condition.isValidUrl
import com.twl.meeboss.base.foundation.viewmodel.BaseMviViewModel
import com.twl.meeboss.base.foundation.IUiIntent
import com.twl.meeboss.base.foundation.IUiState
import com.twl.meeboss.base.foundation.LoadState
import com.twl.meeboss.base.model.HighlightBean
import com.twl.meeboss.base.model.OptionBean
import com.twl.meeboss.base.model.common.CommonBrandBean
import com.twl.meeboss.base.model.common.FileItem
import com.twl.meeboss.base.model.common.getLegalImageItem
import com.twl.meeboss.base.model.common.isValidCompany
import com.twl.meeboss.base.model.enumeration.UploadFileBizType
import com.twl.meeboss.base.usecase.ImageUploadUseCase
import com.twl.meeboss.boss.R
import com.twl.meeboss.boss.module.complete.account.model.BossRegisterJoinCompanyResult
import com.twl.meeboss.boss.repos.BossRepository
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.preference.SpManager
import com.twl.meeboss.common.utils.GsonUtils
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.network.handleDefaultError
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject


@HiltViewModel
class BossRegisterCompanyInfoViewModel @Inject constructor(
    private val repos: BossRepository,
    private val imageUploadUseCase: ImageUploadUseCase,
) : BaseMviViewModel<BossRegisterCompanyInfoUiState, BossRegisterCompanyInfoUiIntent>() {
    private val DRAFT_KEY_COMPANY_INFO = "draft_key_company_info"
    val joinCompanySuccess: MutableLiveData<Boolean> = MutableLiveData()
    var joinCompanyResult: BossRegisterJoinCompanyResult? = null
    override fun initUiState(): BossRegisterCompanyInfoUiState = BossRegisterCompanyInfoUiState()

    /**
     * CommonBrandBean 智能合并扩展函数
     * 将 other 中的有效数据合并到当前对象中，避免覆盖已有的有效数据
     */
    private fun CommonBrandBean.mergeWith(other: CommonBrandBean): CommonBrandBean {
        return this.copy(
            // 字符串字段：非空白则合并
            companyId = if (other.companyId.isNotBlank() || other.isLocalAdd) other.companyId else this.companyId,
            companyName = other.companyName.ifBlank { this.companyName },
            companyLogo = other.companyLogo.ifBlank { this.companyLogo },
            companyShortName = other.companyShortName.ifBlank { this.companyShortName },
            companyDesc = other.companyDesc.ifBlank { this.companyDesc },
            website = other.website.ifBlank { this.website },
            companyLogoThumbnail = other.companyLogoThumbnail.ifBlank { this.companyLogoThumbnail },
            
            // OptionBean字段：有效值则合并
            industry = if (other.industry.code != 0L || other.industry.name.isNotBlank()) other.industry else this.industry,
            sizeType = if (other.sizeType.code != 0L || other.sizeType.name.isNotBlank()) other.sizeType else this.sizeType,
            
            // Int字段：非零则合并
            completeStatus = if (other.completeStatus != 0) other.completeStatus else this.completeStatus,
            
            // Boolean字段：直接使用新值
            isLocalAdd = other.isLocalAdd,
            
            // List字段：非空则合并
            companyEnvironments = other.companyEnvironments.ifEmpty { this.companyEnvironments },
            companyEnvironmentThumbnails = other.companyEnvironmentThumbnails.ifEmpty { this.companyEnvironmentThumbnails },
        )
    }

    override fun handleIntent(intent: IUiIntent) {
        when (intent) {
            is BossRegisterCompanyInfoUiIntent.UpdateCompanyNumber -> {
                sendUiState {
                    copy(companyInfo = companyInfo.copy(sizeType = intent.value)).checkCanSave()
                }
            }

            is BossRegisterCompanyInfoUiIntent.UpdateCompanyIndustry -> {
                sendUiState {
                    copy(
                        companyInfo = companyInfo.copy(
                            industry = OptionBean(
                                code = intent.value.code,
                                name = intent.value.name
                            )
                        )
                    ).checkCanSave()
                }
            }

            is BossRegisterCompanyInfoUiIntent.UpdateCompanyWebsite -> {
                sendUiState {
                    copy(companyInfo = companyInfo.copy(website = intent.value), companyWebsiteError = 0).checkCanSave()
                }
            }

            is BossRegisterCompanyInfoUiIntent.UpdateCompanyName -> {
                sendUiState {
                    XLog.debug(TAG, "UpdateCompanyName: 合并前 companyInfo = $companyInfo")
                    XLog.debug(TAG, "UpdateCompanyName: 传入的 companyBean = ${intent.companyBean}")
                    val mergedCompanyInfo = companyInfo.mergeWith(intent.companyBean)
                    XLog.debug(TAG, "UpdateCompanyName: 合并后 companyInfo = $mergedCompanyInfo")
                    
                    copy(
                        companyInfo = mergedCompanyInfo,
                        mediaList = mergedCompanyInfo.companyEnvironments.map { FileItem(url = it) }.ifEmpty { mediaList }
                    ).checkCanSave()
                }
            }

            is BossRegisterCompanyInfoUiIntent.UpdateCompanyDescription -> {
                sendUiState {
                    copy(companyInfo = companyInfo.copy(companyDesc = intent.value), overFlow = intent.overFlow).checkCanSave()
                }
            }

            is BossRegisterCompanyInfoUiIntent.UpLoadLogo -> {
                upLoadLogo(intent.mediaItems)
            }

            is BossRegisterCompanyInfoUiIntent.AddImage -> {
                addImage(intent.mediaItems)
            }

            is BossRegisterCompanyInfoUiIntent.DeleteImage -> {
                deleteImage(intent.mediaItem)
            }

            is BossRegisterCompanyInfoUiIntent.UpdateCompanyAbbreviation -> {
                sendUiState {
                    copy(companyInfo = companyInfo.copy(companyShortName = intent.value)).checkCanSave()
                }
            }

            is BossRegisterCompanyInfoUiIntent.ReadDraft -> {
                readDraft()
            }

            is BossRegisterCompanyInfoUiIntent.JoinCompanyByLocalData -> {
                joinCompanyByLocalData()
            }

            is BossRegisterCompanyInfoUiIntent.CompanyWebsiteFocusChange -> {
                validateWebsiteUrl(intent.value)
            }

            else -> {

            }

        }
    }

    private fun validateWebsiteUrl(focus: Boolean) {
        // 失去焦点时，检查网址是否合法
        if (focus.not()) {
            val state = uiStateFlow.value
            var websiteText = state.companyInfo.website

            if(websiteText.isNotBlank()) {
                // 检查修正后的 URL 是否合法
                if(websiteText.isValidUrl().not()){
                    sendUiState {
                        copy(
                            canSave = false,
                            companyWebsiteError = R.string.common_error_link_toast,
                        )
                    }
                } else {
                    sendUiState {
                        copy(
                            companyWebsiteError = 0
                        )
                    }
                }
            }
        } else {
            sendUiState {
                copy(
                    companyWebsiteError = 0
                )
            }
        }
    }


    private fun joinCompanyByLocalData() {
        val uiState = uiStateFlow.value
        val companyData = uiState.companyInfo
        if(companyData.website.isValidUrl().not()) {
            sendUiState {
                copy(
                    companyWebsiteError = R.string.common_error_link_toast,
                )
            }
            T.ss(R.string.common_error_link_toast)
            return
        }

        showLoadingDialog()
        requestData(
            request = {
                repos.bossRegisterJoinCompany(
                    companyId = companyData.companyId,
                    companyName = companyData.companyName,
                    companyShortName = companyData.companyShortName,
                    companySizeType = companyData.sizeType.code.toString(),
                    industryCode = companyData.industry.code.toString(),
                    website = companyData.website,
                    industryName = companyData.industry.name,
                    companyLogo = companyData.companyLogo,
                    companyDesc = companyData.companyDesc,
                    environment = uiState.mediaList.map { it.url }.joinToString(",")
                )
            },
            success = {
                dismissLoadingDialog()
                joinCompanySuccess.value = true
                joinCompanyResult = it
            },
            fail = {
                dismissLoadingDialog()
                it.handleDefaultError()
            }
        )
    }


    private fun readDraft() {
        val companyInfoJson = SpManager.getUserString(DRAFT_KEY_COMPANY_INFO, "")
        if (!companyInfoJson.isNullOrBlank()) {
            runCatching {
                GsonUtils.fromJson<CommonBrandBean>(companyInfoJson)
            }.onSuccess {
                it?.let { companyData ->
                    sendUiState {
                        copy(companyInfo = companyData).checkCanSave()
                    }
                }
            }
        }
    }

    //上传Logo
    private fun upLoadLogo(selectMediaItems: List<FileItem>) {
        val mediaItems = selectMediaItems.getLegalImageItem(true)
        if (mediaItems.isEmpty()) {
            return
        }

        sendUiState {
            copy(showLoading = true)
        }

        imageUploadUseCase(
            viewModelScope = viewModelScope,
            bizType = UploadFileBizType.COMPANY_LOGO,
            fileItemList = mediaItems,
            success = {
                val list = it.mapNotNull { fileUploadResult ->
                    fileUploadResult.originUrl
                }
                if (list.isNotEmpty()) {
                    sendUiState {
                        copy(
                            showLoading = false,
                            companyInfo = companyInfo.copy(companyLogo = list[0])
                        )
                    }
                } else {
                    sendUiState {
                        copy(showLoading = false)
                    }
                }
            },
            fail = {
                it?.let {
                    sendLoadUiState(LoadState.Fail(it.message))
                    XLog.error(TAG, "uploadImageFilesUseCase fail: ${it.message}")
                } ?: run {
                    sendLoadUiState(LoadState.Empty())
                    XLog.error(TAG, "uploadImageFilesUseCase is empty data")
                }
                T.ss(R.string.boss_company_logo_upload_failed)
                sendUiState {
                    copy(showLoading = false)
                }
            }
        )
    }

    //上传环境照片
    private fun addImage(selectMediaItems: List<FileItem>) {
        val mediaItems = selectMediaItems.getLegalImageItem(true)
        if (mediaItems.isEmpty()) {
            return
        }

        sendUiState {
            copy(showLoading = true)
        }

        imageUploadUseCase(
            viewModelScope = viewModelScope,
            bizType = UploadFileBizType.COMPANY_ENVIRONMENT,
            fileItemList = mediaItems,
            success = {
                val list = it.map { fileUploadResult ->
                    fileUploadResult.originFileItem.copy(
                        url = fileUploadResult.originUrl
                    )
                }
                val localMediaList = uiStateFlow.value.mediaList.toMutableList()
                localMediaList.addAll(list)
                sendUiState {
                    copy(
                        showLoading = false,
                        mediaList = localMediaList
                    )
                }
            },
            fail = {
                it?.let {
                    sendLoadUiState(LoadState.Fail(it.message))
                    XLog.error(TAG, "uploadImageFilesUseCase fail: ${it.message}")
                } ?: run {
                    sendLoadUiState(LoadState.Empty())
                    XLog.error(TAG, "uploadImageFilesUseCase is empty data")
                }
                T.ss(R.string.boss_upload_failed)

                sendUiState {
                    copy(showLoading = false)
                }
            }
        )
    }

    private fun deleteImage(mediaItem: FileItem) {
        sendUiState {
            copy(
                mediaList = mediaList.toMutableList().apply {
                    if (mediaList.contains(mediaItem)) {
                        remove(mediaItem)
                    }
                }
            )
        }
    }
}

data class BossRegisterCompanyInfoUiState(
    val canSave: Boolean = false,
    val showLoading: Boolean = false,
    val overFlow: Boolean = false,
    val mediaList: List<FileItem> = listOf(), //环境照片本地
    val companyWebsiteError: Int = 0,
    val companyInfo: CommonBrandBean = CommonBrandBean(companyId = "", companyName = ""),
) : IUiState {
    fun checkCanSave(): BossRegisterCompanyInfoUiState {
        val complete = if (companyInfo.isValidCompany()) {
            true
        } else {
            companyInfo.companyName.isNotBlank()
                    && companyInfo.website.isNotBlank()
        }
        return copy(canSave = complete)
    }
}

sealed class BossRegisterCompanyInfoUiIntent : IUiIntent {
    data object ReadDraft : BossRegisterCompanyInfoUiIntent()
    data object JoinCompanyByLocalData : BossRegisterCompanyInfoUiIntent()
    data class UpdateCompanyNumber(val value: OptionBean) : BossRegisterCompanyInfoUiIntent()
    data class UpdateCompanyIndustry(val value: HighlightBean) : BossRegisterCompanyInfoUiIntent()
    data class UpdateCompanyWebsite(val value: String) : BossRegisterCompanyInfoUiIntent()
    data class CompanyWebsiteFocusChange(val value: Boolean) : BossRegisterCompanyInfoUiIntent()
    data class UpdateCompanyDescription(val value: String, val overFlow: Boolean) : BossRegisterCompanyInfoUiIntent()
    data class UpdateCompanyName(val companyBean: CommonBrandBean) : BossRegisterCompanyInfoUiIntent()
    data class UpLoadLogo(val mediaItems: List<FileItem>) : BossRegisterCompanyInfoUiIntent()
    data class AddImage(val mediaItems: List<FileItem>) : BossRegisterCompanyInfoUiIntent()
    data class DeleteImage(val mediaItem: FileItem) : BossRegisterCompanyInfoUiIntent()
    data class UpdateCompanyAbbreviation(val value: String) : BossRegisterCompanyInfoUiIntent()

}