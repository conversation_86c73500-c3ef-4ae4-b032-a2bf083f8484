package com.twl.meeboss.boss.module.candidates.recommend.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.twl.meeboss.base.components.list.refresh.XRefreshListState
import com.twl.meeboss.boss.export.model.CandidateBaseInfo
import com.twl.meeboss.boss.export.model.CandidateCommonDesc
import com.twl.meeboss.boss.export.model.CandidateEduExp
import com.twl.meeboss.boss.export.model.CandidateItemResult
import com.twl.meeboss.boss.export.model.CandidateWorkExp
import com.twl.meeboss.boss.module.candidates.recommend.model.JobTitleInfo
import com.twl.meeboss.boss.module.candidates.recommend.model.JobTitleResult
import java.util.UUID

/**
 * @author: 冯智健
 * @date: 2024年07月11日 13:59
 * @description:
 */
object CandidatesListPreviewData {

    val jobTitleListResult = JobTitleResult(
        list = listOf(
            JobTitleInfo(
                UUID.randomUUID().toString(),
                "Product design"
            ),
            JobTitleInfo(
                UUID.randomUUID().toString(),
                "AI produce"
            ),
            JobTitleInfo(
                UUID.randomUUID().toString(),
                "Javascript"
            ),
            JobTitleInfo(
                UUID.randomUUID().toString(),
                "Android"
            ),
        ),
    )

    val candidateItemResult = CandidateItemResult(
        securityId = UUID.randomUUID().toString(),
        active = "Active",
        baseInfo = CandidateBaseInfo(
            avatar = "https://img2.baidu.com/it/u=1459566101,1482467068&fm=253&fmt=auto&app=138&f=JPEG?w=400&h=400",
            firstName = "Michelle",
            lastName = "Liu"
        ),
        eduLevelDesc = "MA degree MA degree",
        locationDesc = "London London London",
        desiredJobTitle = CandidateCommonDesc(
            tag = "Desired Position:",
            desc = "Product Manager"
        ),
        desiredLocation = CandidateCommonDesc(
            tag = "Desired Location:",
            desc = "HongKong"
        ),
        aboutMe = "Working king closely with closely with developers Working closely with quality " +
                "developers Working closely with quality developers closely with quality developers",
        recommendReason = CandidateCommonDesc(
            tag = "Highlight",
            desc = "Working king closely with closely with developers Working closely with quality " +
                    "developers Working closely with quality developers closely with quality developers"
        ),
        topMatchReason = "Working king closely with closely with developers Working closely with quality " +
                "developers Working closely with quality developers closely with quality developers",
        workExpList = listOf(
            CandidateWorkExp(
                companyName = "Business Executive Executive Executive Executive China Business China Business",
                jobTitle = "Boss ZhiPin PR Boss ZhiPin PR",
                timeDesc = "12 yr 12 mos"
            ),
            CandidateWorkExp(
                companyName = "Business Executive Executive Executive Executive China Business China Business",
                jobTitle = "Operations Executive Operations",
                timeDesc = "2 mos"
            )
        ),
        eduExp = CandidateEduExp(
            schoolName = "University of the arts",
            majorName = "Interaction designer",
            timeDesc = "2020-Present"
        ),
        labels = listOf(
            "Chinese",
            "B2B Marketing",
            "Chinese B2B Marketing B2B Marketing B2B Marketing B2B Marketing B2B Marketing",
            "B2B Marketing"
        ),
        viewJobTitle = CandidateCommonDesc(
            tag = "Viewed job：",
            desc = "Product manager"
        ),
        favorJobTitle = CandidateCommonDesc(
            tag = "Liked job：",
            desc = "Product manager"
        ),
        interested = 1
    )

    val candidateList = List(3) { candidateItemResult.copy(
            securityId = UUID.randomUUID().toString()
        )
    }
}

class CandidatesListItemPreviewParameterProvider : PreviewParameterProvider<CandidateItemResult> {
    override val values: Sequence<CandidateItemResult> = sequenceOf(
        CandidatesListPreviewData.candidateItemResult
    )
}

class CandidatesListPreviewParameterProvider :
    PreviewParameterProvider<XRefreshListState<CandidateItemResult>> {
    override val values: Sequence<XRefreshListState<CandidateItemResult>> = sequenceOf(
        XRefreshListState.getDefault(list = List(3) {
            CandidatesListPreviewData.candidateItemResult.copy(
                securityId = UUID.randomUUID().toString()
            )
        })
    )
}

class CandidatesListPreviewParameterProvider1 :
    PreviewParameterProvider<List<CandidateItemResult>> {
    override val values: Sequence<List<CandidateItemResult>> = sequenceOf(
        List(3) {
            CandidatesListPreviewData.candidateItemResult.copy(
                securityId = UUID.randomUUID().toString()
            )
        }
    )
}