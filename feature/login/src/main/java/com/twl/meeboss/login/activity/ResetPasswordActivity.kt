package com.twl.meeboss.login.activity

import android.app.Activity
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.BUNDLE_ACCOUNT
import com.twl.meeboss.base.constants.BUNDLE_BOOLEAN
import com.twl.meeboss.base.constants.BUNDLE_STRING
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.component.InputState
import com.twl.meeboss.core.ui.component.XPasswordTextField
import com.twl.meeboss.core.ui.theme.COLOR_222222
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.login.R
import com.twl.meeboss.login.components.LoginButton
import com.twl.meeboss.login.util.afterLogin
import com.twl.meeboss.login.viewmodel.ResetPasswordUiIntent
import com.twl.meeboss.login.viewmodel.ResetPasswordViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 重置密码
 */



//@RouterPage(path = [BossRouterPath])
@AndroidEntryPoint
class ResetPasswordActivity() : BaseMviActivity<ResetPasswordViewModel>() {

    override val viewModel: ResetPasswordViewModel by viewModels()

    override fun preInit(intent: Intent) {
        viewModel.uniStr = intent.getStringExtra(BUNDLE_STRING) ?: ""
        viewModel.isPhone = intent.getBooleanExtra(BUNDLE_BOOLEAN, false)
        viewModel.account = intent.getStringExtra(BUNDLE_ACCOUNT) ?: ""
    }

    override fun initData() {
        viewModel.resetResult.observe(this) {
            if (it) {
                XLog.info(TAG,"afterLogin")
                afterLogin()
            }
        }
        viewModel.linkResult.observe(this) {
            if (it) {
               ChooseLinkLoginActivity.intent(
                   context = this@ResetPasswordActivity,
                   phone = viewModel.account,
                   robotCheckToken = ""
               )
            }
        }
    }



    private fun onValueChangedOne(code: String) {
        viewModel.sendUiIntent(ResetPasswordUiIntent.InputPasswordOne(code))
    }


    private fun onValueChangedTwo(newValue: String) {
        viewModel.sendUiIntent(ResetPasswordUiIntent.InputPasswordTwo(newValue))
    }

    private fun onStateChangedOne(state: InputState) {
        viewModel.sendUiIntent(ResetPasswordUiIntent.InputStateChangedOne(state))
    }
    private fun onStateChangedTwo(state: InputState) {
        viewModel.sendUiIntent(ResetPasswordUiIntent.InputStateChangedTwo(state))
    }

    private fun onClickSave() {
        viewModel.sendUiIntent(ResetPasswordUiIntent.Save)
    }


    @Composable
    override fun ComposeContent() {
        ResetPasswordContent(viewModel,
            this::onValueChangedOne,
            this::onStateChangedOne,
            this::onValueChangedTwo,
            this::onStateChangedTwo,
            this::onClickSave)
    }

    companion object {
        fun intent(context: Activity, account: String, uniStr: String, isPhone: Boolean) {
            Intent(context, ResetPasswordActivity::class.java).apply {
                this.putExtra(BUNDLE_ACCOUNT, account)
                this.putExtra(BUNDLE_STRING, uniStr)
                this.putExtra(BUNDLE_BOOLEAN, isPhone)
                context.startActivity(this)
            }


        }
    }
}


@Preview
@Composable
fun ResetPasswordContent(viewModel: ResetPasswordViewModel = androidx.lifecycle.viewmodel.compose.viewModel(),
                         onValueChangedOne: (String) -> Unit = {},
                         onStateChangedOne: (InputState) -> Unit = {},
                         onValueChangedTwo: (String) -> Unit = {},
                         onStateChangedTwo: (InputState) -> Unit = {},
                         onClickSave: () -> Unit = {}) {
    XTheme {
        Column(modifier = Modifier
            .fillMaxSize()
            .background(Color.White)) {
            XTitleBar()
            Spacer(modifier = Modifier.height(12.dp))
            Text(modifier = Modifier.padding(16.dp, 0.dp, 0.dp, 0.dp),
                text = stringResource(id = R.string.setting_reset_password),
                color = COLOR_222222,
                fontSize = 28.sp, fontWeight = FontWeight(590))

            Spacer(modifier = Modifier.height(20.dp))

            val uiState by viewModel.uiStateFlow.collectAsStateWithLifecycle()

            XPasswordTextField(value = uiState.passwordOne,
                modifier = Modifier.padding(16.dp, 0.dp),
                placeHolder = R.string.setting_new_password,
                innerTitle = R.string.setting_new_password,
                textStyle = TextStyle(fontSize = 16.sp, fontWeight = FontWeight.Medium, color = Color.Black),
                isPassword = true,
                errorMessage = uiState.errorMessageOne,
                options = KeyboardOptions(keyboardType = KeyboardType.Password),
                state = uiState.inputStateOne,
                showKeyboard = true,
                onValueChange = {
                    if(it.contains(" ")){
                        T.ssd(com.twl.meeboss.core.ui.R.string.common_cant_contain_space)
                        return@XPasswordTextField
                    }
                    onValueChangedOne(it)
                }, onStateChanged = {
                    onStateChangedOne(it)
                })

            Spacer(modifier = Modifier.height(12.dp))

            XPasswordTextField(value = uiState.passwordTwo,
                modifier = Modifier.padding(16.dp, 0.dp),
                placeHolder = R.string.setting_confirm_new_password,
                innerTitle = R.string.setting_confirm_new_password,
                textStyle = TextStyle(fontSize = 16.sp, fontWeight = FontWeight.Medium, color = Color.Black),
                isPassword = true,
                errorMessage = uiState.errorMessageTwo,
                options = KeyboardOptions(keyboardType = KeyboardType.Password),
                state = uiState.inputStateTwo,
                showKeyboard = false,
                onValueChange = {
                    if(it.contains(" ")){
                        T.ssd(com.twl.meeboss.core.ui.R.string.common_cant_contain_space)
                        return@XPasswordTextField
                    }
                    onValueChangedTwo(it)
                }, onStateChanged = {
                    onStateChangedTwo(it)
                })


            Spacer(modifier = Modifier.height(20.dp))
            //登录按钮
            LoginButton(modifier = Modifier
                .padding(16.dp, 0.dp), onClickSave = onClickSave,
                buttonText = stringResource(id = R.string.common_button_confirm), enabled = uiState.canSave)
        }
    }
}