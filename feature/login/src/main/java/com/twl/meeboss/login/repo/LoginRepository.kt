package com.twl.meeboss.login.repo

import com.twl.meeboss.base.foundation.IRepository
import com.twl.meeboss.base.foundation.repo.BaseRepository
import com.twl.meeboss.login.api.LoginApi
import javax.inject.Inject

/**
 * @author: 冯智健
 * @date: 2024年08月10日 17:42
 * @description:
 */
class LoginRepository @Inject constructor(
    private val loginApi: LoginApi
): BaseRepository(), ILoginRepository {

}

interface ILoginRepository: IRepository {

}