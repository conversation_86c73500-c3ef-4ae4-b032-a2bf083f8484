package com.twl.meeboss.geek.export

/**
 * @author: 冯智健
 * @date: 2024年07月19日 16:00
 * @description:
 */
object GeekEventBusKey {

    const val SAVE_JOB_PREFERENCE_INFECT_F1 = "SaveJobPreferenceInfectF1"
    const val VISA_SPONSORSHIP_COMPLETED = "VisaSponsorshipCompleted"
    const val GEEK_CHAT_APPLY_GUIDE_FINISH = "geek_chat_apply_guide_finish"
    const val GEEK_RESUME_ANALYZE_PROFILE_UPDATE_FINISH = "geek_resume_analyze_profile_update_finish"
    const val GEEK_COMPLETE_MANUALLY_FINISH = "geek_complete_manually_finish"
}