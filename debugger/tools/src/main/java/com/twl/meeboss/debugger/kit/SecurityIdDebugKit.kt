package com.twl.meeboss.debugger.kit

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.twl.meeboss.debugger.R
import com.twl.meeboss.debugger.activity.DebuggerSecurityIdActivity

/**
 * 聊天功能调试工具Kit
 * 用于测试聊天功能，输入friendId和对方身份开始聊天
 */
class SecurityIdDebugKit(
    override val icon: Int = R.mipmap.dk_kit_s_runing,
    override val name: Int = R.string.security_id_debug
) : BaseKit() {

    override fun onAppInit(context: Context?) {
        // 初始化逻辑（如果需要）
    }

    override fun onClickWithReturn(activity: Activity): Boolean {
        activity.startActivity(Intent(activity, DebuggerSecurityIdActivity::class.java))
        return true
    }
}
