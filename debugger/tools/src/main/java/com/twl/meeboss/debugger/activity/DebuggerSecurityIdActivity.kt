package com.twl.meeboss.debugger.activity

import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.twl.meeboss.base.components.titlebar.XTitleBar
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.base.foundation.activity.BaseMviActivity
import com.twl.meeboss.base.foundation.viewmodel.BaseLifecycleViewModel
import com.twl.meeboss.base.model.chat.ChatSource
import com.twl.meeboss.chat.export.ChatServiceRouter
import com.twl.meeboss.common.utils.T
import com.twl.meeboss.core.ui.theme.XTheme
import com.twl.meeboss.debugger.R

/**
 * 聊天功能调试工具Activity
 * 用于测试聊天功能，输入friendId和对方身份开始聊天
 */
class DebuggerSecurityIdActivity : BaseMviActivity<BaseLifecycleViewModel>() {

    override val viewModel: BaseLifecycleViewModel by viewModels()

    override fun preInit(intent: Intent) {
        // 预初始化逻辑
    }

    override fun initData() {
        // 初始化数据
    }

    @Composable
    override fun ComposeContent() {
        DebuggerSecurityIdContent()
    }
}

@Composable
private fun DebuggerSecurityIdContent() {
    var friendId by remember { mutableStateOf("") }
    var selectedFriendIdentity by remember { mutableStateOf(UserConstants.GEEK_IDENTITY) }

    val context = LocalContext.current
    val identityOptions = listOf(
        Pair(UserConstants.GEEK_IDENTITY, R.string.friend_identity_geek),
        Pair(UserConstants.BOSS_IDENTITY, R.string.friend_identity_boss)
    )

    XTheme {
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
        ) {
            Column {
                XTitleBar(title = stringResource(id = R.string.security_id_debug_title), showDivider = true)
                
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // FriendId输入框
                    OutlinedTextField(
                        value = friendId,
                        onValueChange = { friendId = it },
                        label = { Text(stringResource(id = R.string.friend_id_input_hint)) },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )

                    // 对方身份选择
                    Text(
                        text = stringResource(id = R.string.select_friend_identity),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    
                    identityOptions.forEach { (identityValue, identityStringRes) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (identityValue == selectedFriendIdentity),
                                    onClick = { selectedFriendIdentity = identityValue },
                                    role = Role.RadioButton
                                )
                                .padding(horizontal = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (identityValue == selectedFriendIdentity),
                                onClick = null
                            )
                            Text(
                                text = stringResource(id = identityStringRes),
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 开始聊天按钮
                    Button(
                        onClick = {
                            if (friendId.isBlank()) {
                                T.ss("请输入有效的friendId")
                                return@Button
                            }

                            // 使用ChatServiceRouter开始聊天
                            if (context is androidx.activity.ComponentActivity) {
                                ChatServiceRouter.startChat(
                                    context = context,
                                    friendId = friendId,
                                    friendIdentity = selectedFriendIdentity,
                                    source = ChatSource.Detail
                                ) { result ->
                                    if (result.isSuccess) {
                                        T.ss("聊天启动成功")
                                    } else {
                                        T.ss("聊天启动失败")
                                    }
                                }
                            } else {
                                T.ss("无法启动聊天：Context类型不正确")
                            }
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(stringResource(id = R.string.start_chat))
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 说明文本
                    Text(
                        text = stringResource(id = R.string.security_id_debug_description),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewDebuggerSecurityIdContent() {
    DebuggerSecurityIdContent()
}
