pluginManagement {
    includeBuild("build-logic")
    repositories {
        maven { url = uri("https://android3.weizhipin.com/nexus/repository/public/") }
        maven { url  = uri("https://developer.huawei.com/repo/") }
        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public/") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin/") }
        maven { url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/") }
        maven { url = uri("https://jitpack.io") }
//        google {
//            content {
//                includeGroupByRegex("com\\.android.*")
//                includeGroupByRegex("com\\.google.*")
//                includeGroupByRegex("androidx.*")
//            }
//        }
        mavenCentral()
//        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url = uri("https://android3.weizhipin.com/nexus/repository/public/") }
        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public/") }
        maven { url = uri("https://mirrors.cloud.tencent.com/nexus/repository/maven-public/") }
        maven { url = uri("https://maven.aliyun.com/nexus/content/groups/public/") }
        maven { url = uri("https://maven.aliyun.com/repository/central") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/jcenter") }
        mavenCentral()
//        maven { url = uri("https://jitpack.io") }
        google()

    }
}

rootProject.name = "MeeBoss"
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
include(":app")
include(":core:common")
include(":core:ui")
include(":core:network")
include(":core:database")
include(":feature:chat")
include(":feature:chat-export")
include(":feature:login")
include(":feature:login-export")
include(":feature:geek")
include(":feature:geek-export")
include(":feature:boss")
include(":feature:boss-export")
include(":feature:setting")
include(":feature:setting-export")
include(":feature:base")
include(":debugger:tools-no-op")
include(":debugger:tools")
include(":debugger:statement")
include(":debugger:autotest-localserver")
include(":debugger:autotest-localserver-nop")
include(":feature:components")
include(":feature:webview")
include(":feature:webview-export")
include(":feature:export-share")
