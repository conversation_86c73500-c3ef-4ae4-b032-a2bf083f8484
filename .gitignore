*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
/.idea/sonarlint
/.idea/AndroidProjectSystem.xml
/.idea/codeStyles
/.idea/deploymentTargetSelector.xml
/.idea/intellij-javadocs-4.0.1.xml
/.idea/runConfigurations.xml
/.kotlin/sessions
/.kotlin/errors
.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties
/.idea/.name
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationComposeConventionPlugin$apply$lambda$0$$inlined$getByType$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationComposeConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationConventionPlugin$apply$1$3.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationConventionPlugin$apply$lambda$1$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationFlavorsConventionPlugin$apply$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationFlavorsConventionPlugin$apply$lambda$0$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationFlavorsConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationFlavorsConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationJacocoConventionPlugin$apply$lambda$1$$inlined$getByType$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidApplicationJacocoConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidBaseFeatureConventionPlugin$apply$1$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidBaseFeatureConventionPlugin$apply$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidBaseFeatureConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidBaseFeatureConventionPlugin$apply$lambda$0$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidBaseFeatureConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidBaseFeatureConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/AndroidComposeKt$configureAndroidCompose$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/AndroidComposeKt$configureAndroidCompose$1$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/AndroidComposeKt$configureAndroidCompose$1$3.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/AndroidComposeKt.class
/build-logic/convention/build/classes/kotlin/main/AndroidFeatureConventionPlugin$apply$1$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidFeatureConventionPlugin$apply$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidFeatureConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidFeatureConventionPlugin$apply$lambda$0$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidFeatureConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidFeatureConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidHiltConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidHiltConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/AndroidInstrumentedTestsKt$disableUnnecessaryAndroidTests$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/AndroidInstrumentedTestsKt.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryComposeConventionPlugin$apply$lambda$0$$inlined$getByType$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryComposeConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryConventionPlugin$apply$1$2$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryConventionPlugin$apply$1$2$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryConventionPlugin$apply$lambda$1$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryJacocoConventionPlugin$apply$lambda$1$$inlined$getByType$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLibraryJacocoConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$1$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$1$2$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$1$3.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$lambda$0$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$lambda$0$$inlined$configure$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$lambda$0$$inlined$configure$3.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$lambda$0$$inlined$configure$4.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$lambda$0$$inlined$configure$5.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$apply$lambda$0$$inlined$configure$6.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidLintConventionPluginKt.class
/build-logic/convention/build/classes/kotlin/main/AndroidRoomConventionPlugin$apply$1$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidRoomConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidRoomConventionPlugin$apply$lambda$0$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidRoomConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidRoomConventionPlugin$RoomSchemaArgProvider.class
/build-logic/convention/build/classes/kotlin/main/AndroidRoomConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/AndroidTestConventionPlugin$apply$1$2.class
/build-logic/convention/build/classes/kotlin/main/AndroidTestConventionPlugin$apply$lambda$1$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/AndroidTestConventionPlugin$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/AndroidTestConventionPlugin.class
/build-logic/convention/build/kotlin/compileKotlin/local-state/build-history.bin
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt$registerTask$1$1$convert$1.class
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt$registerTask$1$1$processGitLog$1$1$describes$1.class
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt$registerTask$1$1$processGitLog$1.class
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt$registerTask$1$1$stringToUnicode$1.class
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt$registerTask$1$1.class
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt$registerTask$1.class
/build-logic/convention/build/classes/kotlin/main/ci/CommonfuncsKt.class
/.idea/compiler.xml
/build-logic/convention/build/libs/convention.jar
/build-logic/convention/build/classes/kotlin/main/META-INF/convention.kotlin_module
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/counters.tab
/.idea/deploymentTargetDropDown.xml
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/DeviceConfig.class
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/FlavorDimension.class
/.idea/git_toolbox_prj.xml
/.idea/gradle.xml
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/GradleManagedDevicesKt$configureGradleManagedDevices$1$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/GradleManagedDevicesKt$configureGradleManagedDevices$1$1$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/GradleManagedDevicesKt$configureGradleManagedDevices$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/GradleManagedDevicesKt$configureGradleManagedDevices$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/GradleManagedDevicesKt.class
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$$inlined$configure$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$2$reportTask$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$2$reportTask$1$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$2$reportTask$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$3$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$3$execute$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$3$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$configureJacoco$3.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/JacocoKt.class
/build-logic/convention/build/classes/kotlin/main/JvmLibraryConventionPlugin.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlin$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlin$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$3.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$4$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$4.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinAndroid$1$5.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinJvm$$inlined$configure$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$configureKotlinJvm$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt$inlined$sam$i$org_gradle_api_Action$0.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/KotlinAndroidKt.class
/.idea/kotlinc.xml
/build-logic/convention/build/kotlin/compileKotlin/cacheable/last-build.bin
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.s
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len
/build-logic/convention/build/tmp/jar/MANIFEST.MF
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/MappingKt$configureUploadMappingTasks$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/MappingKt$registerUploadMappingTask$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/MappingKt.class
/.idea/migrations.xml
/.idea/misc.xml
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/NiaBuildType.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/NiaFlavor.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/NiaFlavorKt$configureFlavors$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/NiaFlavorKt$configureFlavors$2$1$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/NiaFlavorKt$configureFlavors$2$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/NiaFlavorKt.class
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/PrintApkLocationTask.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/PrintTestApksKt$configurePrintApksTask$1$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/PrintTestApksKt$configurePrintApksTask$1$testSources$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/PrintTestApksKt$configurePrintApksTask$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/PrintTestApksKt.class
/.idea/inspectionProfiles/Project_Default.xml
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/ProjectExtensionsKt$special$$inlined$getByType$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/ProjectExtensionsKt.class
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len
/build-logic/convention/build/kotlin/compileKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i
/build-logic/convention/build/kotlin/compileKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len
/build-logic/convention/build/pluginDescriptors/twl.android.application.compose.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.android.application.compose.properties
/build-logic/convention/build/pluginDescriptors/twl.android.application.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.android.application.properties
/build-logic/convention/build/pluginDescriptors/twl.android.base.feature.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.android.base.feature.properties
/build-logic/convention/build/pluginDescriptors/twl.android.feature.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.android.feature.properties
/build-logic/convention/build/pluginDescriptors/twl.android.library.compose.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.android.library.compose.properties
/build-logic/convention/build/pluginDescriptors/twl.android.library.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.android.library.properties
/build-logic/convention/build/pluginDescriptors/twl.jvm.library.properties
/build-logic/convention/build/resources/main/META-INF/gradle-plugins/twl.jvm.library.properties
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/UploadMappingFileTask$Companion.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/UploadMappingFileTask$generateSig$1.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/UploadMappingFileTask$generateSig$2.class
/build-logic/convention/build/classes/kotlin/main/com/google/samples/apps/nowinandroid/UploadMappingFileTask.class
/.idea/vcs.xml
