[versions]
# android versions
compileSdk = "34"
minSdk = "29"
targetSdk = "34"
androidTools = "31.4.2"
versionCode = "112000" #最后两位保持00不变

# google versions
agp = "8.3.2"
kotlin = "1.9.22"
ksp = "1.9.22-1.0.18"
kotlinxDatetime = "0.5.0"
androidxCore = "1.13.1"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
lifecycleRuntimeKtx = "2.8.3"
lifecycleExtensions = "2.2.0"
activityCompose = "1.9.0"
androidxComposeCompiler = "1.5.10"
foundation = "1.6.8"
composeBom = "2025.01.00"
fragmentKtx = "1.5.2"
lifecycleViewmodelCompose = "2.8.3"
lifecycleRuntimeCompose = "2.8.3"
constraintlayoutCompose = "1.0.1"
appcompat = "1.6.1"
material = "1.11.0"
commonsCodec = "1.16.1"
coreSplashscreen = "1.0.1"
androidDesugarJdkLibs = "2.0.4"
hilt = "2.51.1"
hiltExt = "1.1.0"
room = "2.6.1"
roomWcdb = "1.0.8"
paging = "3.3.0"
accompanistPager = "0.34.0"

# company versions
tlog = "1.1.12"
net-util = "0.0.3"
liveEventBus = "0.0.1"
nativekit = "0.0.1"
luban="0.0.1"
badger="1.0.5"
meebossProcessors = "0.0.1"
symbolProcessingApi = "1.9.22-1.0.16"
atLocalServerDebug = "0.0.4-SNAPSHOT"

# third part versions
mmkv = "1.3.7"
wmRouter = "1.2.1"
wmRouterApt = "0.0.5-beta1"
retrofit = "2.9.0"
okhttp = "4.12.0"
gson = "2.10.1"
moshi = "1.15.1"
dokitx = "3.7.1"
dokitxPlugin = "3.7.1-v1"
bytecodeutilPlugin = "2.1.1"
chucker = "4.0.0"
permissionx = "1.8.1"
landscapist = "2.3.5"
androidsvg = "1.4"
mavericks = "3.0.9"
lz4Java = "1.8.0"
refresh = "1.3.0"
protobuf_format = "1.4"
protobuf = "3.25.0"
sentry = "7.14.0"
sentryPlugin = "4.11.0"
pictureSelector="0.0.2"
ucrop="0.0.1"
compress="0.0.1"
camerax="0.0.1"
lottie="6.5.2"
firebaseBom = "33.7.0"
gmsAuth = "21.3.0"
gmsPlugin = "4.4.2"
xkit = "0.0.9"
appsflyer = "6.15.2"
installreferrer = "2.2"
hmsComponentverifysdk = "13.3.1.301"
miuiHomereferrer = "1.0.0.6"
systemuicontroller = "0.31.0-alpha"
composeRichEditor = "1.0.0-rc10"
localHtmlVersion = "1.7.1"
imPaasSdkVersion = "1.2.2-SNAPSHOT"


[libraries]
# google libraries
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinxDatetime" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidxCore" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-extensions = { group = "androidx.lifecycle", name = "lifecycle-extensions", version.ref = "lifecycleExtensions" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-material = { group = "androidx.compose.material", name = "material" }
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragmentKtx" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycleRuntimeCompose" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintlayoutCompose" }
androidx-compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
androidx-compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-ui-util = { module = "androidx.compose.ui:ui-util" }
androidx-compose-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4" }
androidx-compose-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }
androidx-compose-runtime-livedata = { module = "androidx.compose.runtime:runtime-livedata" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "coreSplashscreen" }
android-support-multidex = { module = "com.android.support:multidex", version = "1.0.3" }
android-desugarJdkLibs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "androidDesugarJdkLibs" }
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-android-testing = { group = "com.google.dagger", name = "hilt-android-testing", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }
hilt-ext-compiler = { group = "androidx.hilt", name = "hilt-compiler", version.ref = "hiltExt" }
hilt-ext-work = { group = "androidx.hilt", name = "hilt-work", version.ref = "hiltExt" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
wcdb-room = {module = "com.tencent.wcdb:room" ,version.ref = "roomWcdb"}
wcdb-room-android = {module = "com.tencent.wcdb:wcdb-android" ,version.ref = "roomWcdb"}
room-paging = { module = "androidx.room:room-paging", version.ref = "room" }
paging-runtime-ktx = { module = "androidx.paging:paging-runtime-ktx", version.ref = "paging" }
paging-compose = { module = "androidx.paging:paging-compose", version.ref = "paging" }
accompanist-pager = { module = "com.google.accompanist:accompanist-pager", version.ref = "accompanistPager"}
androidx-compose-foundation = { module = "androidx.compose.foundation:foundation", version.ref = "foundation"}
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "systemuicontroller"}


# company libraries
tlog = { group = "com.techwolf.lib", name = "tlog", version.ref = "tlog" }
net-util = {group = "com.twl.zhipin",name = "net-util",version.ref = "net-util"}
live-eventbus = { module = "com.twl.sdk:live-event-bus", version.ref = "liveEventBus"}
nativekit = { module = "com.twl.sdk:nativekit", version.ref = "nativekit" }
luban={module = "com.twl.sdk:luban", version.ref = "luban"}
xkit = {module = "com.twl.sdk:xkit", version.ref = "xkit"}
badger={module = "com.zhipin:deskBadger", version.ref = "badger"}
symbol-processing-api = { module = "com.google.devtools.ksp:symbol-processing-api", version.ref = "symbolProcessingApi" }
meeboss-processors = { module = "com.twl.sdk:meeboss-processors", version.ref = "meebossProcessors" }
autotest-localserver-debug={ module = "com.bzl.dz:autotest-localserver", version.ref = "atLocalServerDebug" }

# Third part libraries
mmkv = { group = "com.tencent", name = "mmkv", version.ref = "mmkv" }
landscapist-bom = { module = "com.github.skydoves:landscapist-bom", version.ref = "landscapist" }
landscapist-coil = { module = "com.github.skydoves:landscapist-coil", version.ref = "landscapist" }
landscapist-glide = { module = "com.github.skydoves:landscapist-glide", version.ref = "landscapist" }
landscapist-placeholder = { module = "com.github.skydoves:landscapist-placeholder", version.ref = "landscapist" }
landscapist-transformation = { module = "com.github.skydoves:landscapist-transformation", version.ref = "landscapist" }
landscapist-animation = { module = "com.github.skydoves:landscapist-animation", version.ref = "landscapist" }
lottie = { module = "com.airbnb.android:lottie-compose", version.ref = "lottie" }
mavericks = { module = "com.airbnb.android:mavericks", version.ref = "mavericks" }
mavericks-compose = { module = "com.airbnb.android:mavericks-compose", version.ref = "mavericks" }
mavericks-hilt = { module = "com.airbnb.android:mavericks-hilt", version.ref = "mavericks" }
wm-router = { group = "io.github.meituan-dianping", name = "router", version.ref = "wmRouter" }
wm-router-compiler = { group = "com.bzl.plugins.oneapt", name = "wm_router_apt", version.ref = "wmRouterApt" }
android-util-code = { module = "com.blankj:utilcodex", version = "1.31.1" }
#live-event-bus = { module = "com.github.neo-turak:LiveEventBus", version.ref = "liveEventBus" }
permissionx = { module = 'com.guolindev.permissionx:permissionx', version.ref = "permissionx" }
dokit-dokitx = { module = "io.github.didi.dokit:dokitx", version.ref = "dokitx" }
dokit-dokitx-no-op = { module = "io.github.didi.dokit:dokitx-no-op", version.ref = "dokitx" }
chucker = { module = "com.github.chuckerteam.chucker:library", version.ref = "chucker" }
chucker-no-op = { module = "com.github.chuckerteam.chucker:library-no-op", version.ref = "chucker" }
retrofit-runtime = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-moshi = { module = "com.squareup.retrofit2:converter-moshi", version.ref = "retrofit" }
moshi = { module = "com.squareup.moshi:moshi-kotlin", version.ref = "moshi" }
moshi-codegen = { module = "com.squareup.moshi:moshi-kotlin-codegen", version.ref = "moshi" }
okhttp = { group = "com.squareup.okhttp3",name="okhttp", version.ref = "okhttp" }
lz4-java = { module = "org.lz4:lz4-pure-java", version.ref = "lz4Java" }
commons-codec = { module = "commons-codec:commons-codec", version.ref = "commonsCodec" }
refresh = { module = "com.github.jenly1314.UltraSwipeRefresh:refresh", version.ref = "refresh" }
refresh-indicator = { module = "com.github.jenly1314.UltraSwipeRefresh:refresh-indicator-classic", version.ref = "refresh" }
protobuf-format= { module = "com.googlecode.protobuf-java-format:protobuf-java-format", version.ref = "protobuf_format" }
protobuf = { module = "com.google.protobuf:protobuf-java", version.ref = "protobuf" }
sentry = { module = "io.sentry:sentry-android", version.ref = "sentry" }
pictureSelector = { module = "com.twl.sdk:pictureselector", version.ref = "pictureSelector" }
pictureSelector-ucrop = { module = "com.twl.sdk:ucrop", version.ref = "ucrop" }
pictureSelector-compress = { module = "com.twl.sdk:compress", version.ref = "compress" }
pictureSelector-camerax = { module = "com.twl.sdk:camerax", version.ref = "camerax" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBom" }
firebase-auth-ktx = { module = "com.google.firebase:firebase-auth-ktx" }
firebase-messaging-ktx = { module = "com.google.firebase:firebase-messaging-ktx" }
firebase-analytics-ktx = { module = "com.google.firebase:firebase-analytics-ktx" }
gms-auth = { module = "com.google.android.gms:play-services-auth", version.ref = "gmsAuth" }
# Dependencies of the included build-logic
android-gradle-plugin = { group = "com.android.tools.build", name = "gradle", version.ref = "agp" }
android-tools-common = { group = "com.android.tools", name = "common", version.ref = "androidTools" }
room-gradle-plugin = { group = "androidx.room", name = "room-gradle-plugin", version.ref = "room" }
compose-gradle-plugin = { module = "org.jetbrains.kotlin:compose-compiler-gradle-plugin", version.ref = "kotlin" }
kotlin-gradle-plugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradle-plugin = { group = "com.google.devtools.ksp", name = "com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
wm-router-plugin = { module = "com.bzl.plugins.oneapt:wm_router_plugin", version.ref = "wmRouterApt" }
dokit-compat-plugin = { module = "com.github.Ysj001:DoKitPluginCompat", version.ref = "dokitxPlugin" }
bytecodeutil-plugin = { module = "com.github.Ysj001.BytecodeUtil:plugin", version.ref = "bytecodeutilPlugin" }
appsflyer = {module = "com.appsflyer:af-android-sdk", version.ref = "appsflyer"}
installreferrer = {module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer"}
hms-componentverifysdk = {module = "com.huawei.hms:componentverifysdk", version.ref = "hmsComponentverifysdk"}
miui-homereferrer = {module = "com.miui.referrer:homereferrer", version.ref = "miuiHomereferrer" }
compose-richeditor = {module = "com.mohamedrejeb.richeditor:richeditor-compose", version.ref = "composeRichEditor" }
localhtml = {module = "com.bzl.dz:boss-local-html", version.ref = "localHtmlVersion" }
androidsvg = {module = "com.caverock:androidsvg-aar", version.ref = "androidsvg"}
impaassdk = {module = "com.bzl.dz:im-paas-foundation", version.ref = "imPaasSdkVersion"}

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
room = { id = "androidx.room", version.ref = "room" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
android-test = { id = "com.android.test", version.ref = "agp" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
sentry = { id = "io.sentry.android.gradle", version.ref = "sentryPlugin" }
gmsPlugin = { id = "com.google.gms.google-services", version.ref = "gmsPlugin"}

#Plugins defined by this project
twl-android-application = { id = "twl.android.application", version = "unspecified" }
twl-android-application-compose = { id = "twl.android.application.compose", version = "unspecified" }
twl-android-library = { id = "twl.android.library", version = "unspecified" }
twl-android-library-compose = { id = "twl.android.library.compose", version = "unspecified" }
twl-android-feature = { id = "twl.android.feature", version = "unspecified" }
twl-jvm-library = { id = "twl.jvm.library", version = "unspecified" }
twl-android-room = { id = "twl.android.room", version = "unspecified" }


