package com.twl.meeboss.common.preference

object SpKey {
    const val KEY_U_TICKET = "ac_u_ticket"
    const val KEY_U_SECRET_KEY = "ac_u_secret_key"
    const val KEY_U_REGISTER = "ac_u_register"
    const val KEY_U_IDENTITY = "ac_u_identity"
    const val KEY_U_FIRST_COMPLETE_STATUS = "ac_u_first_complete_status"
    const val KEY_U_ID = "ac_u_id"
    const val KEY_U_PAAS_UID = "ac_u_paas_uid"
    const val KEY_U_PAAS_USER_TOKEN = "ac_u_paas_user_token"
    const val KEY_SEND_EMAIL_TIME = "sp_key_send_email_time"
    const val KEY_SELECT_IDENTITY_BEFORE_LOGIN = "key_select_identity_before_login"
    const val KEY_TAG_AFTER_LOGIN = "KEY_TAG_AFTER_LOGIN"
    const val KEY_CHAT_SHOW_CONNECTED_DIALOG = "key_chat_show_connected_dialog"
    const val KEY_EMPLOYERS_VISIBILITY = "key_employers_visibility"
    const val KEY_MAIN_NOTIFICATION_GUIDE = "key_main_notification_guide"
    const val KEY_NOTIFICATION_PERMISSION_DENIED = "key_notification_permission_denied"
    const val KEY_CHAT_TAB_NOTIFICATION_CLOSE_DATE = "key_chat_tab_notification_close_date"
    const val KEY_APP_COUNTRY = "key_app_country"
    const val KEY_APP_LANGUAGE = "key_app_language"
    const val KEY_APP_ENVIRONMENT = "key_app_environment"
    const val KEY_HAS_SYNCED_COUNTRY = "key_has_synced_country"
    const val KEY_SERVER_CONFIG_HOST_BEAN = "key_server_config_host_bean"
    const val KEY_AREA_CODE = "key_area_code"
    const val RECENTLY_LOGIN_PASSWORD_KEY = "recently_login_account_key"
    const val RECENTLY_USED_STICKERS = "recently_used_stickers"
    const val KEYBOARD_HEIGHT = "mb_keyboard_height"
    const val KEY_AB_TEST_BEAN = "key_ab_test_bean"
    const val KEY_BOSS_SHOW_TALENT_POOL_INSTRUCTION_DIALOG = "key_boss_show_talent_pool_instruction_dialog"
}