package com.twl.meeboss.core.ui.ktx

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import com.blankj.utilcode.util.ScreenUtils

/**
 * 组件由不可见变为可见时触发
 */
fun Modifier.onVisible(
    block: () -> Unit
): Modifier = composed {
    var wasVisible by remember { mutableStateOf(false) }
    val screenHeight = ScreenUtils.getScreenHeight()
    val screenWidth = ScreenUtils.getScreenWidth()

    this.onGloballyPositioned { coordinates ->
        val bounds = coordinates.boundsInWindow()

        // 只要组件与屏幕有任何交集就认为可见
        val isVisible = bounds.right > 0      // 右边界在屏幕左边界右侧
                && bounds.left < screenWidth  // 左边界在屏幕右边界左侧
                && bounds.bottom > 0          // 下边界在屏幕上边界下方
                && bounds.top < screenHeight  // 上边界在屏幕下边界上方

        // 从不可见变为可见时触发
        if (isVisible && !wasVisible) {
            block()
        }

        wasVisible = isVisible
    }
}