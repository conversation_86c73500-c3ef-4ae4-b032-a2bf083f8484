package com.twl.meeboss.core.ui.theme

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp


@Preview
@Composable
fun PreviewColors(@PreviewParameter(ColorPreviewProvider::class) colors: List<Color>) {
    Row {
        colors.forEach {
            Box(modifier = Modifier
                .size(40.dp)
                .background(it))
        }
    }
}

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

//val Primary = Color(0xFF15B3B3)

val Primary50 = Color(0x7F15B3B3)
val Primary30 = Color(0x4D15B3B3)
val Primary20 = Color(0x3315B3B3)
val Primary10 = Color(0x1915B3B3)

val Black222222 = Color(0xFF222222)
val Black484848 = Color(0xFF484848)
val BlackEBEBEB = Color(0xFFEBEBEB)
val RedBD222B = Color(0xFFBD222B)
val RedFF3E3E = Color(0xFFFF3E3E)
val BlackBD222B_8 = Color(0x19BD222B)
val Black888888 = Color(0xFF888888)
val Black020202 = Color(0xFF020202)
val COLOR_00000000 = Color(0x00000000)
val GRAY_AAAAAA = Color(0xFFAAAAAA)
val GRAY_EEEEEE = Color(0xFFEEEEEE)

val COLOR_F6F6F6 = Color(0xFFF6F6F6)
val COLOR_F5F5F5 = Color(0xFFF5F5F5)

val COLOR_354848 = Color(0xFF354848)

val COLOR_DDDDDD = Color(0xFFDDDDDD)
val COLOR_666666 = Color(0xFF666666)
val COLOR_F3F3F3 = Color(0xFFF3F3F3)
val COLOR_0D9EA3 = Color(0xFF0D9EA3)
val COLOR_FF7D00 = Color(0xFFFF7D00)
val COLOR_AAAAAA = Color(0xFFAAAAAA)
val COLOR_B8B8B8 = Color(0xFFB8B8B8)
val COLOR_858585 = Color(0xFF858585)
val COLOR_CCCCCC = Color(0xFFCCCCCC)
val COLOR_13C361 = Color(0xFF13C361)
val COLOR_07727A = Color(0xFF07727A)
val COLOR_202430 = Color(0XFF202430)
val COLOR_00FFFFFF = Color(0x00FFFFFF)
val COLOR_F5FFFFFF = Color(0xF5FFFFFF)
val COLOR_99FFFFFF = Color(0x99FFFFFF)
val COLOR_1A15B3B3 = Color(0x1A15B3B3)
val COLOR_8015B3B3 = Color(0x8015B3B3)
val COLOR_B3000000 = Color(0xB3000000)
val COLOR_4DFFFFFF = Color(0x4DFFFFFF)
val COLOR_000000 = Color(0xFF000000)
val COLOR_3AC248 = Color(0xFF3AC248)
val COLOR_1F3AC248 = Color(0x1F3AC248)
val COLOR_BD222B = Color(0xFFBD222B)
val COLOR_E3525A = Color(0xFFE3525A)
val COLOR_FAEEEE = Color(0xFFFAEEEE)
val COLOR_29787880 = Color(0x29787880)
val COLOR_FFC0EB5E = Color(0xFFC0EB5E)
val COLOR_B3B3B3 = Color(0xFFB3B3B3)
val COLOR_F5F5F6 = Color(0xFFF5F5F6)
val COLOR_E8F7C6 = Color(0xFFE8F7C6)
val COLOR_06605A = Color(0xFF06605A)
val COLOR_15B3B3 = Color(0xFF15B3B3)
val COLOR_F8F8F8 = Color(0xFFF8F8F8)
val COLOR_80000000 = Color(0x80000000)
val COLOR_2B8F88 = Color(0xFF2B8F88)
val COLOR_D6DDEB = Color(0xFFD6DDEB)
val COLOR_FF4D57 = Color(0XFFFF4D57)
val COLOR_EB5721 = Color(0XFFEB5721)
val COLOR_FFE9E1 = Color(0XFFFFE9E1)
val COLOR_EBFFFF = Color(0xFFEBFFFF)
val COLOR_FE4853 = Color(0xFFFE4853)
val COLOR_2EE381_12 = Color(0x1F2EE381)
val COLOR_CDF5E1 = Color(0xFFCDF5E1)
val COLOR_E5E5E5 = Color(0xFFE5E5E5)


//region 品牌色
//品牌主色、品牌氛围渲染，情感传递，按钮底色、文字高亮、特殊强调
val COLOR_C5FFE2 = Color(0xFFC5FFE2)
val COLOR_9FFFCF = Color(0xFF9FFFCF)
val COLOR_7AFFBC = Color(0xFF7AFFBC)
val COLOR_2FFF97 = Color(0xFF2FFF97)
val COLOR_15E57D = Color(0xFF15E57D)
val COLOR_00BC5E = Color(0xFF00BC5E)
val COLOR_028847 = Color(0xFF028847)
val COLOR_005F31 = Color(0xFF005F31)
val COLOR_004725 = Color(0xFF004725)
val COLOR_03301A = Color(0xFF03301A)
val COLOR_16AC3C = Color(0xFF16AC3C)
val COLOR_1234C759 = Color(0x1234C759)


//功能场景：提示、提醒 / 业务场景：C端
val COLOR_FFF1E5 = Color(0xFFFFF1E5)
val COLOR_FFD8B8 = Color(0xFFFFD8B8)
val COLOR_FFC08A = Color(0xFFFFC08A)
val COLOR_FFA75D = Color(0xFFFFA75D)
val COLOR_FF9235 = Color(0xFFFF9235)
val COLOR_FF7602 = Color(0xFFFF7602)
val COLOR_D66200 = Color(0xFFD66200)
val COLOR_AD5000 = Color(0xFFAD5000)
val COLOR_853D00 = Color(0xFF853D00)
val COLOR_5C2A00 = Color(0xFF5C2A00)


//安全主色、业务氛围渲染，主按钮底色、文字高亮、特殊强调
val COLOR_E5F4FF = Color(0xFFE5F4FF)
val COLOR_B8E2FF = Color(0xFFB8E2FF)
val COLOR_8BCFFF = Color(0xFF8BCFFF)
val COLOR_5DBDFF = Color(0xFF5DBDFF)
val COLOR_30AAFF = Color(0xFF30AAFF)
val COLOR_028EEF = Color(0xFF028EEF)
val COLOR_0075C6 = Color(0xFF0075C6)
val COLOR_005D9D = Color(0xFF005D9D)
val COLOR_004575 = Color(0xFF004575)
val COLOR_002D4C = Color(0xFF002D4C)
val COLOR_0025E0 = Color(0xFF0025E0)
val COLOR_F0F2FF = Color(0xFFF0F2FF)

//辅助色
val COLOR_FFF7FB = Color(0xFFFFF7FB)
val COLOR_FFD2E9 = Color(0xFFFFD2E9)
val COLOR_DE93BA = Color(0xFFDE93BA)
val COLOR_C57EA3 = Color(0xFFC57EA3)
val COLOR_F8A9D2 = Color(0xFFF8A9D2)
val COLOR_925876 = Color(0xFF925876)
val COLOR_794660 = Color(0xFF794660)
val COLOR_5F354B = Color(0xFF5F354B)
val COLOR_462636 = Color(0xFF462636)

//功能场景：成功、积极、鼓励
val COLOR_F3FFED = Color(0xFFF3FFED)
val COLOR_D9FFC6 = Color(0xFFD9FFC6)
val COLOR_BFFF9F = Color(0xFFBFFF9F)
val COLOR_A4FE77 = Color(0xFFA4FE77)
val COLOR_81EE4B = Color(0xFF81EE4B)
val COLOR_67CC35 = Color(0xFF67CC35)
val COLOR_50AA23 = Color(0xFF50AA23)
val COLOR_3B8814 = Color(0xFF3B8814)
val COLOR_28660A = Color(0xFF28660A)
val COLOR_184403 = Color(0xFF184403)


//业务场景：B端
val COLOR_F0EEFF = Color(0xFFF0EEFF)
val COLOR_CEC8FF = Color(0xFFCEC8FF)
val COLOR_ACA2FF = Color(0xFFACA2FF)
val COLOR_8A7CFF = Color(0xFF8A7CFF)
val COLOR_6352F2 = Color(0xFF6352F2)
val COLOR_4A39D0 = Color(0xFF4A39D0)
val COLOR_3425AE = Color(0xFF3425AE)
val COLOR_22158C = Color(0xFF22158C)
val COLOR_14096A = Color(0xFF14096A)
val COLOR_0A0248 = Color(0xFF0A0248)

//功能场景：危险、警告、失败
val COLOR_FFECEE = Color(0xFFFFECEE)
val COLOR_FFC3C7 = Color(0xFFFFC3C7)
val COLOR_FF9AA0 = Color(0xFFFF9AA0)
val COLOR_FF7179 = Color(0xFFFF7179)
val COLOR_FF4752 = Color(0xFFFF4752)
val COLOR_DD333E = Color(0xFFDD333E)
val COLOR_BB222C = Color(0xFFBB222C)
val COLOR_99151D = Color(0xFF99151D)
val COLOR_770A11 = Color(0xFF770A11)
val COLOR_550308 = Color(0xFF550308)

//其他
val COLOR_222222 = Color(0xFF222222)
val COLOR_888888 = Color(0xFF888888)
val COLOR_484848 = Color(0xFF484848)
val COLOR_D5F6E5 = Color(0xFFD5F6E5)
val COLOR_E0F8EB = Color(0xFFE0F8EB)
val COLOR_BFFEC6 = Color(0xFFBFFEC6)
val COLOR_E4DBFF = Color(0xFFE4DBFF)
val COLOR_FFE000 = Color(0xFFFFE000)
val COLOR_FFF9C9 = Color(0XFFFFF9C9)
val COLOR_E5E5EA = Color(0xFFE5E5EA)
val COLOR_FFF3E9 = Color(0xFFFFF3E9)
val COLOR_E86802 = Color(0xFFE86802)
fun Color.alpha(alpha: Float) = copy(alpha = alpha)

//品牌色
val Primary = COLOR_00BC5E
val Secondary = COLOR_028847
val Button_Color = COLOR_222222
val Button_Unable_Color = COLOR_AAAAAA
//endregion








