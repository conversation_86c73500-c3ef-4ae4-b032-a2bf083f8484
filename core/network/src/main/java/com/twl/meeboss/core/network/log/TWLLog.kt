package com.twl.meeboss.core.network.log

import android.util.Log
import com.twl.meeboss.core.network.BuildConfig
import com.twl.meeboss.core.network.log.TWLLog.FilterType.API_NAME
import com.twl.meeboss.core.network.log.TWLLog.FilterType.URL
import org.json.JSONObject


/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2020-07-18 16:14
 *     desc   : log工具类
 *     version: v3.1.2
 * </pre>
 */
object TWLLog {

    /**
     * 是否打印log
     */
    private var LOG_ENABLE = BuildConfig.DEBUG

    /**
     * 过滤集
     */
    private var filterList = arrayListOf<FilterBean>()


    fun d(tag: String, msg: String) {
        try {
            if (LOG_ENABLE) {
                Log.i(tag, msg)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    @JvmStatic
    fun d(tag: String, msg: String, tr: Throwable) {
        try {
            if (LOG_ENABLE) {
                Log.i(tag, msg, tr)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    @JvmStatic
    fun i(tag: String, msg: String) {
        try {
            if (LOG_ENABLE) {
                Log.i(tag, msg)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun i(tag: String, tr: Throwable) {
        try {
            if (LOG_ENABLE) {
                Log.i(tag, "", tr)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    @JvmStatic
    fun e(tag: String, msg: String) {
        try {
            if (LOG_ENABLE) {
                Log.e(tag, msg)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }


    @JvmStatic
    fun json(tag: String, msg: String) {

        /**
         * 拼接log
         */
        val sBuilder = StringBuilder()

        /**
         * 开始语
         */
        val startHint = " \n----------------------------- $tag 开始了----------------------------->>>\n"

        /**
         * 结束语
         */
        val endHint = "\n----------------------------- $tag 结束 over-----------------------------<<<\n"

        try {
            if (LOG_ENABLE) {

                sBuilder.append(startHint)

                sBuilder.append("\n ${JsonUtil.formatJson(msg)} \n")

                sBuilder.append(endHint)

                Log.i(tag, sBuilder.toString())
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    @JvmStatic
    fun iTag(tag: String?, log: String?) {

        if (!LOG_ENABLE) return

        /**
         * 拼接log
         */
        val sBuilder = StringBuilder()

        /**
         * 开始语
         */
        val startHint = " \n----------------------------- $tag 开始了----------------------------->>>\n"

        /**
         * 结束语
         */
        val endHint = "\n----------------------------- $tag 结束 over-----------------------------<<<\n"


        sBuilder.append(startHint)

        sBuilder.append("\n $log \n")

        sBuilder.append(endHint)

        Log.i(tag, sBuilder.toString())
    }

    @JvmStatic
    fun dTag(tag: String?, log: String?) {

        if (!LOG_ENABLE) return

        /**
         * 拼接log
         */
        val sBuilder = StringBuilder()

        /**
         * 开始语
         */
        val startHint = " \n----------------------------- $tag 开始了----------------------------->>>\n"

        /**
         * 结束语
         */
        val endHint = "\n----------------------------- $tag 结束 over-----------------------------<<<\n"


        sBuilder.append(startHint)

        sBuilder.append("\n $log \n")

        sBuilder.append(endHint)

        Log.d(tag, sBuilder.toString())
    }

    @JvmStatic
    fun eTag(tag: String?, log: String?) {

        if (!LOG_ENABLE) return

        /**
         * 拼接log
         */
        val sBuilder = StringBuilder()

        /**
         * 开始语
         */
        val startHint = " \n----------------------------- $tag 开始了----------------------------->>>\n"

        /**
         * 结束语
         */
        val endHint = "\n----------------------------- $tag 结束 over-----------------------------<<<\n"


        sBuilder.append(startHint)

        sBuilder.append("\n $log \n")

        sBuilder.append(endHint)

        Log.e(tag, sBuilder.toString())
    }

    /**
     * 网络请求打印log
     *
     * @param url               请求链接
     * @param traceId           traceId
     * @param commonParams      公共参数
     * @param requestParams     请求特定参数
     * @param response          响应结果
     * @param tag               标识
     * @param isParamsExpand    参数是否展开 默认关闭
     * @param isResponseExpand  响应参数是否展开 默认展开
     */
    @JvmStatic
    fun i4Net(
        url: String?,
        traceId: String?,
        commonParams: String?,
        requestParams: String,
        response: String?,
        tag: String?,
        isParamsExpand: Boolean = false,
        isResponseExpand: Boolean = true
    ) {
        if (!LOG_ENABLE) return

        /**
         * 过滤log
         */
        if (filterLog(url, tag)) return

        /**
         * 拼接log
         */
        val sBuilder = StringBuilder()

        /**
         * 开始语
         */
        val startHint = " \n==========================================================  请求开始 api log start  ==========================================================>>>\n"

        /**
         * 结束语
         */
        val endHint = "==========================================================  请求结束了 api log end ==========================================================<<<\n"

        sBuilder.append(startHint)
        sBuilder.append("请求链接：HTTPS  $url@${traceId} \n")

        // 展示公共参数
        if (!commonParams.isNullOrEmpty()) {
            if (!isParamsExpand)
                sBuilder.append("公共参数：${commonParams} \n")
            else
                sBuilder.append("公共参数：${JsonUtil.formatJson(commonParams)} \n")
        }

        // 展示请求特定参数
        if (requestParams.isNotEmpty()) {
            if (!isParamsExpand)
                sBuilder.append("请求参数：$requestParams \n")
            else
                sBuilder.append("请求参数：${JsonUtil.formatJson(requestParams)} \n")
        }

        if (isResponseExpand) {
            if (isJson(response)) {
                val data = JsonUtil.formatJson(response)
                if (data.length > 4000) {
                    sBuilder.append("请求结果： ${data.substring(0, 4000)} \n")
                } else {
                    sBuilder.append("请求结果： $data \n")
                }
            } else {
                val length = response?.length ?: 0
                if (length > 4000) {
                    sBuilder.append("请求结果： ${response?.substring(0, 4000)} \n")
                } else {
                    sBuilder.append("请求结果： $response \n")
                }
            }
        } else {
            val length = response?.length ?: 0
            if (length > 4000) {
                sBuilder.append("请求结果： ${response?.substring(0, 4000)} \n")
            } else {
                sBuilder.append("请求结果： $response \n")
            }
        }

        sBuilder.append(endHint)

        try {
            val newTag = "go go go $tag"
            dispatchLog4Limit(sBuilder.toString(), newTag)
        } catch (e: Throwable) {
            Log.i(tag, sBuilder.toString())
        }
    }

    private fun isJson(string: String?): Boolean {
        if (string.isNullOrBlank()) {
            return false
        }
        try {
            JSONObject(string)
            return true
        } catch (e: Exception) {
            return false
        }
    }

    /**
     * 打印超出 4*1024 限制的log
     * 递归打印
     */
    @Throws
    private fun dispatchLog4Limit(log: String, tag: String?) {
        /**
         * log最大限制
         */
        val maxLength = 4 * 1000

        /**
         * 中英文一律按字符算
         */
        val logBytes = log.toByteArray(charset("utf-8"))

        if (logBytes.size > maxLength) {

            val startLog = String(logBytes.copyOfRange(0, maxLength))
            val endLog = String(logBytes.copyOfRange(maxLength, logBytes.size))

            Log.i(tag, startLog)

            dispatchLog4Limit(endLog, tag)
        } else {
            Log.i(tag, log)
        }
    }

    /**
     * 为log添加过滤条件
     *
     * @param filterType    过滤类型
     * @param filterValue   过滤条件
     */
    fun addFilter4Log(filterType: FilterType, filterValue: String?): TWLLog {

        if (filterValue.isNullOrEmpty()) return this

        //过滤相同filterValue
        var isAddSame = false
        filterList.forEach {
            if (filterType == it.filterType && it.filterValue == filterValue) {
                isAddSame = true
                return@forEach
            }
        }
        if (isAddSame) return this

        filterList.add(FilterBean(filterType, filterValue))

        return this
    }

    /**
     * 过滤log
     */
    private fun filterLog(url: String?, tag: String?): Boolean {

        var isFilterReturn = false

        filterList.forEach {
            //过滤ApiName Url
            if (it.filterType == FilterType.API_NAME && it.filterValue.trim() == tag.toString().trim() ||
                (it.filterType == FilterType.URL && it.filterValue.trim() == url.toString().trim())
            ) {
                isFilterReturn = true
                return@forEach
            }
        }

        return isFilterReturn
    }

    /**
     * 过滤类型
     *
     * @sample API_NAME f1.recommend.list.v5
     * @sample URL      [https://kanzhun-api1.weizhipin.com/api/f1.recommend.list.v5]
     */
    enum class FilterType {
        API_NAME,
        URL
    }

    data class FilterBean(var filterType: FilterType, var filterValue: String)
}
