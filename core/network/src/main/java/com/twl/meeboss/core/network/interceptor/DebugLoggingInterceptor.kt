package com.twl.meeboss.core.network.interceptor

import android.net.Uri
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.common.ktx.toJson
import com.twl.meeboss.core.network.log.TWLLog.i4Net
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okio.Buffer
import org.json.JSONObject
import java.io.IOException
import java.nio.charset.StandardCharsets
import kotlin.time.measureTime

/**
 * Created by qinsanjie on 2022/12/21.
 */
class DebugLoggingInterceptor : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val printContent = HashMap<String, Any?>()
        val requestParams = HashMap<String, Any?>()
        val request: Request = chain.request()
        val parse = Uri.parse(request.url.toString())
        val url = request.method + "  " + parse.authority + parse.path
        //因为glide也用okhttp,所以要过滤掉图片
        if (!isApiHttpRequest(url)) {
            return chain.proceed(request)
        }

        for (i in 0 until request.headers.size) { //记录Header
            printContent[request.headers.name(i)] = request.headers.value(i)
        }
        val requestBody = request.body
        if (request.method == "GET") {
            for (key in parse.getQueryParameterNames()) {
                requestParams[key] = parse.getQueryParameter(key)
            }
        } else {
            if (requestBody != null) {
                val mediaType = requestBody.contentType()
                if (mediaType?.type != null && mediaType.type.contains("multipart")) {
                    printContent["body"] = "Body is multipart" //文件
                } else {
                    if (requestBody is FormBody) {
                        for (i in 0 until requestBody.size) {
                            requestParams[requestBody.encodedName(i)] = requestBody.value(i)
                        }
                    } else if (requestBody.contentType() != null && "application/json" == requestBody.contentType().toString()) {
                        try {
                            val buffer = Buffer()
                            requestBody.writeTo(buffer)
                            val body = Uri.decode(buffer.readUtf8())
                            val `object` = JSONObject(body)
                            `object`.keys().forEachRemaining { key: String ->
                                try {
                                    requestParams[key] = `object`[key]
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
        val response: Response = try {
            chain.proceed(request)
        } catch (e: Exception) {
            throw e
        }
        val responseBody = response.body
        var result = ""
        if (responseBody != null) {
            val source = responseBody.source()
            source.request(Long.MAX_VALUE) // Buffer the entire body.
            val buffer2 = source.buffer()
            val utf8 = StandardCharsets.UTF_8
            result = buffer2.clone().readString(utf8)
        }
        requestParams["sp"] = requestParams.remove("sp") ////把sp移动到尾部
        i4Net(url, response.headers["m-trace"], printContent.toJson(), requestParams.toJson(), result, "http", false, true)

        return response
    }

    private fun isApiHttpRequest(url: String?): Boolean {
        return (url != null && !url.endsWith(".png") && !url.endsWith(".PNG")
                && !url.endsWith(".jpg")
                && !url.endsWith(".jpeg")
                && !url.endsWith(".JPG")
                && !url.endsWith(".JPEG")
                && !url.endsWith(".webp")
                && !url.endsWith(".WEBP"))
    }
}
