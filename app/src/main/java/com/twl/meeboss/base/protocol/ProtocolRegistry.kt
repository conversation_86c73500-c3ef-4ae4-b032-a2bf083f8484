package com.twl.meeboss.base.protocol

import com.twl.meeboss.base.constants.ProtocolConstants
import com.twl.meeboss.base.protocol.action.BossCompanyPageAction
import com.twl.meeboss.base.protocol.action.BossEditJobAction
import com.twl.meeboss.base.protocol.action.BossGreetingSettingAction
import com.twl.meeboss.base.protocol.action.BossMyCandidateAction
import com.twl.meeboss.base.protocol.action.BossPersonInfoAction
import com.twl.meeboss.base.protocol.action.ChatDetailAction
import com.twl.meeboss.base.protocol.action.BossCompanyProfileAction
import com.twl.meeboss.base.protocol.action.BossTabAction
import com.twl.meeboss.base.protocol.action.GeekAddSkillAction
import com.twl.meeboss.base.protocol.action.GeekBeginnerAddEduExpAction
import com.twl.meeboss.base.protocol.action.GeekBeginnerAddNameAction
import com.twl.meeboss.base.protocol.action.GeekBeginnerAddWorkExpAction
import com.twl.meeboss.base.protocol.action.GeekBeginnerGuideAction
import com.twl.meeboss.base.protocol.action.GeekCompanyProfileAction
import com.twl.meeboss.base.protocol.action.GeekEditWorkExperienceAction
import com.twl.meeboss.base.protocol.action.GeekGreetingSettingAction
import com.twl.meeboss.base.protocol.action.GeekImprovementSuggestionAction
import com.twl.meeboss.base.protocol.action.GeekMyJobsAction
import com.twl.meeboss.base.protocol.action.GeekPersonAction
import com.twl.meeboss.base.protocol.action.GeekProfileAction
import com.twl.meeboss.base.protocol.action.H5JobDetailAction
import com.twl.meeboss.base.protocol.action.IProtocolAction
import com.twl.meeboss.base.protocol.action.JobSeekerTabAction
import com.twl.meeboss.base.protocol.action.MainTabAction
import com.twl.meeboss.base.protocol.action.SystemNotificationAction
import com.twl.meeboss.base.protocol.action.WebProtocolAction

/**
 * 应用内协议注册收敛入口，主要用于端侧内部页面跳转以及H5跳转端侧页面
 */
class ProtocolRegistry {
    private val actionMap: MutableMap<String, IProtocolAction> = mutableMapOf()

    fun getAction(key: String): IProtocolAction? {
        return actionMap[key]
    }

    fun registerAction() {
        //跳转首页tab
        actionMap[ProtocolConstants.TYPE_MAIN_TAB] = MainTabAction()
        //B开聊设置
        actionMap[ProtocolConstants.TYPE_B_GREETING_SETTING] = BossGreetingSettingAction()
        //C开聊设置
        actionMap[ProtocolConstants.TYPE_C_GREETING_SETTING] = GeekGreetingSettingAction()
        //B我的候选人
        actionMap[ProtocolConstants.ACTION_B_MY_CANDIDATE] = BossMyCandidateAction()
        //C我的职位
        actionMap[ProtocolConstants.ACTION_C_MY_JOBS] = GeekMyJobsAction()
        //系统通知页面
        actionMap[ProtocolConstants.SYSTEM_NOTIFICATION] = SystemNotificationAction()
        //C端个人信息编辑
        actionMap[ProtocolConstants.GEEK_PERSON] = GeekPersonAction()
        //C端个人信息编辑
        actionMap[ProtocolConstants.GEEK_PROFILE] = GeekProfileAction()
        //B端公司详情
        actionMap[ProtocolConstants.BOSS_COMPANY_PAGE] = BossCompanyPageAction()
        //B端职位编辑页面
        actionMap[ProtocolConstants.BOSS_EDIT_JOB] = BossEditJobAction()
        //C新手引导流程
        actionMap[ProtocolConstants.GEEK_BEGINNER_GUIDE] = GeekBeginnerGuideAction()
        //C新手引导流程-添加工作经验
        actionMap[ProtocolConstants.GEEK_BEGINNER_GUIDE_ADD_WORK_EXP] = GeekBeginnerAddWorkExpAction()
        //C新手引导流程-添加教育经历
        actionMap[ProtocolConstants.GEEK_BEGINNER_GUIDE_ADD_EDU_EXP] = GeekBeginnerAddEduExpAction()
        //C新手引导流程-添加姓名
        actionMap[ProtocolConstants.GEEK_BEGINNER_GUIDE_ADD_NAME] = GeekBeginnerAddNameAction()
        actionMap[ProtocolConstants.GEEK_IMPROVEMENT_SKILL] = GeekAddSkillAction()
        actionMap[ProtocolConstants.GEEK_IMPROVE_WORK_EXP] = GeekEditWorkExperienceAction()
        actionMap[ProtocolConstants.GEEK_IMPROVEMENT_SUGGESTION] = GeekImprovementSuggestionAction()
        actionMap[ProtocolConstants.CHAT_DETAIL] = ChatDetailAction()
        actionMap[ProtocolConstants.BOSS_PERSON_INFO] = BossPersonInfoAction()
        actionMap[ProtocolConstants.GEEK_JOB_DETAIL] = H5JobDetailAction()
        actionMap[ProtocolConstants.BOSS_COMPANY_PROFILE] = BossCompanyProfileAction()
        actionMap[ProtocolConstants.GEEK_COMPANY_PROFILE] = GeekCompanyProfileAction()
        actionMap[ProtocolConstants.BOSS_TAB] = BossTabAction()
        actionMap[ProtocolConstants.JOB_SEEKER_TAB] = JobSeekerTabAction()
        actionMap[ProtocolConstants.WEB_VIEW] = WebProtocolAction()
    }
}