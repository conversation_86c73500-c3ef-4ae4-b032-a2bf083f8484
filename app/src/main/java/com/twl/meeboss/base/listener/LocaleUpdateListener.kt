package com.twl.meeboss.base.listener

import com.twl.meeboss.base.config.locale.ILocaleChangedListener
import com.twl.meeboss.base.config.locale.LocaleConfig
import com.twl.meeboss.base.config.locale.LocaleTextConfigManager
import com.twl.meeboss.base.point.PointHelper
import com.twl.meeboss.common.utils.DeviceExtUtils
import com.twl.meeboss.core.network.HttpCore
import com.twl.meeboss.core.network.config.HttpConfigManager

class LocaleUpdateListener: ILocaleChangedListener {


    override fun onLocaleChanged(config: LocaleConfig, isInit: Boolean, countryChanged: Boolean) {
        DeviceExtUtils.updateLanguageString(config.appLanguage)
        DeviceExtUtils.updateCountryString(config.appCountry)
        HttpConfigManager.setNetEnvironment(config.environment)
        if(!isInit){
            if(countryChanged){
                HttpCore.refreshRetrofit()
                PointHelper.reset()
            }
            LocaleTextConfigManager.onLocaleChanged()
        }
    }
}