package com.twl.meeboss.base

import android.app.Application
import android.content.Context
import android.content.res.Configuration
import com.blankj.utilcode.util.AppUtils
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.BuildConfig
import com.twl.meeboss.base.apm.ActivityThreadHook
import com.twl.meeboss.base.config.language.LanguageUtil
import com.twl.meeboss.base.config.locale.LocalManager
import com.twl.meeboss.base.initializer.CommonInitializer
import com.twl.meeboss.base.initializer.IInitializer
import com.twl.meeboss.base.initializer.impl.AfterLoginInitializer
import com.twl.meeboss.base.initializer.impl.ApmInitializer
import com.twl.meeboss.base.initializer.impl.AppConfigInitializer
import com.twl.meeboss.base.initializer.impl.AppStartReportInitializer
import com.twl.meeboss.base.initializer.impl.AppsFlyerInitializer
import com.twl.meeboss.base.initializer.impl.ComposeHookInitializer
import com.twl.meeboss.base.initializer.impl.HostConfigInitializer
import com.twl.meeboss.base.initializer.impl.LocalHtmlInitializer
import com.twl.meeboss.base.initializer.impl.RouterInitializer
import com.twl.meeboss.base.initializer.impl.SentryInitializer
import com.twl.meeboss.base.initializer.impl.TLogInitializer
import com.twl.meeboss.base.listener.LocaleUpdateListener
import com.twl.meeboss.common.base.AppConfig
import com.twl.meeboss.common.utils.AppExtUtils
import com.twl.meeboss.common.utils.ProcessHelper
import com.twl.meeboss.debugger.ChuckerInitializer
import com.twl.meeboss.debugger.XDebugger
import com.twl.meeboss.debugger.autotest.localserver.ATLocalServerInitializer
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.initializer.impl.UrlListInitializer
import com.twl.meeboss.common.provider.UserProvider
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class App : Application() {

    override fun attachBaseContext(base: Context?) {
        base?.run {
            ChuckerInitializer.init(base)
            LocalManager.addListeners(LocaleUpdateListener())
            LocalManager.init(base, AppConfig(BuildConfig.DEBUG, BuildConfig.environment))
            super.attachBaseContext(LanguageUtil.attachBaseContext(base))
        }
        ProcessHelper.setContext(this, packageName)
        try {
            ActivityThreadHook.hookActivityThread()
        } catch (throwable: Throwable) {
            TLog.error("app", throwable, "hookActivityThread error.")
        }
        AppExtUtils.appStart()
    }

    override fun onCreate() {
        super.onCreate()
        CommonInitializer.init(
            AppConfig(
                BuildConfig.DEBUG,
                BuildConfig.environment
            ),
            initLit = getInitList(),
            bzInitLit = getBzInitList(),
            withAccountInitLit = getAfterAccountCreateInitList()
        )

        // 检查版本和地区，必要时执行退出登录
        checkVersionAndLocale()
    }

    /**
     * 检查应用版本和区域设置
     * 如果版本大于等于1.07且用户区域是德国，则自动退出登录
     */
    private fun checkVersionAndLocale() {
        try {
            val versionNumber = AppUtils.getAppVersionCode()

            // 检查版本号是否大于等于1.07
            if (LocalManager.isGermany() && versionNumber >= 107000) {
                TLog.info("App", "Version $versionNumber detected with German locale. Reset to US locale and performing auto logout.")

                // 重置区域设置为美国和英语
                resetLocaleToUS()

                // 执行退出登录操作
                if (UserProvider.isLogin()) {
                    AccountManager.logout()
                }
            }
        } catch (e: Exception) {
            TLog.error("App", e, "Error checking version and locale")
        }
    }

    /**
     * 将区域设置重置为美国和英语
     * 同时更新SharedPreferences中的值
     */
    private fun resetLocaleToUS() {
        try {
            // 获取美国国家代码和英语语言代码
            val usCountry = java.util.Locale.US.country
            val enLanguage = java.util.Locale.US.language

            // 使用LocalManager的方法更新区域设置
            applicationContext?.let { context ->
                // 使用LocalManager.resetEnvironment方法，这会直接修改SharedPreferences中的值
                // 同时保持环境设置不变
                LocalManager.resetEnvironment(context, usCountry, enLanguage)

                // 再调用justUpdateLanguage方法更新内存中的配置
                // 这样即使不重启应用，当前的区域设置也会被更新
                LocalManager.justUpdateLanguage(enLanguage, usCountry, context)

                TLog.info("App", "Successfully reset locale settings to US/English")
            }
        } catch (e: Exception) {
            TLog.error("App", e, "Error resetting locale to US")
        }
    }

    /**
     * 底层模块的初始化
     */
    private fun getInitList(): List<IInitializer> {
        return listOf(
            XDebugger.getIInitializer(),
            HostConfigInitializer(), //网络环境配置初始化
            SentryInitializer(), //Sentry库初始化(依赖网络环境初始化)
            ApmInitializer(),
            TLogInitializer(),
            ComposeHookInitializer(),
            RouterInitializer(), //路由库初始化
            AppsFlyerInitializer(), //AppsFlyer初始化
            AppStartReportInitializer(), //App启动埋点
            ATLocalServerInitializer() // 自动化测试本地服务初始化
        )
    }

    /**
     * 上层模块的初始化
     */
    private fun getBzInitList(): List<IInitializer> {
        return listOf(
            LocalHtmlInitializer(),
            // H5页面url、协议相关配置信息初始化
            UrlListInitializer()
        )
    }

    /**
     * 在获取了用户信息以后的初始化
     * 用户信息会对这类初始化有影响
     */
    private fun getAfterAccountCreateInitList(): List<IInitializer> {
        return listOf(
            AfterLoginInitializer(),
            AppConfigInitializer(),
        )
    }
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        LanguageUtil.attachBaseContext(this)
    }
}