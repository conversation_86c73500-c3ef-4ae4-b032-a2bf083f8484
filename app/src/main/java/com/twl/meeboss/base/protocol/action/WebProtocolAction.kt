package com.twl.meeboss.base.protocol.action

import android.content.Context
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.URLUtils
import com.twl.meeboss.webview.activity.WebViewActivity
import com.twl.meeboss.webview.ktx.appendParamToWebUrl
import java.net.URLDecoder

private const val TAG = "WebProtocolAction"

class WebProtocolAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        startWebViewActivity(context, params["url"], params)
    }
}

fun startWebViewActivity(context: Context, url: String?, params: Map<String, String>) {
    if (url.isNullOrBlank()) {
        XLog.error(TAG, "startWebViewActivity, url is empty")
        return
    }
    val decodedUrl = URLDecoder.decode(url, "utf-8")
    val finalUrl = decodedUrl.appendParamToWebUrl()
    XLog.info(TAG, "startWebViewActivity, finalUrl is $finalUrl, params is $params")
    val isBusinessParams = params[URLUtils.KEY_WEB_VIEW_APPEND_BUSINESS_PARAMS]?.toBoolean() ?: false
    if (isBusinessParams) {
        params as? HashMap<String, String> ?: run {
            require(true) { "${URLUtils.KEY_WEB_VIEW_APPEND_BUSINESS_PARAMS}, params must HashMap<String, String>" }
            return
        }
        if (params.containsKey(URLUtils.KEY_WEB_VIEW_APPEND_BUSINESS_PARAMS)) {
            params.remove(URLUtils.KEY_WEB_VIEW_APPEND_BUSINESS_PARAMS)
        }
        WebViewActivity.intentWithBusinessParams(context, finalUrl, params, true)
    } else {
        WebViewActivity.intent(context, finalUrl, params, true)
    }
}