package com.twl.meeboss.base.protocol

import android.text.TextUtils
import com.blankj.utilcode.util.ActivityUtils
import com.techwolf.lib.tlog.TLog
import com.twl.meeboss.base.constants.ProtocolConstants
import com.twl.meeboss.base.protocol.action.startWebViewActivity
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.utils.URLUtils
import com.twl.meeboss.webview.ktx.appendParamToWebUrl

class ProtocolManager {

    companion object {
        private val TAG = ProtocolManager::class.java.simpleName

        @JvmStatic
        fun parseProtocol(protocol: String?) {
            if (!protocol.isNullOrBlank()) {
                handleProtocol(protocol, null)
            }
        }

        @JvmStatic
        fun parseProtocol(protocol: String?, map: HashMap<String, String>) {
            if (!protocol.isNullOrBlank()) {
                handleProtocol(protocol, map)
            }
        }

        @JvmStatic
        fun userProtocolCoreByLocal(type: String?,params: Map<String, String>) {
            if (!type.isNullOrBlank()) {
                startProtocolCore(type,params)
            }
        }


        private fun handleProtocol(protocol: String?, map: HashMap<String, String>?) {
            XLog.info(TAG, "handleProtocol:${protocol}")
            if (protocol.isNullOrBlank()) return
            try {
                if (protocol.startsWith(ProtocolConstants.MEE_BOSS_URL_PREFIX,true)) {
                    val params = URLUtils.parseUrlParam(protocol)
                    if (params != null && !TextUtils.isEmpty(params["type"])) {
                        val type = params["type"]
                        startProtocolCore(type,params,protocol)
                    }
                } else if (protocol.startsWith("http", true)) {
                    val formatUrl = protocol.appendParamToWebUrl()
                    XLog.info(TAG, "formatUrl:${formatUrl}, params:$map")
                    startWebViewActivity(ActivityUtils.getTopActivity(), formatUrl, map ?: hashMapOf())
                }
            } catch (e: Exception) {
                TLog.error(TAG, "Push click error : %s", e.toString());
            }

        }

        private fun startProtocolCore(type: String?, params: Map<String, String>, originUrl:String? = null){
            ProtocolActionFactory.getAction(type,originUrl)
                ?.startAction(ActivityUtils.getTopActivity(), params)
        }
    }
}