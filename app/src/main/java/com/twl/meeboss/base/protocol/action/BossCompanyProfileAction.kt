package com.twl.meeboss.base.protocol.action

import android.content.Context
import com.twl.meeboss.boss.export.BossPageRouter
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.provider.UserProvider

class BossCompanyProfileAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        if (!UserProvider.isBoss()) {
            XLog.error("BossCompanyProfileAction", "identity error, currentIdentity:${UserProvider.getIdentify()}")
            return
        }
        BossPageRouter.jumpToBossCompanyPreviewActivity(context)
    }
}