package com.twl.meeboss.base.protocol.action

import android.content.Context
import com.twl.meeboss.base.account.AccountManager
import com.twl.meeboss.base.constants.UserConstants
import com.twl.meeboss.common.log.XLog
import com.twl.meeboss.common.provider.UserProvider
import com.twl.meeboss.geek.export.GeekPageRouter

private const val TAG = "GeekCompanyProfileAction"

class GeekCompanyProfileAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        if (!UserProvider.isLogin()) {
            XLog.error(TAG, "not login")
            return
        }
        if (AccountManager.getFirstCompleteStatus() != UserConstants.COMPLETE_STATUS_ALREADY_COMPLETE) {
            XLog.error(TAG, "not complete")
            return
        }
        if (!UserProvider.isGeek()) {
            XLog.error(TAG, "not geek")
            return
        }

        val companyId = params["companyId"]?.takeIf { it.isNotEmpty() } ?: run {
            XLog.error(TAG, "参数companyId不能为空")
            return
        }

        // 默认是0
        val selectTabIndex = params["selectTabIndex"]?.toIntOrNull() ?: 0
        GeekPageRouter.jumpToGeekCompanyDetailActivity(context, companyId, selectTabIndex)
    }
}